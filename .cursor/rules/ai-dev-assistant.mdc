---
description: 
globs: 
alwaysApply: false
---
# AI 智能开发助手规则指南

## 技术栈
- 本项目使用的技术栈包括：
  - TypeScript
  - Node.js
  - Web APIs
  - Vite
  - Vue.js
  - Vue Router
  - Pinia
  - VueUse
  - Radix Vue
  - Tailwind CSS
  - Hono
  - Drizzle ORM

## 代码风格与结构
- 编写简洁、可维护且技术上准确的代码，提供相关示例。
- 使用函数式、声明式编程模式。
- 优先使用迭代和模块化，避免代码重复。
- 使用描述性变量名，包含辅助动词（例如：isLoading, hasError）。
- 系统化组织文件：每个文件仅包含相关内容，如导出组件、子组件、助手函数、静态内容和类型。
- 目录使用小写加短横线（例如：components/auth-wizard）。
- 函数优先使用命名导出。
- 纯函数使用"function"关键字以利用提升和清晰性。
- 函数参数优先使用接收对象、返回对象（RORO）模式。
- 简单条件语句优先使用单行语法（例如：if (condition) doSomething()）。
- 所有代码使用TypeScript。优先使用接口而非类型。避免使用枚举；使用映射以获得更好的类型安全性和灵活性。

## 错误处理与验证
- 在函数开始时处理错误和边界情况。
- 使用早期返回以避免深层嵌套的if语句。
- 使用保护子句以早期处理前提条件和无效状态。
- 避免不必要的else语句；使用if-return模式。
- 实现适当的错误日志记录和用户友好的错误消息。
- 考虑使用自定义错误类型或错误工厂以实现一致的错误处理。

## VueJS
- 使用带有TypeScript接口的函数式组件。
- 始终使用Vue Composition API脚本设置风格。
- 在适用的情况下利用VueUse函数以增强反应性和性能。
- 方法优先使用"function"关键字，但计算属性使用const和箭头函数。
- 创建双向绑定时优先使用`defineModel`宏。
- 使用简洁的语法定义事件（例如：`change: [id: number]`）。

## UI与样式
- 使用Radix Vue和Tailwind CSS进行组件和样式设计。
- 使用Tailwind CSS实现响应式设计；采用移动优先的方法。

## 性能优化
- 将异步组件包装在Suspense中，并提供后备UI。
- 对非关键组件使用动态加载。
- 优化图像：使用WebP格式，包含尺寸数据，实现延迟加载。
- 在Vite构建过程中实现优化的分块策略，如代码拆分，以生成更小的包大小。

## 关键约定
- 使用Lighthouse或WebPageTest等工具优化Web Vitals（LCP, CLS, FID）。


# mics-vue 项目版本信息
- Vue: 3.5.14
- Vite: 6.3.5
- @vitejs/plugin-vue: 5.2.4
- vite-plugin-vue-devtools: 7.7.6
