---
description:
globs:
alwaysApply: false
---
# 前端架构规则

## 角色定义

这是一套前端架构设计规则，用于指导开发者如何构建具有良好可维护性的Vue.js应用程序。核心目标是：

- 实现页面(视图)与数据(逻辑)的清晰分离
- 提供真实数据与mock数据的无缝切换能力
- 保持代码的可测试性和可维护性
- 确保组件与数据层之间的松耦合

应用场景：
- 前端项目初始架构设计
- 现有项目的重构与优化
- 新功能模块的开发

## 上下文信息

在现代前端开发中，特别是使用Vue.js框架时，清晰的架构设计至关重要。本规则基于以下背景：

- 使用Vue 3 + TypeScript开发的企业级应用
- 需要支持开发环境和生产环境之间的无缝切换
- 前后端分离开发，需要在前端独立开发阶段使用mock数据
- 采用Element Plus作为UI组件库

## 架构设计规范

### 目录结构

```
src/
├── api/                # API服务层
│   └── [module]/       # 按模块组织的API服务
│       ├── index.ts    # 服务工厂与导出
│       ├── interface.ts # 接口定义
│       ├── mock.ts     # Mock数据实现
│       ├── real.ts     # 真实API实现
│       └── types.ts    # 类型定义
├── components/         # 全局公共组件
├── views/              # 页面视图
│   └── [module]/       # 按模块组织的页面
│       ├── index.vue   # 模块入口页面
│       └── components/ # 模块特定组件
├── utils/              # 工具函数
├── config/             # 配置文件
│   └── env.ts          # 环境配置
└── mock/               # 全局Mock数据(已废弃，使用模块内mock.ts)
```

### 输入输出格式

#### 1. API接口定义 (`interface.ts`)

```typescript
// 服务接口定义
export interface IExampleService {
  // 方法签名定义
  getList(params: IQueryParams): Promise<IListResponse>;
  getDetail(id: string): Promise<IDetailData>;
  create(data: ICreateRequest): Promise<IDetailData>;
  update(data: IUpdateRequest): Promise<IDetailData>;
  delete(id: string): Promise<void>;
}
```

#### 2. 数据实现 (`mock.ts` 和 `real.ts`)

```typescript
// 实现接口的具体类
export class MockExampleService implements IExampleService {
  // 实现所有接口方法
  async getList(params: IQueryParams): Promise<IListResponse> {
    // mock实现
  }
}

export class RealExampleService implements IExampleService {
  // 实现所有接口方法
  async getList(params: IQueryParams): Promise<IListResponse> {
    // 真实API调用
  }
}
```

#### 3. 服务工厂 (`index.ts`)

```typescript
// 工厂模式创建服务实例
const exampleService: IExampleService = config.useMock
  ? new MockExampleService()
  : new RealExampleService();

// 导出方法而非实例
export const getList = (params: any) => exampleService.getList(params);
```

#### 4. 视图组件 (`*.vue`)

```vue
<script setup lang="ts">
// 只导入API方法和类型，不依赖具体实现
import { getList, getDetail } from '@/api/example';
import type { IExampleData } from '@/api/example/types';

// 组件逻辑...
</script>

<template>
  <!-- 视图模板 -->
</template>
```

## 核心规则

### 1. 页面与数据分离

- **单一职责原则**: 页面组件负责渲染和用户交互，API服务负责数据获取和处理
- **依赖注入**: 组件通过导入API方法获取数据，不直接依赖具体实现
- **状态管理**: 复杂状态使用Pinia或Vuex管理，简单状态使用组件内ref/reactive

### 2. 真实数据与Mock数据分离

- **接口优先设计**: 先定义接口，再分别实现真实和mock版本
- **工厂模式**: 使用工厂方法根据配置决定使用哪个实现
- **统一导出**: 只导出API方法，不暴露具体实现细节
- **环境配置**: 使用环境变量控制是否使用mock数据

### 3. 类型定义与复用

- **集中定义类型**: 所有类型定义放在types.ts文件中
- **类型共享**: 真实和mock实现共享同一套类型定义
- **接口继承**: 使用接口继承建立类型间的关系
- **严格类型检查**: 启用TypeScript的严格模式

## 示例与用例

### 商户管理模块示例

1. **接口定义** (`merchant/interface.ts`):

```typescript
export interface IMerchantService {
  getMerchantList(params: IMerchantQueryParams): Promise<IMerchantListResponse>;
  getMerchantDetail(id: string): Promise<IMerchantDetail>;
  // 其他方法...
}
```

2. **Mock实现** (`merchant/mock.ts`):

```typescript
export class MockMerchantService implements IMerchantService {
  private mockData: IMerchantDetail[] = [
    // 硬编码的mock数据
  ];

  async getMerchantList(params: IMerchantQueryParams): Promise<IMerchantListResponse> {
    // 模拟分页逻辑
    const { page = 1, pageSize = 10 } = params;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const list = this.mockData.slice(start, end);
    
    return {
      list,
      total: this.mockData.length,
      page,
      pageSize
    };
  }
  
  // 其他方法实现...
}
```

3. **真实API实现** (`merchant/real.ts`):

```typescript
export class RealMerchantService implements IMerchantService {
  async getMerchantList(params: IMerchantQueryParams): Promise<IMerchantListResponse> {
    // 实际API调用
    return get<IMerchantListResponse>('/api/merchant/list', params);
  }
  
  // 其他方法实现...
}
```

4. **服务工厂** (`merchant/index.ts`):

```typescript
// 根据配置创建服务实例
const merchantService: IMerchantService = apiConfig.useMock
  ? new MockMerchantService()
  : new RealMerchantService();

// 导出服务方法
export const getMerchantList = (params: any) => merchantService.getMerchantList(params);
export const getMerchantDetail = (id: string) => merchantService.getMerchantDetail(id);
// 其他方法导出...
```

5. **视图组件使用** (`MerchantList.vue`):

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getMerchantList } from '@/api/merchant';
import type { IMerchant } from '@/api/merchant/types';

// 数据状态
const tableData = ref<IMerchant[]>([]);
const loading = ref(false);

// 获取数据方法
const fetchList = async () => {
  loading.value = true;
  try {
    const { list, total } = await getMerchantList({ page: 1, pageSize: 10 });
    tableData.value = list;
  } finally {
    loading.value = false;
  }
};

// 初始化获取数据
onMounted(() => {
  fetchList();
});
</script>

<template>
  <el-table :data="tableData" v-loading="loading">
    <!-- 表格列定义 -->
  </el-table>
</template>
```

## 常见错误与处理

### 1. 接口不匹配

**问题**: Mock数据结构与API响应结构不匹配

**解决方案**:
- 确保类型定义与后端API协议一致
- 统一`types.ts`中的类型定义
- 在mock实现中严格遵循接口定义返回的数据结构

```typescript
// 错误示例
// types.ts中定义返回 { items: IMerchant[] }
// mock.ts中返回 { list: IMerchant[] }

// 正确做法: 保持一致的命名和结构
```

### 2. 上下文丢失

**问题**: 使用解构导出服务方法导致this上下文丢失

**解决方案**:
- 使用箭头函数包装方法调用
- 不要直接解构服务实例的方法

```typescript
// 错误示例
export const { getList } = service; // this上下文丢失

// 正确做法
export const getList = (params) => service.getList(params); // 保留上下文
```

### 3. 忘记处理加载状态

**问题**: 组件中未处理API调用的加载状态

**解决方案**:
- 始终使用loading状态控制UI加载指示器
- 使用try/finally确保无论成功失败都重置loading状态

```typescript
// 正确示例
const fetchData = async () => {
  loading.value = true;
  try {
    // API调用
  } catch (error) {
    // 错误处理
  } finally {
    loading.value = false; // 始终执行
  }
};
```

### 4. 直接修改返回数据

**问题**: 直接修改API返回的数据可能导致问题

**解决方案**:
- 使用解构或展开运算符创建新对象
- 使用immutable数据处理方式

```typescript
// 错误示例
const data = await getDetail(id);
data.name = '新名称'; // 直接修改

// 正确做法
const data = await getDetail(id);
const updatedData = { ...data, name: '新名称' }; // 创建新对象
```

## 最佳实践总结

1. **接口优先设计**: 先定义清晰的接口，再实现具体逻辑
2. **依赖倒置原则**: 高层模块不应依赖低层模块，都应依赖抽象
3. **单一职责原则**: 每个类或文件只负责一个功能领域
4. **关注点分离**: 将页面渲染、数据获取、业务逻辑分开
5. **类型安全**: 使用TypeScript类型系统确保类型安全
6. **一致性**: 保持命名和结构的一致性
7. **可测试性**: 设计便于单元测试的代码结构

遵循这些规则将帮助你构建可维护、可扩展的前端应用程序，同时实现页面与数据分离，以及真实数据与mock数据的灵活切换。
