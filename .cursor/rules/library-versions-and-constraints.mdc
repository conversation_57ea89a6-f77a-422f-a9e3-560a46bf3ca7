---
description: 
globs: 
alwaysApply: false
---
# 库版本和功能约束规则指南

## TypeScript
- 使用最新的稳定版本，确保与Node.js和Vue.js的兼容性。

## Node.js
- 使用LTS版本以确保稳定性和长期支持。

## Web APIs
- 确保使用现代浏览器支持的Web APIs，避免使用已弃用的功能。

## Vite
- 使用最新的稳定版本，确保与Vue.js和其他插件的兼容性。

## Vue.js
- 使用最新的稳定版本，确保与Vue Router和Pinia的兼容性。

## Vue Router
- 使用与Vue.js兼容的版本，确保路由功能的稳定性。

## Pinia
- 使用与Vue.js兼容的版本，确保状态管理的稳定性。

## VueUse
- 使用最新版本以利用最新的功能和性能优化。

## Radix Vue
- 使用最新版本以确保与Vue.js和Tailwind CSS的兼容性。

## Tailwind CSS
- 使用最新版本以确保与Radix Vue的兼容性，并支持最新的CSS功能。

## Hono
- 使用最新版本以确保与Node.js的兼容性。

## Drizzle ORM
- 使用最新版本以确保与Node.js和数据库的兼容性。

## 版本管理
- 定期检查和更新库版本以利用最新的功能和安全修复。
- 使用语义化版本控制（SemVer）以确保版本更新的稳定性。

## 功能约束
- 确保所有库的功能与项目需求一致，避免使用不必要的功能以减少复杂性。
- 定期评估库的功能和性能，确保其满足项目的性能和功能需求。
