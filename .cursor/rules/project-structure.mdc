---
description: 
globs: 
alwaysApply: false
---
# 工程结构规则指南

## 项目源码
- 项目源码位于`mics-vue`目录中，包含所有的前端代码实现。

### 源码目录结构
```
mics-vue/
├── public/                 # 静态资源文件夹
├── src/                    # 源代码主目录
│   ├── api/                # API接口层
│   │   ├── merchant/       # 商户相关API
│   │   ├── channel/        # 渠道相关API
│   │   └── menu/           # 菜单相关API
│   ├── assets/             # 静态资源文件
│   │   └── images/         # 图片资源
│   ├── components/         # 组件目录
│   │   ├── base/           # 基础UI组件
│   │   ├── business/       # 业务组件
│   │   └── icons/          # 图标组件
│   ├── composables/        # 组合式函数
│   ├── config/             # 配置文件
│   ├── constants/          # 常量定义
│   ├── directives/         # 自定义指令
│   ├── hooks/              # 自定义钩子函数
│   ├── mock/               # 模拟数据
│   ├── plugins/            # 插件配置
│   ├── router/             # 路由配置
│   │   ├── modules/        # 路由模块
│   │   ├── index.ts        # 路由入口
│   │   └── routes.ts       # 路由定义
│   ├── stores/             # 状态管理
│   │   ├── modules/        # 状态模块
│   │   ├── counter.ts      # 示例状态
│   │   └── menu.ts         # 菜单状态
│   ├── styles/             # 全局样式
│   ├── utils/              # 工具函数
│   │   ├── logger.ts       # 日志工具
│   │   ├── request.ts      # 请求工具
│   │   └── envValidator.ts # 环境变量验证
│   ├── views/              # 页面视图
│   │   ├── layouts/        # 布局组件
│   │   ├── dashboard/      # 仪表盘页面
│   │   ├── merchant/       # 商户管理页面
│   │   ├── channel/        # 渠道管理页面
│   │   ├── payment/        # 支付管理页面
│   │   └── system/         # 系统管理页面
│   ├── App.vue             # 根组件
│   └── main.ts             # 入口文件
├── env-templates/          # 环境变量模板
├── .env.development        # 开发环境配置
├── .prettierrc.json        # Prettier配置
├── .editorconfig           # 编辑器配置
├── eslint.config.ts        # ESLint配置
├── index.html              # HTML入口
├── package.json            # 项目依赖
├── postcss.config.js       # PostCSS配置
├── tailwind.config.js      # Tailwind配置
├── tsconfig.json           # TypeScript配置
├── tsconfig.app.json       # 应用TS配置
├── tsconfig.node.json      # Node TS配置
├── tsconfig.vitest.json    # 测试TS配置
├── vite.config.ts          # Vite配置
└── vitest.config.ts        # Vitest配置
```

## 工具类
mics-vue/src/utils 中已经实现了常用工具
mics-vue/
├── src/                    # 源代码主目录
│   ├── utils/              # 工具函数
│   │   ├── logger.ts       # 日志工具
│   │   ├── request.ts      # 请求工具
│   │   ├── dict.ts         # 数据字典工具
│   │   └── envValidator.ts # 环境变量验证


## 项目原型
- 项目原型位于`prototype`目录中，包含所有的设计原型和用户体验文档。

### 原型页面说明
```
prototype/
├── images/                     # 原型页面使用的图片资源
├── layout.html                 # 通用布局页面
├── layout_template.html        # 布局模板页面
├── dashboard.html              # 仪表盘页面
├── merchant.html               # 商户列表页面
├── merchant_detail.html        # 商户详情页面
├── merchant_edit.html          # 商户编辑页面
├── application.html            # 应用列表页面
├── application_detail.html     # 应用详情页面
├── application_edit.html       # 应用编辑页面
├── channel_account.html        # 渠道账户列表页面
├── channel_account_detail.html # 渠道账户详情页面
├── channel_account_edit.html   # 渠道账户编辑页面
├── channel_employee.html       # 渠道员工列表页面
├── channel_employee_edit.html  # 渠道员工编辑页面
├── channel_route_rules.html    # 渠道路由规则列表页面
├── channel_route_rule_edit.html# 渠道路由规则编辑页面
├── payment_method_list.html    # 支付方式列表页面
├── payment_method_detail.html  # 支付方式详情页面
├── payment_method_edit.html    # 支付方式编辑页面
├── payment_method_filter.html  # 支付方式过滤页面
├── payment_method_trend.html   # 支付方式趋势页面
├── payment_scene.html          # 支付场景列表页面
├── payment_scene_edit.html     # 支付场景编辑页面（旧版）
├── payment_scene_edit_new.html # 支付场景编辑页面（新版）
├── payment_scene_version.html  # 支付场景版本页面
├── payment_scene_preview.html  # 支付场景预览页面
├── condition_module.html       # 条件模块列表页面
├── condition_module_edit.html  # 条件模块编辑页面
├── condition_module_view.html  # 条件模块查看页面
├── condition_rule.html         # 条件规则列表页面
├── condition_rule_edit.html    # 条件规则编辑页面
├── condition_relation_designs.html # 条件关系设计页面
├── dict_management.html        # 字典管理页面
├── dict_category.html          # 字典分类页面
├── dict_item.html              # 字典项页面
├── role_management.html        # 角色管理页面
├── operation_log.html          # 操作日志页面
└── feature_flags.html          # 功能开关页面
```

## 任务模块
项目的任务计划位于`task`目录，包含了从HTML原型到Vue项目转换的全面计划：

```
task/
├── master_plan.md                     # 整体计划
├── 01_preparation_analysis.md         # 准备和分析
├── 02_component_structure.md          # 组件结构设计
├── 02_01_component_prototype_mapping.md # 组件与原型映射
├── 03_implementation.md               # 实施转换
├── 04_routing_state.md                # 路由与状态管理
├── 05_api_layer.md                    # API层实现
├── 06_optimization.md                 # 功能优化
└── 07_testing_deployment.md           # 测试与部署
```

## 项目技术栈
- 项目使用的技术栈包括：
  - TypeScript：使用最新的稳定版本，确保与Node.js和Vue.js的兼容性。
  - Node.js：使用LTS版本以确保稳定性和长期支持。
  - Web APIs：确保使用现代浏览器支持的Web APIs，避免使用已弃用的功能。
  - Vite：使用最新的稳定版本，确保与Vue.js和其他插件的兼容性。
  - Vue.js：使用最新的稳定版本，确保与Vue Router和Pinia的兼容性。
  - Vue Router：使用与Vue.js兼容的版本，确保路由功能的稳定性。
  - Pinia：使用与Vue.js兼容的版本，确保状态管理的稳定性。
  - VueUse：使用最新版本以利用最新的功能和性能优化。
  - Tailwind CSS：使用最新版本构建响应式UI，实现移动优先的设计理念。
  - Element Plus：用于提供丰富的UI组件，与Tailwind CSS结合使用。
  - Axios：用于处理HTTP请求，与后端API通信。

### API接口适配与Mock
- 真实API接口：通过`/api`前缀请求，由Vite代理转发到实际后端服务
- Mock数据：在开发阶段使用，便于前端独立开发
- 服务接口层：采用Strategy模式，根据配置选择使用真实API或Mock数据
- 结构和实现： [frontend-architecture-rules.mdc](mdc:.cursor/rules/frontend-architecture-rules.mdc)

### 状态管理与组件通信
- 使用Pinia进行全局状态管理
- 采用组合式API（Composition API）和script setup语法
- 通过Props、Emits和Provide/Inject进行组件间通信

### 核心功能模块
- 商户管理：商户信息维护、应用管理
- 渠道管理：渠道账户、渠道员工、路由规则
- 支付管理：支付方式、支付场景、支付趋势
- 系统管理：角色权限、数据字典、操作日志、功能开关

## 项目介绍
- 项目概述和详细模块说明请参考[项目介绍](mdc:项目介绍)。

## 文件格式
- 规则文件必须存储在`.cursor/rules`目录中。
- 文件扩展名必须为`.mdc`。
- 使用Markdown格式编写规则，包含Cursor特定扩展。
- 在规则中引用文件时，使用格式`[filename.ext](mdc:filename.ext)`，路径相对于工作区根目录。
