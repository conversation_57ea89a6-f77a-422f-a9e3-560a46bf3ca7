---
description: 
globs: 
alwaysApply: false
---
# 代理规则指南

## 规则选择机制
- 每次请求时，自动根据请求的描述和上下文选择合适的规则文件。
- 当前可用的规则文件包括：
  - [项目结构规则](mdc:project-structure.mdc)：用于了解项目的整体结构、源码位置或技术栈。
  - [库版本和功能约束规则](mdc:library-versions-and-constraints.mdc)：用于了解项目中使用的技术栈的版本和功能约束。
  - [AI智能开发助手规则](mdc:ai-dev-assistant.mdc)：用于遵循开发中的最佳实践和代码风格。
  - [结构化提示词规则生成指南](mdc:structured-prompt-rules.mdc)：用于生成和管理结构化提示词的规则。

## 添加新规则
- 要添加新的规则文件，请将其存储在`.cursor/rules`目录中，并确保文件扩展名为`.mdc`。
- 在本代理规则文件中添加新规则的引用，使用格式`[filename.ext](mdc:filename.ext)`，路径相对于工作区根目录。

## 使用说明
- 本代理规则文件将作为请求的入口，自动选择和应用最合适的规则文件以满足请求需求。
