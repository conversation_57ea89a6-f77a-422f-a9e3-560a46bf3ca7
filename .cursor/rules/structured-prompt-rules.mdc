---
description: 
globs: 
alwaysApply: false
---
# 结构化提示词规则生成指南

## 规则文件生成步骤

1. **角色定义**
   - 在规则文件中明确指定提示词的角色和功能。
   - 角色定义应包括任务目标和使用场景。

2. **上下文信息**
   - 提供提示词所需的背景和环境信息。
   - 确保上下文信息能够帮助理解任务背景。

3. **输入输出格式**
   - 定义提示词的输入和输出格式。
   - 确保格式定义清晰，便于后续处理。

4. **语法和规则**
   - 包含特定的语法和规则指导。
   - 规则可以包括条件判断、循环结构等。

5. **示例和用例**
   - 提供具体的示例和用例。
   - 帮助用户理解和应用提示词。

6. **错误处理**
   - 定义可能出现的错误及其处理方式。
   - 提高提示词的鲁棒性和可靠性。

## 文件格式

- 规则文件必须存储在`.cursor/rules`目录中。
- 文件扩展名必须为`.mdc`。
- 使用Markdown格式编写规则，包含Cursor特定扩展。
- 在规则中引用文件时，使用格式`[filename.ext](mdc:filename.ext)`，路径相对于工作区根目录。
