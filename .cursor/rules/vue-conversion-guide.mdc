---
description: 
globs: 
alwaysApply: false
---
# Vue 项目转换指南

## 项目环境设置
- 使用 Vue CLI 创建新的 Vue 项目。
- 安装必要的依赖，包括 Vue Router 和 Vuex。

## 组件化开发
- 将每个 HTML 页面转换为 Vue 组件。
- 提取公共部分（如头部、侧边栏、表格、表单等）为独立组件。

## 路由管理
- 使用 Vue Router 定义路由规则。
- 确保每个页面对应一个路由。

## 状态管理
- 使用 Vuex 管理全局状态。
- 定义模块化的状态管理方案。

## 交互逻辑迁移
- 将现有的 JavaScript 交互逻辑迁移到 Vue 的生命周期钩子和方法中。

## 文件格式
- 规则文件必须存储在`.cursor/rules`目录中。
- 文件扩展名必须为`.mdc`。
- 使用Markdown格式编写规则，包含Cursor特定扩展。
- 在规则中引用文件时，使用格式`[filename.ext](mdc:filename.ext)`，路径相对于工作区根目录。
