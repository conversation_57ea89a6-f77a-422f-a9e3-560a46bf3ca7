# 请求
```
curl --location --request GET "http://10.32.233.3:9032/merchant/queryList?merchantNo=M202307010001&uMerchantNo=&channelCode=&merchantType=NORMAL&status=ACTIVE&pageNo=1&pageSize=10"
```




# 响应
```
{
    "code": 0,
    "message": "",
    "data": {
        "total": 5,
        "data": [
            {
                "cid": "2fd5e47a-781e-4f34-b548-74666f45c52b",
                "merchantUniqueNumber": "merchant_1",
                "merchantNumber": "101",
                "channelCode": "channel_1",
                "merchantType": "normal",
                "status": "open",
                "channelAccountId": null,
                "channelAccountCid": null,
                "parentMerchantId": null,
                "parentMerchantCid": null,
                "comment": "这是一个商户号",
                "createTime": "2025-05-16 13:34",
                "updateTime": "2025-05-16 13:34",
                "usages": null,
                "apps": null,
                "channelCodeName": null,
                "merchantTypeName": null,
                "parentMerchantNumber": null
            },
            {
                "cid": "dc7880c3-688d-457e-8fcf-a99e1eb41eae",
                "merchantUniqueNumber": "merchant_2",
                "merchantNumber": "102",
                "channelCode": "channel_2",
                "merchantType": "special",
                "status": "open",
                "channelAccountId": null,
                "channelAccountCid": null,
                "parentMerchantId": 7,
                "parentMerchantCid": "2fd5e47a-781e-4f34-b548-74666f45c52b",
                "comment": "这是一个商户号2",
                "createTime": "2025-05-16 13:35",
                "updateTime": "2025-05-16 13:35",
                "usages": null,
                "apps": null,
                "channelCodeName": null,
                "merchantTypeName": null,
                "parentMerchantNumber": "101"
            },
            {
                "cid": "9aca96f4-53ff-4c5f-ab93-d32f95a366fd",
                "merchantUniqueNumber": "U202307010001",
                "merchantNumber": "M202307010001",
                "channelCode": "ALIPAY",
                "merchantType": "NORMAL",
                "status": "open",
                "channelAccountId": 25,
                "channelAccountCid": "850bd173-2fcf-46ea-b8b2-c9f6a770d93b",
                "parentMerchantId": null,
                "parentMerchantCid": null,
                "comment": null,
                "createTime": "2025-05-16 13:37",
                "updateTime": "2025-05-16 13:37",
                "usages": null,
                "apps": null,
                "channelCodeName": null,
                "merchantTypeName": null,
                "parentMerchantNumber": null
            },
            {
                "cid": "6744e8ec-89c0-4f2a-945b-a2a2b38d6130",
                "merchantUniqueNumber": "merchant_4",
                "merchantNumber": "101",
                "channelCode": "channel_4",
                "merchantType": "normal",
                "status": "open",
                "channelAccountId": null,
                "channelAccountCid": null,
                "parentMerchantId": null,
                "parentMerchantCid": null,
                "comment": "这是一个商户号",
                "createTime": "2025-05-16 13:40",
                "updateTime": "2025-05-16 13:40",
                "usages": [
                    {
                        "code": "01",
                        "value": "V01"
                    }
                ],
                "apps": null,
                "channelCodeName": null,
                "merchantTypeName": null,
                "parentMerchantNumber": null
            },
            {
                "cid": "d8a73dda-e8d5-40a8-896d-5f263464e60f",
                "merchantUniqueNumber": "U202307010002",
                "merchantNumber": "M202307010002",
                "channelCode": "weixin",
                "merchantType": "NORMAL",
                "status": "open",
                "channelAccountId": null,
                "channelAccountCid": null,
                "parentMerchantId": null,
                "parentMerchantCid": null,
                "comment": null,
                "createTime": "2025-05-16 14:15",
                "updateTime": "2025-05-16 14:38",
                "usages": null,
                "apps": null,
                "channelCodeName": null,
                "merchantTypeName": null,
                "parentMerchantNumber": null
            }
        ]
    }
}
```