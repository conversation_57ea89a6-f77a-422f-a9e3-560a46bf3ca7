# 商户号管理接口文档

本文档基于 `MerchantHandler` 类，提供商户号管理相关的接口说明。

## 基础信息

- 基础路径: `/merchant`

## 通用响应结构

所有接口返回统一的 `Result` 结构:

```json
{
  "code": 0,         // 状态码，0表示成功，其他值表示错误
  "message": "",     // 错误信息，成功时为空
  "data": {}         // 响应数据，根据接口不同而不同
}
```

错误码说明:
- `0`: 成功
- `104`: 服务器错误
- `102`: 参数错误
- `140001`: 内部错误

## 接口列表

### 1. 新增商户号

- **URL**: `/merchant/add`
- **方法**: POST
- **描述**: 创建新的商户号

#### 请求参数

请求体 (JSON):

```json
{
  "merchantUniqueNumber": "U202307010001",  // 商户唯一编号，必填
  "merchantNumber": "M202307010001",        // 商户号，必填
  "channelCode": "ALIPAY",                  // 渠道编码，必填
  "channelType": "MERCHANT",                // 渠道类型，必填
  "merchantType": "NORMAL",                 // 商户类型，必填
  "status": "open",                         // 状态，默认为"open"
  "channelAccountCid": "uuid-xxx",          // 关联渠道账号UUID
  "parentMerchantCid": "uuid-parent",       // 父级商户UUID
  "comment": "测试商户",                     // 备注说明
  "usages": [                               // 用途列表
    {
      "code": "PAYMENT",                    // 用途编码
      "value": "支付"                        // 用途说明
    }
  ],
  "apps": [                                 // 关联应用列表
    {
      "applicationManagementCid": "uuid-app", // 应用管理UUID，必填
      "status": "open"                        // 状态，默认为"open"
    }
  ]
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "cid": "uuid-xxx"  // 新创建的商户号UUID
  }
}
```

### 2. 修改商户号

- **URL**: `/merchant/modify`
- **方法**: POST
- **描述**: 修改现有商户号信息

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-xxx",                        // 商户UUID，必填
  "merchantUniqueNumber": "U202307010001",  // 商户唯一编号
  "merchantNumber": "M202307010001",        // 商户号
  "channelCode": "ALIPAY",                  // 渠道编码
  "channelType": "MERCHANT",                // 渠道类型
  "merchantType": "NORMAL",                 // 商户类型
  "status": "open",                         // 状态
  "channelAccountCid": "uuid-xxx",          // 关联渠道账号UUID
  "parentMerchantCid": "uuid-parent",       // 父级商户UUID
  "comment": "测试商户(已更新)",              // 备注说明
  "usages": [                               // 用途列表
    {
      "code": "PAYMENT",                    // 用途编码
      "value": "支付"                        // 用途说明
    }
  ],
  "apps": [                                 // 关联应用列表
    {
      "applicationManagementCid": "uuid-app", // 应用管理UUID
      "status": "open"                        // 状态
    }
  ]
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

### 3. 更新商户号状态

- **URL**: `/merchant/updStatus`
- **方法**: POST
- **描述**: 更新商户号状态（上线/下线）

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-xxx",    // 商户UUID，必填
  "status": "close"     // 状态，必填
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

### 4. 根据CID查询商户号

- **URL**: `/merchant/queryByCid`
- **方法**: GET
- **描述**: 根据商户UUID查询详细信息

#### 请求参数

查询参数:

- `cid`: 商户UUID，必填

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "cid": "uuid-xxx",
    "merchantUniqueNumber": "U202307010001",
    "merchantNumber": "M202307010001",
    "channelCode": "ALIPAY",
    "merchantType": "NORMAL",
    "status": "open",
    "channelAccountId": 123,
    "channelAccountCid": "uuid-account",
    "parentMerchantId": 456,
    "parentMerchantCid": "uuid-parent",
    "comment": "测试商户",
    "createTime": "2023-01-01 12:00:00",
    "updateTime": "2023-01-02 12:00:00",
    "channelCodeName": "支付宝",
    "merchantTypeName": "普通商户",
    "parentMerchantNumber": "M202307010000",
    "usages": [
      {
        "code": "PAYMENT",
        "value": "支付"
      }
    ],
    "apps": [
      {
        "cid": "uuid-app-relation",
        "applicationManagementId": 789,
        "status": "open",
        "appId": "app123456"
      }
    ]
  }
}
```

### 5. 查询商户号列表

- **URL**: `/merchant/queryList`
- **方法**: GET
- **描述**: 分页查询商户号列表

#### 请求参数

查询参数:

- `merchantNo`: 商户号，可选
- `uMerchantNo`: 商户唯一编号，可选
- `channelCode`: 渠道编码，可选
- `merchantType`: 商户类型，可选
- `status`: 状态，可选
- `pageNo`: 页码，必填
- `pageSize`: 每页记录数，必填

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "total": 100,           // 总记录数
    "data": [               // 商户号列表
      {
        "cid": "uuid-xxx",
        "merchantUniqueNumber": "U202307010001",
        "merchantNumber": "M202307010001",
        "channelCode": "ALIPAY",
        "merchantType": "NORMAL",
        "status": "open",
        "channelAccountId": 123,
        "channelAccountCid": "uuid-account",
        "parentMerchantId": 456,
        "parentMerchantCid": "uuid-parent",
        "comment": "测试商户",
        "createTime": "2023-01-01 12:00:00",
        "updateTime": "2023-01-02 12:00:00",
        "channelCodeName": "支付宝",
        "merchantTypeName": "普通商户",
        "parentMerchantNumber": "M202307010000",
        "usages": [
          {
            "code": "PAYMENT",
            "value": "支付"
          }
        ],
        "apps": [
          {
            "cid": "uuid-app-relation",
            "applicationManagementId": 789,
            "status": "open",
            "appId": "app123456"
          }
        ]
      }
    ]
  }
}
```

### 6. 新增商户号与应用关系

- **URL**: `/merchant/appAss/add`
- **方法**: POST
- **描述**: 为商户号添加应用关联关系

#### 请求参数

请求体 (JSON):

```json
{
  "merchantCid": "uuid-merchant",           // 商户UUID，必填
  "applicationManagementCid": "uuid-app",   // 应用管理UUID，必填
  "status": "open"                          // 状态，默认为"open"
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

### 7. 修改商户号与应用关系

- **URL**: `/merchant/appAss/modify`
- **方法**: POST
- **描述**: 修改商户号与应用的关联关系

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-relation",                   // 关联关系UUID，必填
  "merchantCid": "uuid-merchant",           // 商户UUID
  "applicationManagementCid": "uuid-app",   // 应用管理UUID
  "status": "close"                         // 状态
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

### 8. 删除商户号与应用关系

- **URL**: `/merchant/appAss/del`
- **方法**: POST
- **描述**: 删除商户号与应用的关联关系

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-relation"                    // 关联关系UUID，必填
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

## 数据结构说明

### MerchantDTO (商户数据传输对象)

| 字段名 | 类型 | 描述 | 是否必填 |
|-------|------|------|---------|
| cid | String | UUID | 修改时必填 |
| merchantUniqueNumber | String | 商户唯一编号 | 新增时必填 |
| merchantNumber | String | 商户号 | 新增时必填 |
| channelCode | String | 渠道编码 | 新增时必填 |
| channelType | String | 渠道类型 | 新增时必填 |
| merchantType | String | 商户类型 | 新增时必填 |
| status | String | 状态 | 新增时默认为"open" |
| channelAccountCid | String | 关联渠道账号UUID | 可选 |
| parentMerchantCid | String | 父级商户UUID | 可选 |
| comment | String | 备注说明 | 可选 |
| usages | List<MerchantUsageDTO> | 用途列表 | 可选 |
| apps | List<MerchantAppAssDTO> | 关联应用列表 | 可选 |

### MerchantUsageDTO (商户用途数据传输对象)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| code | String | 用途编码 |
| value | String | 用途说明 |

### MerchantAppAssDTO (商户应用关联数据传输对象)

| 字段名 | 类型 | 描述 | 是否必填 |
|-------|------|------|---------|
| cid | String | UUID | 修改时必填 |
| merchantCid | String | 商户UUID | 单独添加关联时必填 |
| applicationManagementCid | String | 应用管理UUID | 新增时必填 |
| status | String | 状态 | 新增时默认为"open" |

### MerchantVO (商户视图对象)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| cid | String | UUID |
| merchantUniqueNumber | String | 商户唯一编号 |
| merchantNumber | String | 商户号 |
| channelCode | String | 渠道编码 |
| merchantType | String | 商户类型 |
| status | String | 状态 |
| channelAccountId | Integer | 关联渠道账号ID |
| channelAccountCid | String | 关联渠道账号UUID |
| parentMerchantId | Integer | 父级商户ID |
| parentMerchantCid | String | 父级商户UUID |
| comment | String | 备注说明 |
| createTime | String | 创建时间 |
| updateTime | String | 更新时间 |
| channelCodeName | String | 渠道编码中文名 |
| merchantTypeName | String | 商户类型中文名 |
| parentMerchantNumber | String | 父级商户号 |
| usages | List<MerchantUsageVO> | 用途列表 |
| apps | List<MerchantAppAssVO> | 关联应用列表 |

### MerchantUsageVO (商户用途视图对象)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| code | String | 用途编码 |
| value | String | 用途说明 |

### MerchantAppAssVO (商户应用关联视图对象)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| cid | String | UUID |
| applicationManagementId | Integer | 应用管理ID |
| status | String | 状态 |
| appId | String | 应用ID |
