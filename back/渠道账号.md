# 渠道账号接口文档


## 基础信息

- 基础路径: `/channel_account`

## 通用响应结构

所有接口返回统一的 `Result` 结构:

```json
{
  "code": 0,         // 状态码，0表示成功，其他值表示错误
  "message": "",     // 错误信息，成功时为空
  "data": {}         // 响应数据，根据接口不同而不同
}
```

错误码说明:
- `0`: 成功
- `104`: 服务器错误
- `102`: 参数错误
- `140001`: 内部错误

## 接口列表

### 1. 新增渠道账号

- **URL**: `/channel_account/add`
- **方法**: POST
- **描述**: 创建新的渠道账号

#### 请求参数

请求体 (JSON):

```json
{
  "channelCode": "ALIPAY",           // 渠道编码，必填
  "channelType": "PAYMENT",          // 账户类型，必填
  "applicantEntity": "测试公司",      // 申请主体，必填
  "loginAccount": "test_account",    // 登录账号，必填
  "superAdmin": "张三",              // 超管
  "linkedAccountCid": "uuid-xxx",    // 关联账号UUID
  "platformUrl": "https://example.com", // 平台地址，必填
  "accs": [                          // 渠道员工关系，可选
    {
      "employeeCode": "EMP001",      // 员工编码，必填
      "fullName": "李四",            // 员工名称，必填
      "loginUsername": "lisi",       // 登录账号，必填
      "role": "ADMIN",               // 员工角色，必填
      "status": "open"               // 状态，默认为"open"
    }
  ]
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "cid": "uuid-xxx"  // 新创建的渠道账号UUID
  }
}
```

### 2. 修改渠道账号

- **URL**: `/channel_account/modify`
- **方法**: POST
- **描述**: 修改现有渠道账号信息

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-xxx",                 // 渠道账号UUID，必填
  "channelCode": "ALIPAY",           // 渠道编码
  "channelType": "PAYMENT",          // 账户类型
  "applicantEntity": "测试公司(已更新)", // 申请主体
  "loginAccount": "test_account",    // 登录账号
  "superAdmin": "张三",              // 超管
  "linkedAccountCid": "uuid-xxx",    // 关联账号UUID
  "platformUrl": "https://example.com", // 平台地址
  "accs": [                          // 渠道员工关系
    {
      "cid": "uuid-employee-1",      // 员工UUID (修改时必填)
      "employeeCode": "EMP001",      // 员工编码
      "fullName": "李四(已更新)",     // 员工名称
      "loginUsername": "lisi",       // 登录账号
      "role": "ADMIN",               // 员工角色
      "status": "open"               // 状态
    }
  ]
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

### 3. 查询渠道账号列表

- **URL**: `/channel_account/queryList`
- **方法**: GET
- **描述**: 分页查询渠道账号列表

#### 请求参数

查询参数:

- `channelCode`: 渠道编码，可选
- `channelType`: 渠道类型，可选
- `applicantEntity`: 申请主体，可选
- `pageNo`: 页码，必填
- `pageSize`: 每页记录数，必填

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "total": 100,           // 总记录数
    "data": [               // 渠道账号列表
      {
        "cid": "uuid-xxx",
        "channelCode": "ALIPAY",
        "channelType": "PAYMENT",
        "applicantEntity": "测试公司",
        "loginAccount": "test_account",
        "superAdmin": "张三",
        "linkedAccountId": 123,
        "platformUrl": "https://example.com",
        "createTime": "2023-01-01 12:00:00",
        "updateTime": "2023-01-02 12:00:00",
        "channelCodeName": "支付宝",
        "channelTypeName": "支付",
        "superAdminName": "张三",
        "linkedApplicantEntity": "关联主体",
        "accs": [
          {
            "cid": "uuid-employee-1",
            "employeeCode": "EMP001",
            "fullName": "李四",
            "loginUsername": "lisi",
            "role": "ADMIN",
            "status": "open",
            "createTime": "2023-01-01 12:00:00",
            "updateTime": "2023-01-02 12:00:00"
          }
        ]
      }
    ]
  }
}
```

### 4. 根据CID查询渠道账号

- **URL**: `/channel_account/queryByCid`
- **方法**: GET
- **描述**: 根据渠道账号CID查询详细信息

#### 请求参数

查询参数:

- `cid`: 渠道账号UUID，必填

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "cid": "uuid-xxx",
    "channelCode": "ALIPAY",
    "channelType": "PAYMENT",
    "applicantEntity": "测试公司",
    "loginAccount": "test_account",
    "superAdmin": "张三",
    "linkedAccountId": 123,
    "platformUrl": "https://example.com",
    "createTime": "2023-01-01 12:00:00",
    "updateTime": "2023-01-02 12:00:00",
    "channelCodeName": "支付宝",
    "channelTypeName": "支付",
    "superAdminName": "张三",
    "linkedApplicantEntity": "关联主体",
    "accs": [
      {
        "cid": "uuid-employee-1",
        "employeeCode": "EMP001",
        "fullName": "李四",
        "loginUsername": "lisi",
        "role": "ADMIN",
        "status": "open",
        "createTime": "2023-01-01 12:00:00",
        "updateTime": "2023-01-02 12:00:00"
      }
    ]
  }
}
```

### 5. 新增渠道员工

- **URL**: `/channel_account/employee/add`
- **方法**: POST
- **描述**: 为现有渠道账号添加员工

#### 请求参数

请求体 (JSON):

```json
{
  "channelCid": "uuid-xxx",        // 渠道账号UUID，必填
  "employeeCode": "EMP002",        // 员工编码，必填
  "fullName": "王五",              // 员工名称，必填
  "loginUsername": "wangwu",       // 登录账号，必填
  "role": "VIEWER",                // 员工角色，必填
  "status": "open"                 // 状态，默认为"open"
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": "",
  "data": {
    "cid": "uuid-employee-2"  // 新创建的员工UUID
  }
}
```

### 6. 修改渠道员工

- **URL**: `/channel_account/employee/modify`
- **方法**: POST
- **描述**: 修改渠道员工信息

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-employee-1",        // 员工UUID，必填
  "channelCid": "uuid-xxx",        // 渠道账号UUID
  "employeeCode": "EMP001",        // 员工编码
  "fullName": "李四(已更新)",       // 员工名称
  "loginUsername": "lisi",         // 登录账号
  "role": "ADMIN",                 // 员工角色
  "status": "open"                 // 状态
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

### 7. 更新渠道员工状态

- **URL**: `/channel_account/employee/updStatus`
- **方法**: POST
- **描述**: 更新渠道员工状态

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-employee-1",        // 员工UUID，必填
  "status": "close"                // 状态，必填
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

### 8. 删除渠道员工

- **URL**: `/channel_account/employee/del`
- **方法**: POST
- **描述**: 删除渠道员工

#### 请求参数

请求体 (JSON):

```json
{
  "cid": "uuid-employee-1"         // 员工UUID，必填
}
```

#### 响应参数

成功响应:

```json
{
  "code": 0,
  "message": ""
}
```

## 数据结构说明

### ChannelAccountDTO (渠道账号数据传输对象)

| 字段名 | 类型 | 描述 | 是否必填 |
|-------|------|------|---------|
| cid | String | UUID | 修改时必填 |
| channelCode | String | 渠道编码 | 新增时必填 |
| channelType | String | 账户类型 | 新增时必填 |
| applicantEntity | String | 申请主体 | 新增时必填 |
| loginAccount | String | 登录账号 | 新增时必填 |
| superAdmin | String | 超管 | 可选 |
| linkedAccountCid | String | 关联账号UUID | 可选 |
| platformUrl | String | 平台地址 | 新增时必填 |
| accs | List<ChannelEmployeeAccDTO> | 渠道员工关系 | 可选 |

### ChannelEmployeeAccDTO (渠道员工数据传输对象)

| 字段名 | 类型 | 描述 | 是否必填 |
|-------|------|------|---------|
| cid | String | UUID | 修改时必填 |
| channelCid | String | 渠道账号UUID | 单独添加员工时必填 |
| employeeCode | String | 员工编码 | 新增时必填 |
| fullName | String | 员工名称 | 新增时必填 |
| loginUsername | String | 登录账号 | 新增时必填 |
| role | String | 员工角色 | 新增时必填 |
| status | String | 状态 | 新增时默认为"open" |

### ChannelAccountVO (渠道账号视图对象)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| cid | String | UUID |
| channelCode | String | 渠道编码 |
| channelType | String | 账户类型 |
| applicantEntity | String | 申请主体 |
| loginAccount | String | 登录账号 |
| superAdmin | String | 超管 |
| linkedAccountId | Integer | 关联账号ID |
| platformUrl | String | 平台地址 |
| createTime | String | 创建时间 |
| updateTime | String | 更新时间 |
| channelCodeName | String | 渠道编码中文名 |
| channelTypeName | String | 账户类型中文名 |
| superAdminName | String | 超管中文名 |
| linkedApplicantEntity | String | 关联账号的申请主体 |
| accs | List<ChannelEmployeeAccVO> | 渠道员工关系 |

### ChannelEmployeeAccVO (渠道员工视图对象)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| cid | String | UUID |
| employeeCode | String | 员工编码 |
| fullName | String | 员工名称 |
| loginUsername | String | 登录账号 |
| role | String | 员工角色 |
| status | String | 状态 |
| createTime | String | 创建时间 |
| updateTime | String | 更新时间 |
