# 技术栈选择与说明

## 核心框架选择

基于对项目需求的分析，我们选择以下核心技术栈：

### 基础框架
- **Vue 3 + Composition API**：选择Vue 3作为基础框架，利用其Composition API实现更灵活的逻辑复用和组件开发
- **TypeScript**：使用TypeScript提供更好的类型安全性和开发体验，增强代码可维护性
- **Vite**：使用Vite作为构建工具，提供更快的开发环境启动和热更新速度

### UI框架与样式处理
- **Element Plus**：使用Element Plus作为主要UI组件库，提供丰富的预制组件
- **Tailwind CSS**：结合Tailwind CSS实现快速样式开发和定制化
- **Sass**：使用Sass作为CSS预处理器，增强样式开发体验

### 状态管理与路由
- **Pinia**：使用Pinia作为状态管理方案，替代Vuex，提供更简洁的API和类型支持
- **Vue Router**：使用Vue Router处理路由管理，支持路由守卫和动态路由

### 请求与工具
- **Axios**：使用Axios处理HTTP请求，实现拦截器和统一错误处理
- **Lodash**：使用Lodash提供实用的JavaScript工具函数
- **Day.js**：轻量级日期处理库，替代Moment.js

### 图标与字体
- **Font Awesome**：使用Font Awesome提供丰富的图标资源
- **Noto Sans SC**：使用Google的Noto Sans SC作为主要字体，确保良好的中文显示效果

## 技术选型依据

### 为什么选择Vue 3 + TypeScript + Vite?

1. **性能考虑**：
   - Vue 3比Vue 2性能更好，基于Proxy的响应式系统更高效
   - Vite提供极速的开发服务器启动和热更新
   - 按需编译的特性使得大型应用在开发环境下仍能保持高效

2. **开发体验**：
   - Composition API解决了Vue 2中混入(mixin)难以追踪和维护的问题
   - TypeScript提供强类型约束，减少运行时错误
   - Vite的快速热重载能提高开发效率

3. **维护性**：
   - 类型系统使代码更易于理解和重构
   - Composition API使逻辑更易组织和复用
   - 官方支持和活跃的社区保证长期维护

### 为什么选择Element Plus + Tailwind CSS?

1. **Element Plus**：
   - 提供完整的企业级UI组件库
   - 与Vue 3深度集成，支持TypeScript
   - 中文优先，适合国内开发团队
   - 具有一致的设计风格和丰富的功能

2. **Tailwind CSS**：
   - 提供原子化CSS类，加速UI开发
   - 减少自定义CSS的编写量
   - 易于与Element Plus配合，增强定制能力
   - 确保一致的设计系统

### 为什么选择Pinia而非Vuex?

1. **更现代的API**：
   - 提供更简洁的API设计，无需mutations
   - 支持组合式API风格，更适合Vue 3
   - 更好的TypeScript支持
   - 开箱即用的devtools支持

2. **性能优势**：
   - 优化后的性能，减少不必要的组件更新
   - 更小的包体积
   - 更好的代码分割支持

### 为什么选择Axios?

1. **功能完备**：
   - 支持请求/响应拦截器
   - 支持请求取消
   - 自动转换JSON数据
   - 客户端CSRF保护

2. **广泛采用**：
   - 成熟稳定的解决方案
   - 丰富的文档和社区支持
   - 与Vue生态良好兼容

## 工具链配置

### ESLint配置

采用以下ESLint配置确保代码质量：

```javascript
module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2021: true,
  },
  extends: [
    'plugin:vue/vue3-recommended',
    'eslint:recommended',
    '@vue/typescript/recommended',
    '@vue/prettier',
    '@vue/prettier/@typescript-eslint',
  ],
  parserOptions: {
    ecmaVersion: 2021,
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/multi-word-component-names': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    'prettier/prettier': [
      'error',
      {
        singleQuote: true,
        semi: false,
        trailingComma: 'es5',
      },
    ],
  },
}
```

### Prettier配置

采用以下Prettier配置确保代码风格统一：

```javascript
module.exports = {
  printWidth: 100,
  tabWidth: 2,
  useTabs: false,
  semi: false,
  singleQuote: true,
  quoteProps: 'as-needed',
  jsxSingleQuote: false,
  trailingComma: 'es5',
  bracketSpacing: true,
  jsxBracketSameLine: false,
  arrowParens: 'avoid',
  rangeStart: 0,
  rangeEnd: Infinity,
  requirePragma: false,
  insertPragma: false,
  proseWrap: 'preserve',
  htmlWhitespaceSensitivity: 'css',
  endOfLine: 'lf',
}
```

### Vite配置

基础Vite配置如下：

```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`,
      },
    },
  },
})
```

### 测试工具

选择Vitest作为单元测试框架，配合Vue Test Utils进行组件测试：

```typescript
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    globals: true,
    deps: {
      inline: ['element-plus'],
    },
  },
})
```

## 开发规范

### 目录结构规范

```
src/
├── assets/                # 静态资源
│   ├── images/            # 图片资源
│   ├── icons/             # 图标资源
│   └── styles/            # 全局样式
├── components/            # 组件
│   ├── common/            # 通用组件
│   ├── layout/            # 布局组件
│   └── business/          # 业务组件
├── composables/           # 组合式函数
├── router/                # 路由
│   ├── index.ts           # 路由主文件
│   └── modules/           # 路由模块
├── stores/                # 状态管理
│   ├── index.ts           # Store入口
│   └── modules/           # Store模块
├── api/                   # API接口
│   ├── index.ts           # API入口
│   └── modules/           # API模块
├── utils/                 # 工具函数
│   ├── request.ts         # Axios配置
│   ├── auth.ts            # 认证相关
│   └── helpers.ts         # 辅助函数
├── views/                 # 页面组件
│   ├── dashboard/         # 仪表盘
│   ├── payment/           # 支付相关
│   ├── channel/           # 渠道相关
│   └── system/            # 系统管理
├── types/                 # 类型定义
├── constants/             # 常量定义
├── App.vue                # 根组件
└── main.ts                # 入口文件
```

### 命名规范

1. **文件命名**：
   - 组件文件使用PascalCase：`UserProfile.vue`
   - 非组件文件使用kebab-case：`auth-service.ts`
   - 页面组件添加Page后缀：`DashboardPage.vue`

2. **组件命名**：
   - 组件名使用PascalCase：`UserProfile`
   - 基础组件添加App前缀：`AppButton`
   - 单例组件添加The前缀：`TheHeader`

3. **变量命名**：
   - 变量使用camelCase：`userName`
   - 常量使用UPPER_SNAKE_CASE：`MAX_COUNT`
   - 私有属性使用下划线前缀：`_privateVar`

### 代码风格规范

1. **组件选项顺序**：
   ```typescript
   // 这是 Vue 2 和 Vue 3 选项式 API (Options API) 的基本结构。
   // 在本项目中，我们优先使用组合式 API (Composition API) 与 <script setup>。
   export default {
     name: 'ComponentName',
     components: {},
     props: {},
     emits: [],
     // data, computed, watch, methods 等选项也可以用 TypeScript 书写
     // 例如 data(): { message: string } { return { message: 'hello' } }
     setup() { /* ... */ },
     data() { /* ... */ },
     computed: { /* ... */ },
     watch: { /* ... */ },
     created() { /* ... */ },
     mounted() { /* ... */ },
     methods: { /* ... */ }
   }
   ```

2. **Composition API风格**：
   ```typescript
   // 推荐使用 <script setup lang="ts"> 以获得更简洁的组合式 API 体验
   // 以下是在 <script setup> 环境中的示例：
   
   // import { ref, computed, onMounted } from 'vue'
   
   // const count = ref<number>(0)
   // const doubleCount = computed<number>(() => count.value * 2)
   
   // function increment(): void {
   //   count.value++
   // }
   
   // onMounted(() => {
   //   console.log('Component mounted')
   // })
   
   // 在 <script setup> 中，所有顶级绑定 (包括变量和函数) 都自动暴露给模板
   // 无需显式 return
   
   // 如果确实需要使用显式的 setup() 函数 (例如不使用 <script setup> 时):
   import { defineComponent, ref, computed, onMounted } from 'vue'
   
   export default defineComponent({
     setup() {
       const count = ref<number>(0)
       const doubleCount = computed<number>(() => count.value * 2)
       
       function increment(): void {
         count.value++
       }
       
       onMounted((): void => {
         console.log('Component mounted')
       })
       
       return {
         count,
         doubleCount,
         increment
       }
     }
   })
   ```

3. **Props定义**：
   ```typescript
   // 在 <script setup lang="ts"> 中定义 Props
   
   // 方式一：使用运行时声明 (配合 TypeScript 类型推断)
   // defineProps({
   //   status: {
   //     type: String as () => 'active' | 'inactive', // 使用 PropType 进行更精确的类型约束
   //     required: true,
   //     validator: (value: string) => ['active', 'inactive'].includes(value)
   //   },
   //   message: {
   //     type: String,
   //     default: ''
   //   },
   //   id: Number // 简单类型可以直接指定
   // })

   // 方式二：使用基于类型的声明 (推荐，更符合 TypeScript 风格)
   interface Props {
     status: 'active' | 'inactive';
     message?: string; // 可选 prop
     id: number;
     items?: string[]; // 数组类型
     onClick?: (payload: Event) => void; // 函数类型
   }
   
   // const props = defineProps<Props>()
   
   // 带默认值的基于类型的声明 (Vue 3.3+)
   const props = withDefaults(defineProps<Props>(), {
     message: 'default message',
     items: () => ['item1', 'item2'], // 对象或数组的默认值需要使用工厂函数
   })
   ``` 