# 通用UI组件分析

本文档总结了对 MICS 项目通用UI组件的分析结果，基于 `task/02_01_component_prototype_mapping.md` 文件及项目需求。

## 1. `Button.vue`

*   **原型文件**: 所有页面
*   **说明**: 按钮组件
*   **主要职责**:
    *   提供统一的按钮交互界面
    *   支持多种按钮类型和状态
    *   处理点击事件和加载状态

*   **组件变体**:
    *   **类型 (type)**:
        *   `primary`: 主要按钮，用于强调主操作
        *   `secondary`: 次要按钮，用于次要操作
        *   `text`: 文本按钮，最轻量级的按钮形式
        *   `link`: 链接按钮，模拟链接行为
        *   `danger`: 危险操作按钮，如删除
        *   `success`: 成功/确认按钮
        *   `warning`: 警告按钮
    *   **尺寸 (size)**:
        *   `small`: 小型按钮
        *   `medium`: 中等按钮（默认）
        *   `large`: 大型按钮
    *   **状态**:
        *   普通状态
        *   悬停状态 (hover)
        *   按下状态 (active)
        *   禁用状态 (disabled)
        *   加载状态 (loading)

*   **Props 定义**:
    ```typescript
    interface ButtonProps {
      type?: 'primary' | 'secondary' | 'text' | 'link' | 'danger' | 'success' | 'warning'
      size?: 'small' | 'medium' | 'large'
      disabled?: boolean
      loading?: boolean
      block?: boolean // 是否占满容器宽度
      icon?: string // 按钮图标
      htmlType?: 'button' | 'submit' | 'reset' // 原生button类型
    }
    ```

*   **Events 定义**:
    ```typescript
    interface ButtonEvents {
      click: (event: MouseEvent) => void
      mouseenter: (event: MouseEvent) => void
      mouseleave: (event: MouseEvent) => void
    }
    ```

*   **Slots 定义**:
    *   `default`: 按钮文本内容
    *   `icon`: 自定义图标
    *   `loading`: 自定义加载图标

*   **样式变量**:
    ```scss
    // 主题色变量
    --button-primary-bg: #1890ff;
    --button-primary-color: #fff;
    --button-secondary-bg: #fff;
    --button-secondary-color: #333;
    --button-secondary-border: #d9d9d9;
    
    // 尺寸变量
    --button-height-small: 24px;
    --button-height-medium: 32px;
    --button-height-large: 40px;
    
    // 圆角变量
    --button-border-radius: 2px;
    
    // 过渡变量
    --button-transition: all 0.3s;
    ```

*   **交互行为**:
    *   点击触发 `click` 事件
    *   加载状态下禁止点击
    *   禁用状态下不响应任何事件
    *   支持键盘访问和焦点状态

*   **无障碍设计**:
    *   支持键盘导航
    *   加载状态时通过 `aria-busy` 标识
    *   禁用状态时通过 `aria-disabled` 标识
    *   图标按钮提供 `aria-label`

*   **使用示例**:
    ```vue
    <template>
      <div>
        <!-- 基础按钮 -->
        <Button>默认按钮</Button>
        <Button type="primary">主要按钮</Button>
        <Button type="danger">危险按钮</Button>
        
        <!-- 不同尺寸 -->
        <Button size="small">小型按钮</Button>
        <Button size="large">大型按钮</Button>
        
        <!-- 加载状态 -->
        <Button :loading="true">加载中</Button>
        
        <!-- 禁用状态 -->
        <Button disabled>禁用按钮</Button>
        
        <!-- 图标按钮 -->
        <Button icon="search">搜索</Button>
      </div>
    </template>
    ```

*   **最佳实践**:
    1.  为不同操作选择合适的按钮类型
    2.  在表单中使用 `htmlType="submit"` 的按钮
    3.  危险操作使用 `type="danger"` 并配合确认机制
    4.  加载状态时提供视觉反馈
    5.  按钮文本应简洁明了 

## 2. 表单组件

### 2.1 `Input.vue`

*   **原型文件**: 所有表单页面
*   **说明**: 输入框组件
*   **主要职责**:
    *   提供文本输入功能
    *   支持多种输入类型
    *   处理输入验证和格式化

*   **组件变体**:
    *   **类型 (type)**:
        *   `text`: 普通文本输入
        *   `password`: 密码输入
        *   `number`: 数字输入
        *   `textarea`: 多行文本输入
        *   `search`: 搜索输入框
    *   **尺寸 (size)**:
        *   `small`: 小型输入框
        *   `medium`: 中等输入框（默认）
        *   `large`: 大型输入框
    *   **状态**:
        *   普通状态
        *   聚焦状态 (focused)
        *   禁用状态 (disabled)
        *   只读状态 (readonly)
        *   错误状态 (error)

*   **Props 定义**:
    ```typescript
    interface InputProps {
      type?: 'text' | 'password' | 'number' | 'textarea' | 'search'
      size?: 'small' | 'medium' | 'large'
      value?: string | number
      placeholder?: string
      disabled?: boolean
      readonly?: boolean
      maxLength?: number
      showCount?: boolean
      clearable?: boolean
      prefix?: string
      suffix?: string
      error?: boolean
      errorMessage?: string
      rows?: number // 用于 textarea
      autosize?: boolean | { minRows: number; maxRows: number } // 用于 textarea
    }
    ```

*   **Events 定义**:
    ```typescript
    interface InputEvents {
      'update:value': (value: string) => void
      input: (event: Event) => void
      change: (value: string) => void
      focus: (event: FocusEvent) => void
      blur: (event: FocusEvent) => void
      clear: () => void
      keydown: (event: KeyboardEvent) => void
      keyup: (event: KeyboardEvent) => void
      enter: (value: string) => void
    }
    ```

### 2.2 `Select.vue`

*   **原型文件**: 所有表单页面
*   **说明**: 下拉选择组件
*   **主要职责**:
    *   提供单选/多选下拉选择功能
    *   支持选项分组
    *   支持搜索过滤

*   **Props 定义**:
    ```typescript
    interface SelectProps {
      value: any | any[]
      options: Array<{ label: string; value: any; disabled?: boolean }>
      multiple?: boolean
      disabled?: boolean
      clearable?: boolean
      filterable?: boolean
      loading?: boolean
      placeholder?: string
      size?: 'small' | 'medium' | 'large'
      remote?: boolean
      remoteMethod?: (query: string) => Promise<void>
    }
    ```

### 2.3 `Checkbox.vue`

*   **原型文件**: 多个列表页面
*   **说明**: 复选框组件
*   **主要职责**:
    *   提供单个复选框和复选框组
    *   支持中间态
    *   支持禁用状态

*   **Props 定义**:
    ```typescript
    interface CheckboxProps {
      checked?: boolean
      indeterminate?: boolean
      disabled?: boolean
      value?: any
      label?: string
    }
    ```

## 3. 数据展示组件

### 3.1 `Table.vue`

*   **原型文件**: 所有列表页面
*   **说明**: 表格组件
*   **主要职责**:
    *   展示结构化数据
    *   支持排序、筛选
    *   支持固定表头和列

*   **Props 定义**:
    ```typescript
    interface TableProps {
      data: any[]
      columns: TableColumn[]
      loading?: boolean
      bordered?: boolean
      stripe?: boolean
      size?: 'small' | 'medium' | 'large'
      rowKey: string
      selection?: boolean
      expandable?: boolean
      scroll?: { x?: number | string; y?: number | string }
    }
    
    interface TableColumn {
      title: string
      dataIndex: string
      key: string
      width?: number | string
      fixed?: 'left' | 'right'
      sortable?: boolean
      filterable?: boolean
      render?: (text: any, record: any, index: number) => VNode | string
    }
    ```

### 3.2 `Card.vue`

*   **原型文件**: 所有页面
*   **说明**: 卡片容器组件
*   **主要职责**:
    *   提供带标题和内容的卡片容器
    *   支持自定义操作区
    *   支持加载状态

*   **Props 定义**:
    ```typescript
    interface CardProps {
      title?: string
      extra?: string
      bordered?: boolean
      loading?: boolean
      hoverable?: boolean
      bodyStyle?: CSSProperties
    }
    ```

### 3.3 `List.vue`

*   **原型文件**: dashboard.html, operation_log.html
*   **说明**: 列表组件
*   **主要职责**:
    *   展示列表数据
    *   支持加载更多
    *   支持空状态展示

*   **Props 定义**:
    ```typescript
    interface ListProps {
      dataSource: any[]
      loading?: boolean
      itemLayout?: 'horizontal' | 'vertical'
      loadMore?: boolean
      grid?: { gutter?: number; column?: number }
    }
    ```

## 4. 反馈组件

### 4.1 `Modal.vue`

*   **原型文件**: 所有页面
*   **说明**: 模态对话框组件
*   **主要职责**:
    *   展示重要信息或需要用户确认的内容
    *   支持自定义按钮
    *   支持拖拽和调整大小

*   **Props 定义**:
    ```typescript
    interface ModalProps {
      visible: boolean
      title?: string
      width?: number | string
      centered?: boolean
      closable?: boolean
      maskClosable?: boolean
      confirmLoading?: boolean
      destroyOnClose?: boolean
    }
    ```

### 4.2 `Alert.vue`

*   **原型文件**: 多个页面
*   **说明**: 警告提示组件
*   **主要职责**:
    *   展示警告、提示信息
    *   支持不同类型的提示
    *   支持关闭功能

*   **Props 定义**:
    ```typescript
    interface AlertProps {
      type?: 'success' | 'info' | 'warning' | 'error'
      message: string
      description?: string
      closable?: boolean
      showIcon?: boolean
      banner?: boolean
    }
    ```

### 4.3 `Notification.vue`

*   **原型文件**: 操作成功/失败提示
*   **说明**: 通知提醒组件
*   **主要职责**:
    *   展示全局通知信息
    *   支持不同类型的通知
    *   支持自动关闭

*   **Props 定义**:
    ```typescript
    interface NotificationProps {
      type?: 'success' | 'info' | 'warning' | 'error'
      message: string
      description?: string
      duration?: number
      placement?: 'topRight' | 'topLeft' | 'bottomRight' | 'bottomLeft'
    }
    ```

## 5. 导航组件

### 5.1 `Tabs.vue`

*   **原型文件**: payment_method_detail.html, merchant_detail.html
*   **说明**: 标签页组件
*   **主要职责**:
    *   提供内容分组展示
    *   支持动态增减标签
    *   支持自定义标签内容

*   **Props 定义**:
    ```typescript
    interface TabsProps {
      value: string | number
      type?: 'line' | 'card'
      closable?: boolean
      addable?: boolean
      position?: 'top' | 'right' | 'bottom' | 'left'
      animated?: boolean
    }
    ```

### 5.2 `Breadcrumb.vue`

*   **原型文件**: 所有页面
*   **说明**: 面包屑导航
*   **主要职责**:
    *   显示当前页面在系统层级结构中的位置
    *   提供快速返回功能
    *   支持自定义分隔符

*   **Props 定义**:
    ```typescript
    interface BreadcrumbProps {
      routes?: Array<{
        path: string
        breadcrumbName: string
        children?: Array<{
          path: string
          breadcrumbName: string
        }>
      }>
      separator?: string | VNode
    }
    ```

### 5.3 `Pagination.vue`

*   **原型文件**: 所有列表页面
*   **说明**: 分页组件
*   **主要职责**:
    *   提供数据分页导航
    *   支持页码跳转
    *   支持自定义每页条数

*   **Props 定义**:
    ```typescript
    interface PaginationProps {
      current: number
      total: number
      pageSize: number
      showSizeChanger?: boolean
      showQuickJumper?: boolean
      showTotal?: (total: number) => string
      simple?: boolean
      disabled?: boolean
    }
    ```

*   **Events 定义**:
    ```typescript
    interface PaginationEvents {
      'update:current': (page: number) => void
      'update:pageSize': (size: number) => void
      change: (page: number, pageSize: number) => void
    }
    ``` 