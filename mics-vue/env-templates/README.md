# 环境变量配置模板

本目录包含了项目中使用的环境变量模板文件，用于不同环境的配置。

## 使用方法

1. 复制相应的模板文件到项目根目录
2. 根据实际环境修改配置值
3. 确保不要将环境文件提交到版本控制系统中

## 环境文件说明

- `.env` - 基础环境变量，所有环境共享
- `.env.development` - 开发环境专用变量
- `.env.production` - 生产环境专用变量
- `.env.test` - 测试环境专用变量

## 模板内容

### 基础环境配置 (.env)

```
# 基础环境配置 - 所有环境共享
# API相关
VITE_API_TIMEOUT=15000
VITE_API_RETRIES=3

# 系统配置
VITE_APP_TITLE=MICS支付管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_COPYRIGHT=© 2025 MICS. All rights reserved.

# 分页配置
VITE_DEFAULT_PAGE_SIZE=10
VITE_PAGE_SIZES=10,20,50,100
```

### 开发环境配置 (.env.development)

```
# 开发环境配置
VITE_API_BASE_URL=/api
VITE_USE_MOCK=true
VITE_APP_TITLE=MICS支付管理系统(开发)
```

### 生产环境配置 (.env.production)

```
# 生产环境配置
VITE_API_BASE_URL=https://api.example.com
VITE_USE_MOCK=false
VITE_APP_TITLE=MICS支付管理系统
```

### 测试环境配置 (.env.test)

```
# 测试环境配置
VITE_API_BASE_URL=https://test-api.example.com
VITE_USE_MOCK=false
VITE_APP_TITLE=MICS支付管理系统(测试)
```

## 环境变量说明

| 变量名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| VITE_API_BASE_URL | string | /api | API基础路径 |
| VITE_API_TIMEOUT | number | 15000 | API请求超时时间(毫秒) |
| VITE_API_RETRIES | number | 3 | API请求失败重试次数 |
| VITE_USE_MOCK | boolean | true | 是否使用Mock数据 |
| VITE_APP_TITLE | string | MICS支付管理系统 | 应用标题 |
| VITE_APP_VERSION | string | 1.0.0 | 应用版本号 |
| VITE_APP_COPYRIGHT | string | © 2024 MICS. All rights reserved. | 版权信息 |
| VITE_DEFAULT_PAGE_SIZE | number | 10 | 默认分页大小 |
| VITE_PAGE_SIZES | string | 10,20,50,100 | 分页选项，逗号分隔 |

## 注意事项

1. 敏感信息（如密钥、密码等）不要存储在环境变量中
2. 对于敏感配置，建议使用运行时加载或更安全的存储方式
3. 本地开发时可以创建`.env.local`文件进行个人定制，该文件不会被提交到版本控制系统 