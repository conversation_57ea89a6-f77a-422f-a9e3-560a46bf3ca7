/// <reference types="vite/client" />

/**
 * 环境变量类型定义
 * 为所有环境变量提供类型安全
 */
interface ImportMetaEnv {
  // 构建环境
  readonly MODE: string;
  readonly PROD: boolean;
  readonly DEV: boolean;
  
  // API配置
  readonly VITE_API_BASE_URL: string;
  readonly VITE_API_TIMEOUT?: string;
  readonly VITE_API_RETRIES?: string;
  
  // 功能开关
  readonly VITE_USE_MOCK: string;
  readonly VITE_ENABLE_MOCK?: string;
  
  // 应用信息
  readonly VITE_APP_TITLE?: string;
  readonly VITE_APP_VERSION?: string;
  readonly VITE_APP_COPYRIGHT?: string;
  
  // 分页配置
  readonly VITE_DEFAULT_PAGE_SIZE?: string;
  readonly VITE_PAGE_SIZES?: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
