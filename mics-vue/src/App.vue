/**
 * @file App.vue
 * @description 应用程序根组件，负责加载主布局
 * <AUTHOR> Assistant
 */
<script setup lang="ts">
import Layout from '@/views/layouts/Layout.vue'
import { StagewiseToolbar } from '@stagewise/toolbar-vue'
import { ref } from 'vue'

// stagewise工具栏配置
const stagewiseConfig = {
  plugins: []
}

// 判断是否为开发环境
const isDev = ref(import.meta.env.DEV)
</script>

<template>
  <!-- 加载主布局组件 -->
  <Layout />
  
  <!-- stagewise工具栏，仅在开发环境下显示 -->
  <StagewiseToolbar v-if="isDev" :config="stagewiseConfig" />
</template>

<style>
/* 重置页面基础样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

/* 设置应用根容器高度 */
#app {
  height: 100%;
}
</style>
