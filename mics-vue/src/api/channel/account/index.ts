/**
 * @file index.ts
 * @description 渠道账户API模块入口
 */

import { apiConfig } from '@/config/env'
import { RealChannelAccountService } from './real'
import { MockChannelAccountService } from './mock'
import type { IChannelAccountService } from './interface'

// 根据配置创建服务实例
const channelAccountService: IChannelAccountService = apiConfig.useMock
  ? new MockChannelAccountService()
  : new RealChannelAccountService()

// 导出服务实例
export default channelAccountService

// 导出所有类型和接口
export * from './types'
export * from './interface' 