/**
 * @file interface.ts
 * @description 渠道账户服务接口定义
 */

import type {
  IChannelAccountQueryParams,
  IChannelAccountListResponse,
  IChannelAccountDetail,
  ICreateChannelAccountRequest,
  IUpdateChannelAccountRequest,
  IChannelEmployeeQueryParams,
  IChannelEmployeeListResponse,
  IChannelEmployee,
  ICreateChannelEmployeeRequest,
  IUpdateChannelEmployeeRequest
} from './types'

/**
 * 渠道账户服务接口
 */
export interface IChannelAccountService {
  /**
   * 获取渠道账户列表
   */
  getChannelAccountList(params: IChannelAccountQueryParams): Promise<IChannelAccountListResponse>

  /**
   * 获取渠道账户详情
   */
  getChannelAccountDetail(id: string): Promise<IChannelAccountDetail>

  /**
   * 创建渠道账户
   */
  createChannelAccount(data: ICreateChannelAccountRequest): Promise<IChannelAccountDetail>

  /**
   * 更新渠道账户
   */
  updateChannelAccount(data: IUpdateChannelAccountRequest): Promise<IChannelAccountDetail>

  /**
   * 删除渠道账户
   */
  deleteChannelAccount(id: string): Promise<void>

  /**
   * 更新渠道账户状态
   */
  updateChannelAccountStatus(id: string, status: string): Promise<void>

  /**
   * 获取渠道员工列表
   */
  getChannelEmployeeList(params: IChannelEmployeeQueryParams): Promise<IChannelEmployeeListResponse>

  /**
   * 获取渠道员工详情
   */
  getChannelEmployeeDetail(id: string): Promise<IChannelEmployee>

  /**
   * 创建渠道员工
   */
  createChannelEmployee(data: ICreateChannelEmployeeRequest): Promise<IChannelEmployee>

  /**
   * 更新渠道员工
   */
  updateChannelEmployee(data: IUpdateChannelEmployeeRequest): Promise<IChannelEmployee>

  /**
   * 删除渠道员工
   */
  deleteChannelEmployee(id: string): Promise<void>

  /**
   * 更新渠道员工状态
   */
  updateChannelEmployeeStatus(id: string, status: string): Promise<void>
} 