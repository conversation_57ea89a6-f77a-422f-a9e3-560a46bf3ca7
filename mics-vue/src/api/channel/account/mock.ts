/**
 * @file mock.ts
 * @description 渠道账户Mock服务实现
 */

import type { IChannelAccountService } from './interface'
import { ChannelType, ChannelAccountStatus } from './types'
import type {
  IChannelAccountQueryParams,
  IChannelAccountListResponse,
  IChannelAccountDetail,
  ICreateChannelAccountRequest,
  IUpdateChannelAccountRequest,
  IChannelEmployeeQueryParams,
  IChannelEmployeeListResponse,
  IChannelEmployee,
  ICreateChannelEmployeeRequest,
  IUpdateChannelEmployeeRequest
} from './types'
import { createLogger } from '@/utils/logger'

const logger = createLogger('channel-account-service-mock')

/**
 * Mock 渠道账户服务实现
 */
export class MockChannelAccountService implements IChannelAccountService {
  // Mock渠道账户数据
  private mockAccounts: IChannelAccountDetail[] = [
    {
      id: '1',
      code: 'CH001',
      name: '微信支付服务商',
      channelType: ChannelType.WECHAT,
      status: ChannelAccountStatus.ACTIVE,
      merchantId: '1',
      merchantName: '测试商户1',
      appId: 'wx12345678',
      appSecret: '123456789abcdef',
      contactName: '张三',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      remarks: '微信支付渠道',
      dailyLimit: 1000000,
      monthlyLimit: ********,
      singleLimit: 5000,
      createdAt: '2024-03-14T10:00:00Z',
      updatedAt: '2024-03-14T10:00:00Z',
      employees: [
        {
          id: '101',
          name: '张三',
          jobTitle: '渠道经理',
          phone: '***********',
          email: '<EMAIL>',
          status: 'ACTIVE',
          channelAccountId: '1',
          department: '支付部',
          createdAt: '2024-03-14T10:00:00Z',
          updatedAt: '2024-03-14T10:00:00Z'
        },
        {
          id: '102',
          name: '李四',
          jobTitle: '技术支持',
          phone: '***********',
          email: '<EMAIL>',
          status: 'ACTIVE',
          channelAccountId: '1',
          department: '技术部',
          createdAt: '2024-03-14T11:00:00Z',
          updatedAt: '2024-03-14T11:00:00Z'
        }
      ]
    },
    {
      id: '2',
      code: 'CH002',
      name: '支付宝直连',
      channelType: ChannelType.ALIPAY,
      status: ChannelAccountStatus.ACTIVE,
      merchantId: '2',
      merchantName: '测试商户2',
      appId: '****************',
      appSecret: 'abcdef123456789',
      contactName: '李四',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      remarks: '支付宝直连渠道',
      dailyLimit: 1500000,
      monthlyLimit: ********,
      singleLimit: 10000,
      createdAt: '2024-03-15T10:00:00Z',
      updatedAt: '2024-03-15T10:00:00Z',
      employees: [
        {
          id: '103',
          name: '王五',
          jobTitle: '渠道经理',
          phone: '***********',
          email: '<EMAIL>',
          status: 'ACTIVE',
          channelAccountId: '2',
          department: '支付部',
          createdAt: '2024-03-15T10:00:00Z',
          updatedAt: '2024-03-15T10:00:00Z'
        }
      ]
    },
    {
      id: '3',
      code: 'CH003',
      name: '银联商务',
      channelType: ChannelType.UNIONPAY,
      status: ChannelAccountStatus.INACTIVE,
      merchantId: '3',
      merchantName: '测试商户3',
      appId: 'up123456789',
      appSecret: '987654321abcdef',
      contactName: '王五',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      remarks: '银联商务渠道',
      dailyLimit: 2000000,
      monthlyLimit: ********,
      singleLimit: 20000,
      createdAt: '2024-03-16T10:00:00Z',
      updatedAt: '2024-03-16T10:00:00Z',
      employees: []
    },
    {
      id: '4',
      code: 'CH004',
      name: '京东支付',
      channelType: ChannelType.JD_PAY,
      status: ChannelAccountStatus.PENDING,
      merchantId: '4',
      merchantName: '测试商户4',
      appId: 'jd123456789',
      appSecret: 'jd987654321',
      contactName: '赵六',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      remarks: '京东支付渠道',
      dailyLimit: 500000,
      monthlyLimit: ********,
      singleLimit: 5000,
      createdAt: '2024-03-17T10:00:00Z',
      updatedAt: '2024-03-17T10:00:00Z',
      employees: [
        {
          id: '104',
          name: '赵六',
          jobTitle: '渠道经理',
          phone: '***********',
          email: '<EMAIL>',
          status: 'ACTIVE',
          channelAccountId: '4',
          department: '支付部',
          createdAt: '2024-03-17T10:00:00Z',
          updatedAt: '2024-03-17T10:00:00Z'
        },
        {
          id: '105',
          name: '钱七',
          jobTitle: '技术支持',
          phone: '***********',
          email: '<EMAIL>',
          status: 'INACTIVE',
          channelAccountId: '4',
          department: '技术部',
          createdAt: '2024-03-17T11:00:00Z',
          updatedAt: '2024-03-17T11:00:00Z'
        }
      ]
    }
  ]

  /**
   * 获取渠道账户列表
   */
  async getChannelAccountList(params: IChannelAccountQueryParams): Promise<IChannelAccountListResponse> {
    logger.info('Mock: 获取渠道账户列表', params)
    
    let filteredAccounts = [...this.mockAccounts]
    
    // 应用过滤条件
    if (params.channelType) {
      filteredAccounts = filteredAccounts.filter(
        account => account.channelType === params.channelType
      )
    }
    
    if (params.status) {
      filteredAccounts = filteredAccounts.filter(
        account => account.status === params.status
      )
    }
    
    if (params.merchantId) {
      filteredAccounts = filteredAccounts.filter(
        account => account.merchantId === params.merchantId
      )
    }
    
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      filteredAccounts = filteredAccounts.filter(
        account => 
          account.name.toLowerCase().includes(keyword) ||
          account.code.toLowerCase().includes(keyword) ||
          account.contactName.toLowerCase().includes(keyword)
      )
    }
    
    // 应用排序
    if (params.sortField) {
      const direction = params.sortOrder === 'descend' ? -1 : 1
      filteredAccounts.sort((a: any, b: any) => {
        if (a[params.sortField!] < b[params.sortField!]) return -1 * direction
        if (a[params.sortField!] > b[params.sortField!]) return 1 * direction
        return 0
      })
    }
    
    // 应用分页
    const page = params.page || 1
    const pageSize = params.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const paginatedAccounts = filteredAccounts.slice(start, end)
    
    // 构造基础返回对象（不包含详细信息）
    const baseAccounts = paginatedAccounts.map(account => {
      const { employees, appSecret, certInfo, configData, ...baseAccount } = account
      return baseAccount
    })
    
    return {
      total: filteredAccounts.length,
      items: baseAccounts
    }
  }

  /**
   * 获取渠道账户详情
   */
  async getChannelAccountDetail(id: string): Promise<IChannelAccountDetail> {
    logger.info('Mock: 获取渠道账户详情', { id })
    const account = this.mockAccounts.find(item => item.id === id)
    if (!account) {
      throw new Error('渠道账户不存在')
    }
    return account
  }

  /**
   * 创建渠道账户
   */
  async createChannelAccount(data: ICreateChannelAccountRequest): Promise<IChannelAccountDetail> {
    logger.info('Mock: 创建渠道账户', data)
    const now = new Date().toISOString()
    const newAccount: IChannelAccountDetail = {
      id: Math.random().toString(36).substring(2),
      code: `CH${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
      employees: [],
      createdAt: now,
      updatedAt: now,
      status: ChannelAccountStatus.ACTIVE,
      ...data
    }
    this.mockAccounts.push(newAccount)
    return newAccount
  }

  /**
   * 更新渠道账户
   */
  async updateChannelAccount(data: IUpdateChannelAccountRequest): Promise<IChannelAccountDetail> {
    logger.info('Mock: 更新渠道账户', data)
    const account = this.mockAccounts.find(item => item.id === data.id)
    if (!account) {
      throw new Error('渠道账户不存在')
    }
    
    Object.assign(account, {
      ...data,
      updatedAt: new Date().toISOString()
    })
    
    return account
  }

  /**
   * 删除渠道账户
   */
  async deleteChannelAccount(id: string): Promise<void> {
    logger.info('Mock: 删除渠道账户', { id })
    const index = this.mockAccounts.findIndex(item => item.id === id)
    if (index === -1) {
      throw new Error('渠道账户不存在')
    }
    this.mockAccounts.splice(index, 1)
  }

  /**
   * 更新渠道账户状态
   */
  async updateChannelAccountStatus(id: string, status: string): Promise<void> {
    logger.info('Mock: 更新渠道账户状态', { id, status })
    const account = this.mockAccounts.find(item => item.id === id)
    if (!account) {
      throw new Error('渠道账户不存在')
    }
    account.status = status as ChannelAccountStatus
    account.updatedAt = new Date().toISOString()
  }

  /**
   * 获取渠道员工列表
   */
  async getChannelEmployeeList(params: IChannelEmployeeQueryParams): Promise<IChannelEmployeeListResponse> {
    logger.info('Mock: 获取渠道员工列表', params)
    
    const account = this.mockAccounts.find(item => item.id === params.channelAccountId)
    if (!account) {
      throw new Error('渠道账户不存在')
    }
    
    let employees = [...account.employees]
    
    // 应用过滤条件
    if (params.status) {
      employees = employees.filter(employee => employee.status === params.status)
    }
    
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase()
      employees = employees.filter(
        employee => 
          employee.name.toLowerCase().includes(keyword) ||
          employee.jobTitle.toLowerCase().includes(keyword) ||
          employee.email.toLowerCase().includes(keyword)
      )
    }
    
    // 应用排序
    if (params.sortField) {
      const direction = params.sortOrder === 'descend' ? -1 : 1
      employees.sort((a: any, b: any) => {
        if (a[params.sortField!] < b[params.sortField!]) return -1 * direction
        if (a[params.sortField!] > b[params.sortField!]) return 1 * direction
        return 0
      })
    }
    
    // 应用分页
    const page = params.page || 1
    const pageSize = params.pageSize || 10
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const paginatedEmployees = employees.slice(start, end)
    
    return {
      total: employees.length,
      items: paginatedEmployees
    }
  }

  /**
   * 获取渠道员工详情
   */
  async getChannelEmployeeDetail(id: string): Promise<IChannelEmployee> {
    logger.info('Mock: 获取渠道员工详情', { id })
    
    for (const account of this.mockAccounts) {
      const employee = account.employees.find(emp => emp.id === id)
      if (employee) {
        return employee
      }
    }
    
    throw new Error('渠道员工不存在')
  }

  /**
   * 创建渠道员工
   */
  async createChannelEmployee(data: ICreateChannelEmployeeRequest): Promise<IChannelEmployee> {
    logger.info('Mock: 创建渠道员工', data)
    
    const account = this.mockAccounts.find(acc => acc.id === data.channelAccountId)
    if (!account) {
      throw new Error('渠道账户不存在')
    }
    
    const now = new Date().toISOString()
    const newEmployee: IChannelEmployee = {
      id: Math.random().toString(36).substring(2),
      status: 'ACTIVE',
      createdAt: now,
      updatedAt: now,
      ...data
    }
    
    account.employees.push(newEmployee)
    return newEmployee
  }

  /**
   * 更新渠道员工
   */
  async updateChannelEmployee(data: IUpdateChannelEmployeeRequest): Promise<IChannelEmployee> {
    logger.info('Mock: 更新渠道员工', data)
    
    for (const account of this.mockAccounts) {
      const employeeIndex = account.employees.findIndex(emp => emp.id === data.id)
      if (employeeIndex !== -1) {
        const employee = account.employees[employeeIndex]
        const updatedEmployee = {
          ...employee,
          ...data,
          updatedAt: new Date().toISOString()
        }
        account.employees[employeeIndex] = updatedEmployee
        return updatedEmployee
      }
    }
    
    throw new Error('渠道员工不存在')
  }

  /**
   * 删除渠道员工
   */
  async deleteChannelEmployee(id: string): Promise<void> {
    logger.info('Mock: 删除渠道员工', { id })
    
    for (const account of this.mockAccounts) {
      const employeeIndex = account.employees.findIndex(emp => emp.id === id)
      if (employeeIndex !== -1) {
        account.employees.splice(employeeIndex, 1)
        return
      }
    }
    
    throw new Error('渠道员工不存在')
  }

  /**
   * 更新渠道员工状态
   */
  async updateChannelEmployeeStatus(id: string, status: string): Promise<void> {
    logger.info('Mock: 更新渠道员工状态', { id, status })
    
    for (const account of this.mockAccounts) {
      const employee = account.employees.find(emp => emp.id === id)
      if (employee) {
        employee.status = status as 'ACTIVE' | 'INACTIVE'
        employee.updatedAt = new Date().toISOString()
        return
      }
    }
    
    throw new Error('渠道员工不存在')
  }
} 