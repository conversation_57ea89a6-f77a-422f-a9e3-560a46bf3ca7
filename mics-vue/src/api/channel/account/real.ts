/**
 * @file real.ts
 * @description 渠道账户真实服务实现
 */

import { get, post, put, del } from '@/utils/request'
import type { IChannelAccountService } from './interface'
import type {
  IChannelAccountQueryParams,
  IChannelAccountListResponse,
  IChannelAccountDetail,
  ICreateChannelAccountRequest,
  IUpdateChannelAccountRequest,
  IChannelEmployeeQueryParams,
  IChannelEmployeeListResponse,
  IChannelEmployee,
  ICreateChannelEmployeeRequest,
  IUpdateChannelEmployeeRequest
} from './types'
import { createLogger } from '@/utils/logger'

const logger = createLogger('channel-account-service')

/**
 * 真实渠道账户服务实现
 */
export class RealChannelAccountService implements IChannelAccountService {
  /**
   * 获取渠道账户列表
   */
  async getChannelAccountList(params: IChannelAccountQueryParams): Promise<IChannelAccountListResponse> {
    logger.info('获取渠道账户列表', params)
    try {
      const startTime = Date.now()
      const response = await get<IChannelAccountListResponse>('/api/channel/account/list', params)
      logger.info('获取渠道账户列表成功', {
        duration: Date.now() - startTime,
        total: response.total
      })
      return response
    } catch (error) {
      logger.error('获取渠道账户列表失败', error)
      throw error
    }
  }

  /**
   * 获取渠道账户详情
   */
  async getChannelAccountDetail(id: string): Promise<IChannelAccountDetail> {
    logger.info('获取渠道账户详情', { id })
    try {
      const startTime = Date.now()
      const response = await get<IChannelAccountDetail>(`/api/channel/account/${id}`)
      logger.info('获取渠道账户详情成功', {
        duration: Date.now() - startTime,
        id
      })
      return response
    } catch (error) {
      logger.error('获取渠道账户详情失败', error)
      throw error
    }
  }

  /**
   * 创建渠道账户
   */
  async createChannelAccount(data: ICreateChannelAccountRequest): Promise<IChannelAccountDetail> {
    logger.info('创建渠道账户', data)
    try {
      const startTime = Date.now()
      const response = await post<IChannelAccountDetail>('/api/channel/account', data)
      logger.info('创建渠道账户成功', {
        duration: Date.now() - startTime,
        id: response.id
      })
      return response
    } catch (error) {
      logger.error('创建渠道账户失败', error)
      throw error
    }
  }

  /**
   * 更新渠道账户
   */
  async updateChannelAccount(data: IUpdateChannelAccountRequest): Promise<IChannelAccountDetail> {
    logger.info('更新渠道账户', data)
    try {
      const startTime = Date.now()
      const response = await put<IChannelAccountDetail>(`/api/channel/account/${data.id}`, data)
      logger.info('更新渠道账户成功', {
        duration: Date.now() - startTime,
        id: data.id
      })
      return response
    } catch (error) {
      logger.error('更新渠道账户失败', error)
      throw error
    }
  }

  /**
   * 删除渠道账户
   */
  async deleteChannelAccount(id: string): Promise<void> {
    logger.info('删除渠道账户', { id })
    try {
      const startTime = Date.now()
      await del(`/api/channel/account/${id}`)
      logger.info('删除渠道账户成功', {
        duration: Date.now() - startTime,
        id
      })
    } catch (error) {
      logger.error('删除渠道账户失败', error)
      throw error
    }
  }

  /**
   * 更新渠道账户状态
   */
  async updateChannelAccountStatus(id: string, status: string): Promise<void> {
    logger.info('更新渠道账户状态', { id, status })
    try {
      const startTime = Date.now()
      await put(`/api/channel/account/${id}/status`, { status })
      logger.info('更新渠道账户状态成功', {
        duration: Date.now() - startTime,
        id,
        status
      })
    } catch (error) {
      logger.error('更新渠道账户状态失败', error)
      throw error
    }
  }

  /**
   * 获取渠道员工列表
   */
  async getChannelEmployeeList(params: IChannelEmployeeQueryParams): Promise<IChannelEmployeeListResponse> {
    logger.info('获取渠道员工列表', params)
    try {
      const startTime = Date.now()
      const response = await get<IChannelEmployeeListResponse>(
        `/api/channel/account/${params.channelAccountId}/employee/list`,
        params
      )
      logger.info('获取渠道员工列表成功', {
        duration: Date.now() - startTime,
        total: response.total
      })
      return response
    } catch (error) {
      logger.error('获取渠道员工列表失败', error)
      throw error
    }
  }

  /**
   * 获取渠道员工详情
   */
  async getChannelEmployeeDetail(id: string): Promise<IChannelEmployee> {
    logger.info('获取渠道员工详情', { id })
    try {
      const startTime = Date.now()
      const response = await get<IChannelEmployee>(`/api/channel/employee/${id}`)
      logger.info('获取渠道员工详情成功', {
        duration: Date.now() - startTime,
        id
      })
      return response
    } catch (error) {
      logger.error('获取渠道员工详情失败', error)
      throw error
    }
  }

  /**
   * 创建渠道员工
   */
  async createChannelEmployee(data: ICreateChannelEmployeeRequest): Promise<IChannelEmployee> {
    logger.info('创建渠道员工', data)
    try {
      const startTime = Date.now()
      const response = await post<IChannelEmployee>('/api/channel/employee', data)
      logger.info('创建渠道员工成功', {
        duration: Date.now() - startTime,
        id: response.id
      })
      return response
    } catch (error) {
      logger.error('创建渠道员工失败', error)
      throw error
    }
  }

  /**
   * 更新渠道员工
   */
  async updateChannelEmployee(data: IUpdateChannelEmployeeRequest): Promise<IChannelEmployee> {
    logger.info('更新渠道员工', data)
    try {
      const startTime = Date.now()
      const response = await put<IChannelEmployee>(`/api/channel/employee/${data.id}`, data)
      logger.info('更新渠道员工成功', {
        duration: Date.now() - startTime,
        id: data.id
      })
      return response
    } catch (error) {
      logger.error('更新渠道员工失败', error)
      throw error
    }
  }

  /**
   * 删除渠道员工
   */
  async deleteChannelEmployee(id: string): Promise<void> {
    logger.info('删除渠道员工', { id })
    try {
      const startTime = Date.now()
      await del(`/api/channel/employee/${id}`)
      logger.info('删除渠道员工成功', {
        duration: Date.now() - startTime,
        id
      })
    } catch (error) {
      logger.error('删除渠道员工失败', error)
      throw error
    }
  }

  /**
   * 更新渠道员工状态
   */
  async updateChannelEmployeeStatus(id: string, status: string): Promise<void> {
    logger.info('更新渠道员工状态', { id, status })
    try {
      const startTime = Date.now()
      await put(`/api/channel/employee/${id}/status`, { status })
      logger.info('更新渠道员工状态成功', {
        duration: Date.now() - startTime,
        id,
        status
      })
    } catch (error) {
      logger.error('更新渠道员工状态失败', error)
      throw error
    }
  }
} 