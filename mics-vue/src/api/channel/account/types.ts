/**
 * @file types.ts
 * @description 渠道账户相关的类型定义
 */

// 渠道账户状态
export enum ChannelAccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  CLOSED = 'CLOSED'
}

// 渠道类型
export enum ChannelType {
  WECHAT = 'WECHAT',
  ALIPAY = 'ALIPAY',
  UNIONPAY = 'UNIONPAY',
  JD_PAY = 'JD_PAY',
  OTHER = 'OTHER'
}

// 渠道账户基本信息
export interface IChannelAccount {
  id: string
  name: string
  code: string
  channelType: ChannelType
  status: ChannelAccountStatus
  merchantId?: string
  merchantName?: string
  appId: string
  contactName: string
  contactPhone: string
  contactEmail: string
  remarks?: string
  createdAt: string
  updatedAt: string
}

// 渠道账户详细信息
export interface IChannelAccountDetail extends IChannelAccount {
  appSecret?: string
  certInfo?: string
  configData?: Record<string, any>
  dailyLimit?: number
  monthlyLimit?: number
  singleLimit?: number
  employees: IChannelEmployee[]
}

// 渠道员工信息
export interface IChannelEmployee {
  id: string
  name: string
  jobTitle: string
  phone: string
  email: string
  status: 'ACTIVE' | 'INACTIVE'
  channelAccountId: string
  department?: string
  remarks?: string
  createdAt: string
  updatedAt: string
}

// 渠道账户查询参数
export interface IChannelAccountQueryParams {
  page?: number
  pageSize?: number
  channelType?: ChannelType
  status?: ChannelAccountStatus
  keyword?: string
  merchantId?: string
  sortField?: string
  sortOrder?: 'ascend' | 'descend'
}

// 渠道员工查询参数
export interface IChannelEmployeeQueryParams {
  page?: number
  pageSize?: number
  channelAccountId: string
  keyword?: string
  status?: 'ACTIVE' | 'INACTIVE'
  sortField?: string
  sortOrder?: 'ascend' | 'descend'
}

// 渠道账户列表响应
export interface IChannelAccountListResponse {
  total: number
  items: IChannelAccount[]
}

// 渠道员工列表响应
export interface IChannelEmployeeListResponse {
  total: number
  items: IChannelEmployee[]
}

// 创建渠道账户请求
export interface ICreateChannelAccountRequest {
  name: string
  channelType: ChannelType
  merchantId?: string
  appId: string
  appSecret?: string
  contactName: string
  contactPhone: string
  contactEmail: string
  remarks?: string
  configData?: Record<string, any>
  dailyLimit?: number
  monthlyLimit?: number
  singleLimit?: number
}

// 更新渠道账户请求
export interface IUpdateChannelAccountRequest {
  id: string
  name?: string
  merchantId?: string
  appId?: string
  appSecret?: string
  contactName?: string
  contactPhone?: string
  contactEmail?: string
  remarks?: string
  configData?: Record<string, any>
  dailyLimit?: number
  monthlyLimit?: number
  singleLimit?: number
}

// 创建渠道员工请求
export interface ICreateChannelEmployeeRequest {
  name: string
  jobTitle: string
  phone: string
  email: string
  channelAccountId: string
  department?: string
  remarks?: string
}

// 更新渠道员工请求
export interface IUpdateChannelEmployeeRequest {
  id: string
  name?: string
  jobTitle?: string
  phone?: string
  email?: string
  department?: string
  remarks?: string
} 