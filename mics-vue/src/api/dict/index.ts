/**
 * 字典管理模块API服务工厂
 * 用于在真实API和Mock API之间无缝切换
 */
import { RealDictTypeService, RealDictDataService } from './real'
import { MockDictTypeService, MockDictDataService } from './mock'
import type { IDictTypeService, IDictDataService } from './interface'

// 根据环境变量决定是否使用Mock API
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true'

/**
 * 获取字典类型服务实例
 * @returns 字典类型服务实例
 */
export function getDictTypeService(): IDictTypeService {
  return USE_MOCK
    ? new MockDictTypeService()
    : new RealDictTypeService()
}

/**
 * 获取字典数据服务实例
 * @returns 字典数据服务实例
 */
export function getDictDataService(): IDictDataService {
  return USE_MOCK
    ? new MockDictDataService()
    : new RealDictDataService()
}

// 导出公共类型供使用方使用
export * from './types'

// 导出字典管理服务接口
export type { IDictTypeService, IDictDataService } 