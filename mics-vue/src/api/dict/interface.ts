/**
 * 字典管理模块接口定义
 */
import type {
  IDictType,
  IDictData,
  IDictTypeQueryParams,
  IDictDataQueryParams,
  IDictTypeForm,
  IDictDataForm,
  IPagination
} from './types'

/**
 * 字典类型服务接口
 */
export interface IDictTypeService {
  /**
   * 获取字典类型列表
   * @param params 查询参数
   */
  getDictTypeList(params: IDictTypeQueryParams): Promise<IPagination<IDictType>>;
  
  /**
   * 添加字典类型
   * @param data 字典类型数据
   */
  addDictType(data: IDictTypeForm): Promise<void>;
  
  /**
   * 更新字典类型
   * @param data 字典类型数据
   */
  updateDictType(data: IDictTypeForm): Promise<void>;
  
  /**
   * 删除字典类型
   * @param cid 字典类型ID
   */
  deleteDictType(cid: number): Promise<void>;
}

/**
 * 字典数据服务接口
 */
export interface IDictDataService {
  /**
   * 获取字典数据列表
   * @param params 查询参数
   */
  getDictDataList(params: IDictDataQueryParams): Promise<IPagination<IDictData>>;
  
  /**
   * 添加字典数据
   * @param data 字典数据
   */
  addDictData(data: IDictDataForm): Promise<void>;
  
  /**
   * 更新字典数据
   * @param data 字典数据
   */
  updateDictData(data: IDictDataForm): Promise<void>;
  
  /**
   * 删除字典数据
   * @param cid 字典数据ID
   */
  deleteDictData(cid: number): Promise<void>;
} 