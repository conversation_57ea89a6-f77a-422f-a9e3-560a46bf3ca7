/**
 * 字典管理模块Mock数据实现
 */
import type {
  IDictType,
  IDictData,
  IDictTypeQueryParams,
  IDictDataQueryParams,
  IDictTypeForm,
  IDictDataForm,
  IPagination
} from './types'
import { DictStatus } from './types'
import type { IDictTypeService, IDictDataService } from './interface'

// 模拟字典类型数据
const mockDictTypes: IDictType[] = [
  {
    id: 1,
    dictCode: 'NORMAL',
    dictName: '正常状态',
    status: DictStatus.OPEN,
    remark: '测试添加字典类型',
    createTime: '2023-07-01 10:00:00',
    updateTime: '2023-07-01 10:00:00'
  },
  {
    id: 2,
    dictCode: 'SEX',
    dictName: '性别类型',
    status: DictStatus.OPEN,
    remark: '性别类型',
    createTime: '2023-07-02 11:00:00',
    updateTime: '2023-07-02 11:00:00'
  },
  {
    id: 3,
    dictCode: 'USER_STATUS',
    dictName: '用户状态',
    status: DictStatus.OPEN,
    remark: '用户状态',
    createTime: '2023-07-03 09:30:00',
    updateTime: '2023-07-03 09:30:00'
  },
  {
    id: 4,
    dictCode: 'PAY_METHOD',
    dictName: '支付方式',
    status: DictStatus.OPEN,
    remark: '支付方式',
    createTime: '2023-07-04 14:20:00',
    updateTime: '2023-07-04 14:20:00'
  },
  {
    id: 5,
    dictCode: 'CHANNEL_TYPE',
    dictName: '渠道类型',
    status: DictStatus.CLOSE,
    remark: '渠道类型（已禁用）',
    createTime: '2023-07-05 16:45:00',
    updateTime: '2023-07-05 16:45:00'
  }
]

// 模拟字典数据
const mockDictData: IDictData[] = [
  {
    id: 1,
    dictCode: 'SEX',
    uniqueCode: '1',
    label: '男',
    status: DictStatus.OPEN,
    remark: '性别男',
    createTime: '2023-07-02 11:30:00',
    updateTime: '2023-07-02 11:30:00'
  },
  {
    id: 2,
    dictCode: 'SEX',
    uniqueCode: '2',
    label: '女',
    status: DictStatus.OPEN,
    remark: '性别女',
    createTime: '2023-07-02 11:31:00',
    updateTime: '2023-07-02 11:31:00'
  },
  {
    id: 3,
    dictCode: 'USER_STATUS',
    uniqueCode: 'normal',
    label: '正常',
    status: DictStatus.OPEN,
    remark: '用户正常状态',
    createTime: '2023-07-03 10:00:00',
    updateTime: '2023-07-03 10:00:00'
  },
  {
    id: 4,
    dictCode: 'USER_STATUS',
    uniqueCode: 'locked',
    label: '锁定',
    status: DictStatus.OPEN,
    remark: '用户锁定状态',
    createTime: '2023-07-03 10:01:00',
    updateTime: '2023-07-03 10:01:00'
  },
  {
    id: 5,
    dictCode: 'USER_STATUS',
    uniqueCode: 'disabled',
    label: '禁用',
    status: DictStatus.OPEN,
    remark: '用户禁用状态',
    createTime: '2023-07-03 10:02:00',
    updateTime: '2023-07-03 10:02:00'
  },
  {
    id: 6,
    dictCode: 'PAY_METHOD',
    uniqueCode: 'alipay',
    label: '支付宝',
    status: DictStatus.OPEN,
    remark: '支付宝支付',
    createTime: '2023-07-04 14:30:00',
    updateTime: '2023-07-04 14:30:00'
  },
  {
    id: 7,
    dictCode: 'PAY_METHOD',
    uniqueCode: 'wechat',
    label: '微信支付',
    status: DictStatus.OPEN,
    remark: '微信支付',
    createTime: '2023-07-04 14:31:00',
    updateTime: '2023-07-04 14:31:00'
  },
  {
    id: 8,
    dictCode: 'PAY_METHOD',
    uniqueCode: 'bank',
    label: '银行卡',
    status: DictStatus.CLOSE,
    remark: '银行卡支付（已禁用）',
    createTime: '2023-07-04 14:32:00',
    updateTime: '2023-07-04 14:32:00'
  }
]

/**
 * 字典类型服务Mock实现
 */
export class MockDictTypeService implements IDictTypeService {
  private mockData = [...mockDictTypes]
  
  /**
   * 获取字典类型列表
   * @param params 查询参数
   */
  async getDictTypeList(params: IDictTypeQueryParams): Promise<IPagination<IDictType>> {
    const { dictCode, dictName, status, pageNo = 1, pageSize = 10 } = params

    let result = [...this.mockData]

    // 按条件筛选
    if (dictCode) {
      result = result.filter(item => item.dictCode.includes(dictCode))
    }
    if (dictName) {
      result = result.filter(item => item.dictName.includes(dictName))
    }
    if (status) {
      result = result.filter(item => item.status === status)
    }

    // 分页
    const total = result.length
    const start = (pageNo - 1) * pageSize
    const end = start + pageSize
    const data = result.slice(start, end)

    return Promise.resolve({
      data,
      total
    })
  }

  /**
   * 添加字典类型
   * @param data 字典类型数据
   */
  async addDictType(data: IDictTypeForm): Promise<void> {
    const newDictType = {
      id: this.mockData.length + 1,
      ...data,
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }
    this.mockData.push(newDictType as IDictType)
    return Promise.resolve()
  }

  /**
   * 更新字典类型
   * @param data 字典类型数据
   */
  async updateDictType(data: IDictTypeForm): Promise<void> {
    const index = this.mockData.findIndex(item => item.id === data.id)
    if (index !== -1) {
      this.mockData[index] = {
        ...this.mockData[index],
        ...data,
        updateTime: new Date().toLocaleString()
      }
    }
    return Promise.resolve()
  }

  /**
   * 删除字典类型
   * @param id 字典类型ID
   */
  async deleteDictType(id: number): Promise<void> {
    const index = this.mockData.findIndex(item => item.id === id)
    if (index !== -1) {
      this.mockData.splice(index, 1)
    }
    return Promise.resolve()
  }
}

/**
 * 字典数据服务Mock实现
 */
export class MockDictDataService implements IDictDataService {
  private mockData = [...mockDictData]
  
  /**
   * 获取字典数据列表
   * @param params 查询参数
   */
  async getDictDataList(params: IDictDataQueryParams): Promise<IPagination<IDictData>> {
    const { dictCode, uniqueCode, label, status, pageNo = 1, pageSize = 10 } = params

    let result = [...this.mockData]

    // 按条件筛选
    if (dictCode) {
      result = result.filter(item => item.dictCode === dictCode)
    }
    if (uniqueCode) {
      result = result.filter(item => item.uniqueCode.includes(uniqueCode))
    }
    if (label) {
      result = result.filter(item => item.label.includes(label))
    }
    if (status) {
      result = result.filter(item => item.status === status)
    }

    // 分页
    const total = result.length
    const start = (pageNo - 1) * pageSize
    const end = start + pageSize
    const data = result.slice(start, end)

    return Promise.resolve({
      data,
      total
    })
  }

  /**
   * 添加字典数据
   * @param data 字典数据
   */
  async addDictData(data: IDictDataForm): Promise<void> {
    const newDictData = {
      id: this.mockData.length + 1,
      ...data,
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    }
    this.mockData.push(newDictData as IDictData)
    return Promise.resolve()
  }

  /**
   * 更新字典数据
   * @param data 字典数据
   */
  async updateDictData(data: IDictDataForm): Promise<void> {
    const index = this.mockData.findIndex(item => item.id === data.id)
    if (index !== -1) {
      this.mockData[index] = {
        ...this.mockData[index],
        ...data,
        updateTime: new Date().toLocaleString()
      }
    }
    return Promise.resolve()
  }

  /**
   * 删除字典数据
   * @param id 字典数据ID
   */
  async deleteDictData(id: number): Promise<void> {
    const index = this.mockData.findIndex(item => item.id === id)
    if (index !== -1) {
      this.mockData.splice(index, 1)
    }
    return Promise.resolve()
  }
}

// 导出Mock数据以供其他组件直接使用
export { mockDictTypes, mockDictData } 