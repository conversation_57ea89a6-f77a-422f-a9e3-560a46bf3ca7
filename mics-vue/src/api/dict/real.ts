/**
 * 字典管理模块真实API实现
 */
import { get, post, put, del, postForm } from '@/utils/request'
import { createLogger } from '@/utils/logger'
import type {
  IDictType,
  IDictData,
  IDictTypeQueryParams,
  IDictDataQueryParams,
  IDictTypeForm,
  IDictDataForm,
  IPagination
} from './types'
import { DictStatus } from './types'
import type { IDictTypeService, IDictDataService } from './interface'

// 创建字典模块日志记录器
const logger = createLogger('DictAPI')

/**
 * 字典类型服务实现
 */
export class RealDictTypeService implements IDictTypeService {
  /**
   * 获取字典类型列表
   * @param params 查询参数
   */
  async getDictTypeList(params: IDictTypeQueryParams): Promise<IPagination<IDictType>> {
    logger.info('获取字典类型列表', { params })
    try {
      const result = await get<IPagination<IDictType>>('/api/dict_type/queryList', params)
      logger.info('获取字典类型列表成功', { count: result.total })
      return result
    } catch (error) {
      logger.error('获取字典类型列表失败', { params, error })
      throw error
    }
  }

  /**
   * 添加字典类型
   * @param data 字典类型数据
   */
  async addDictType(data: IDictTypeForm): Promise<void> {
    // 构造一个符合SysDictDataDTO的请求对象
    const requestData = {
      dictName: data.dictName,
      dictCode: data.dictCode,
      dictType: "LIST",
      status: data.status,
      remark: data.remark
    }

    logger.info('添加字典类型', requestData)
    try {
      await post<void>('/api/dict_type/add', requestData)
      logger.info('添加字典类型成功', { dictCode: data.dictCode })
    } catch (error) {
      logger.error('添加字典类型失败', { data, error })
      throw error
    }
  }

  /**
   * 更新字典类型
   * @param data 字典类型数据
   */
  async updateDictType(data: IDictTypeForm): Promise<void> {
    logger.info('更新字典类型', { cid: data.cid, dictCode: data.dictCode })
    try {
      await post<void>('/api/dict_type/modify', data)
      logger.info('更新字典类型成功', { cid: data.cid })
    } catch (error) {
      logger.error('更新字典类型失败', { data, error })
      throw error
    }
  }

  /**
   * 删除字典类型
   * @param cid 字典类型ID
   */
  async deleteDictType(cid: number): Promise<void> {
    logger.info('删除字典类型', { cid })
    try {
      await del<void>(`/api/dict_type/delete/${cid}`)
      logger.info('删除字典类型成功', { cid })
    } catch (error) {
      logger.error('删除字典类型失败', { cid, error })
      throw error
    }
  }
}

/**
 * 字典数据服务实现
 */
export class RealDictDataService implements IDictDataService {
  /**
   * 获取字典数据列表
   * @param params 查询参数
   */
  async getDictDataList(params: IDictDataQueryParams): Promise<IPagination<IDictData>> {
    logger.info('获取字典数据列表', { params })
    try {
      const result = await get<IPagination<IDictData>>('/api/dict_data/queryList', params)
      logger.info('获取字典数据列表成功', { 
        dictCode: params.dictCode, 
        count: result.total 
      })
      return result
    } catch (error) {
      logger.error('获取字典数据列表失败', { params, error })
      throw error
    }
  }

  /**
   * 添加字典数据
   * @param data 字典数据
   */
  async addDictData(data: IDictDataForm): Promise<void> {
    logger.info('添加字典数据', { 
      dictCode: data.dictCode,
      uniqueCode: data.dictCode+"_"+data.keyData,
      label: data.label,
      sort: data.sort,
      remark: data.remark
    })
    try {
      // 构造一个符合SysDictDataDTO的请求对象
      const requestData = {
        dictCode: data.dictCode,           // 字典编码
        uniqueCode: data.dictCode+"_"+data.keyData,       // 字典值编码
        label: data.label,                 // 标签
        keyData: data.keyData,          // 键值，使用uniqueCode作为keyData
        sortNo: data.sort || 0,            // 排序序号
        status: data.status,               // 状态
        comment: data.remark || ''         // 描述（对应remark字段）
      }
      
      // 发送请求到接口
      await post<void>('/api/dict_data/add', requestData)
      
      logger.info('添加字典数据成功', { 
        dictCode: data.dictCode,
        keyData: data.keyData
      })
    } catch (error) {
      logger.error('添加字典数据失败', { data, error })
      throw error
    }
  }

  /**
   * 更新字典数据
   * @param data 字典数据
   */
  async updateDictData(data: IDictDataForm): Promise<void> {
    logger.info('更新字典数据', { 
      cid: data.cid,
      dictCode: data.dictCode,
      keyData: data.keyData,
      label: data.label,
      sort: data.sort,
      remark: data.remark
    })
    try {
      // 构造一个符合SysDictDataDTO的请求对象
      const requestData = {
        cid: data.cid,                     // UUID
        dictCode: data.dictCode,           // 字典编码
        uniqueCode: data.dictCode+"_"+data.keyData,       // 字典值编码
        label: data.label,                 // 标签
        keyData: data.keyData,          // 键值，使用uniqueCode作为keyData
        sortNo: data.sort || 0,            // 排序序号
        status: data.status,               // 状态
        comment: data.remark || ''         // 描述（对应remark字段）
      }
      await post<void>('/api/dict_data/modify', requestData)
      logger.info('更新字典数据成功', { cid: data.cid })
    } catch (error) {
      logger.error('更新字典数据失败', { data, error })
      throw error
    }
  }

  /**
   * 删除字典数据
   * @param cid 字典数据ID
   */
  async deleteDictData(cid: number): Promise<void> {
    logger.info('删除字典数据', { cid })
    try {
      await del<void>(`/api/dict_data/delete/${cid}`)
      logger.info('删除字典数据成功', { cid })
    } catch (error) {
      logger.error('删除字典数据失败', { cid, error })
      throw error
    }
  }
}