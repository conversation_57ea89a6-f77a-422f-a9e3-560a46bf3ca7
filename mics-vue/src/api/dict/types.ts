/**
 * 字典管理模块类型定义
 */

/**
 * 字典类型对象
 */
export interface IDictType {
  cid: number
  dictCode: string
  dictName: string
  status: DictStatus
  remark?: string
  createTime: string
  updateTime: string
}

/**
 * 字典数据对象
 */
export interface IDictData {
  cid: number
  dictCode: string
  uniqueCode: string
  label: string
  status: DictStatus
  remark?: string
  comment?: string
  createTime: string
  updateTime: string
  sort?: number
  sortNo?: number
}

/**
 * 分页响应结构
 */
export interface IPagination<T> {
  data: T[]
  total: number
}

/**
 * 字典类型查询参数
 */
export interface IDictTypeQueryParams {
  dictCode?: string
  dictName?: string
  status?: DictStatus | null
  pageNo?: number
  pageSize?: number
}

/**
 * 字典数据查询参数
 */
export interface IDictDataQueryParams {
  dictCode?: string
  uniqueCode?: string
  label?: string
  status?: DictStatus | null
  pageNo?: number
  pageSize?: number
}

/**
 * 字典类型表单数据
 */
export interface IDictTypeForm {
  cid?: number
  dictCode: string
  dictName: string
  status: DictStatus
  remark?: string
}

/**
 * 字典数据表单数据
 */
export interface IDictDataForm {
  cid?: number
  dictCode: string
  uniqueCode: string
  label: string
  status: DictStatus
  remark?: string
  sort: number
}

// 状态枚举
export enum DictStatus {
  OPEN = 'open',
  CLOSE = 'close'
} 