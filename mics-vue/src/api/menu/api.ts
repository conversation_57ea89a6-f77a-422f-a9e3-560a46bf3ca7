/**
 * @file api.ts
 * @description 菜单真实API实现
 */

import type { MenuAPI } from './index';
import type { MenuItem } from './types';
import { request } from '@/utils/request';

export class RealMenuAPI implements MenuAPI {
  /**
   * 获取菜单列表
   * @returns Promise<MenuItem[]>
   */
  async getMenuList(): Promise<MenuItem[]> {
    const response = await request.get<MenuItem[]>('/api/v1/menus');
    return response.data;
  }

  /**
   * 获取用户权限下的菜单列表
   * @param userId 用户ID
   * @returns Promise<MenuItem[]>
   */
  async getUserMenus(userId: string): Promise<MenuItem[]> {
    const response = await request.get<MenuItem[]>(`/api/v1/users/${userId}/menus`);
    return response.data;
  }
} 