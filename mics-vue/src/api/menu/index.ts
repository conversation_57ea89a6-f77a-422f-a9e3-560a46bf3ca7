/**
 * @file index.ts
 * @description 菜单API模块入口
 */

import type { MenuItem } from './types';
import { apiConfig } from '@/config/env';
import { logger } from '@/utils/logger';

export interface MenuAPI {
  /**
   * 获取菜单列表
   * @returns Promise<MenuItem[]> 菜单列表
   */
  getMenuList(): Promise<MenuItem[]>;

  /**
   * 获取用户权限下的菜单列表
   * @param userId 用户ID
   * @returns Promise<MenuItem[]> 菜单列表
   */
  getUserMenus(userId: string): Promise<MenuItem[]>;
}

// API 实例创建工厂函数
export const createMenuAPI = async (): Promise<MenuAPI> => {
  // 使用配置中心判断是否使用Mock数据
  const useMock = apiConfig.useMock;
  logger.info('[createMenuAPI] Environment:', {
    useMock,
    baseURL: apiConfig.baseURL
  });

  try {
    if (true) {
      logger.info('[createMenuAPI] Loading MockMenuAPI...');
      const { MockMenuAPI } = await import('./mock');
      const api = new MockMenuAPI();
      logger.info('[createMenuAPI] MockMenuAPI created successfully');
      return api;
    } else {
      logger.info('[createMenuAPI] Loading RealMenuAPI...');
      const { RealMenuAPI } = await import('./api');
      const api = new RealMenuAPI();
      logger.info('[createMenuAPI] RealMenuAPI created successfully');
      return api;
    }
  } catch (error) {
    logger.error('[createMenuAPI] Error creating API:', error);
    throw error;
  }
};
