/**
 * 模拟菜单数据
 */
import type { MenuItem } from '@/api/menu/types.ts'

export const mockMenuData: MenuItem[] = [
  {
    id: 'dashboard',
    title: '仪表盘II',
    path: '/dashboard11',
    icon: 'HomeFilled'
  },
  {
    id: 'dict',
    title: '数据字典管理',
    path: '/dict-management',
    icon: 'Document'
  },
  {
    id: 'payment-scene',
    title: '支付场景管理',
    path: '/payment-scene',
    icon: 'Share',
    children: [
      {
        id: 'scene-list',
        title: '场景列表',
        path: '/payment-scene/list'
      },
      {
        id: 'scene-version',
        title: '版本历史',
        path: '/payment-scene/version'
      }
    ]
  },
  {
    id: 'condition',
    title: '条件筛选管理',
    path: '/condition',
    icon: 'Filter',
    children: [
      {
        id: 'condition-module',
        title: '条件模块',
        path: '/condition/module'
      },
      {
        id: 'condition-rule',
        title: '条件规则',
        path: '/condition/rule'
      }
    ]
  },
  {
    id: 'payment-method',
    title: '支付方式管理',
    path: '/payment-method',
    icon: 'CreditCard'
  },
  {
    id: 'channel-route',
    title: '渠道路由管理',
    path: '/channel-route',
    icon: 'Share',
    children: [
      {
        id: 'route-rules',
        title: '路由规则',
        path: '/channel-route/rules'
      },
      {
        id: 'route-version',
        title: '版本管理',
        path: '/channel-route/version'
      }
    ]
  },
  {
    id: 'feature-flags',
    title: '功能开关',
    path: '/feature-flags',
    icon: 'Setting'
  },
  {
    id: 'merchant',
    title: '商户号管理',
    path: '/merchant',
    icon: 'Collection'
  },
  {
    id: 'application',
    title: '应用管理',
    path: '/application',
    icon: 'Cellphone'
  },
  {
    id: 'channel-account',
    title: '渠道账号管理',
    path: '/channel',
    icon: 'Connection',
    children: [
      {
        id: 'account-list',
        title: '渠道账户',
        path: '/channel/account'
      },
      {
        id: 'employee-list',
        title: '渠道员工',
        path: '/channel/account/employee'
      }
    ]
  },
  {
    id: 'operation-log',
    title: '操作日志',
    path: '/operation-log',
    icon: 'Clock'
  }
]
