/**
 * @file mock.ts
 * @description 菜单Mock API实现
 */

import type { MenuAPI } from './index';
import type { MenuItem } from './types';
import { mockMenuData } from '@/api/menu/menuData.ts';

// 类型转换函数
const convertMenuItem = (item: any): MenuItem => {
  return {
    id: String(item.id), // 确保 id 是字符串
    title: item.title,
    path: item.path,
    icon: item.icon,
    children: item.children?.map(convertMenuItem),
    type: 'menu',
    sort: 0,
    hidden: false,
    meta: {
      title: item.title,
      icon: item.icon,
      keepAlive: true,
      requiresAuth: true
    }
  };
};

export class MockMenuAPI implements MenuAPI {
  /**
   * 获取菜单列表
   * @returns Promise<MenuItem[]>
   */
  async getMenuList(): Promise<MenuItem[]> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    // 转换数据类型
    return Promise.resolve(mockMenuData.map(convertMenuItem));
  }

  /**
   * 获取用户权限下的菜单列表
   * @param userId 用户ID
   * @returns Promise<MenuItem[]>
   */
  async getUserMenus(userId: string): Promise<MenuItem[]> {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    // 这里可以根据userId过滤菜单，目前直接返回所有菜单
    return Promise.resolve(mockMenuData.map(convertMenuItem));
  }
}
