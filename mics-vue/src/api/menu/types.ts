/**
 * @file types.ts
 * @description 菜单相关的类型定义
 */

// 菜单项接口
export interface MenuItem {
  id: string;
  title: string;
  path?: string;
  icon?: string;
  children?: MenuItem[];
  permission?: string; // 权限标识
  type?: 'menu' | 'button'; // 菜单类型：菜单项或按钮
  sort?: number; // 排序
  hidden?: boolean; // 是否在菜单中隐藏
  meta?: {
    title?: string;
    icon?: string;
    keepAlive?: boolean;
    requiresAuth?: boolean;
  };
}

// 菜单树接口
export interface MenuTree extends MenuItem {
  children?: MenuTree[];
}

// 菜单状态
export interface MenuState {
  activeMenu: string;
  isCollapse: boolean;
  menuList: MenuItem[];
  loading: boolean;
  error: Error | null;
} 