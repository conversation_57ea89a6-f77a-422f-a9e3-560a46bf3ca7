import { apiConfig } from '@/config/env'
import { RealMerchantService } from './real'
import { MockMerchantService } from './mock'
import type { IMerchantService } from './interface'

// 根据配置创建服务实例
const merchantService: IMerchantService = apiConfig.useMock
  ? new MockMerchantService()
  : new RealMerchantService()

// 导出所有类型
export * from './types'

// 导出服务方法
export const getMerchantList = (params: any) => merchantService.getMerchantList(params)
export const getMerchantDetail = (id: string) => merchantService.getMerchantDetail(id)
export const createMerchant = (data: any) => merchantService.createMerchant(data)
export const updateMerchant = (data: any) => merchantService.updateMerchant(data)
export const deleteMerchant = (id: string) => merchantService.deleteMerchant(id)
export const updateMerchantStatus = (id: string, status: string) => 
  merchantService.updateMerchantStatus(id, status)
export const getMerchantApplications = (merchantId: string) => 
  merchantService.getMerchantApplications(merchantId)
export const createMerchantApplication = (merchantId: string, data: any) => 
  merchantService.createMerchantApplication(merchantId, data)
export const deleteMerchantApplication = (merchantId: string, applicationId: string) => 
  merchantService.deleteMerchantApplication(merchantId, applicationId)
export const updateApplicationStatus = (merchantId: string, applicationId: string, status: string) => 
  merchantService.updateApplicationStatus(merchantId, applicationId, status) 