import type {
  IMerchantQueryParams,
  IMerchantListResponse,
  IMerchantDetail,
  ICreateMerchantRequest,
  IUpdateMerchantRequest
} from './types'

/**
 * 商户服务接口定义
 */
export interface IMerchantService {
  /**
   * 获取商户列表
   */
  getMerchantList(params: IMerchantQueryParams): Promise<IMerchantListResponse>

  /**
   * 获取商户详情
   */
  getMerchantDetail(id: string): Promise<IMerchantDetail>

  /**
   * 创建商户
   */
  createMerchant(data: ICreateMerchantRequest): Promise<IMerchantDetail>

  /**
   * 更新商户
   */
  updateMerchant(data: IUpdateMerchantRequest): Promise<IMerchantDetail>

  /**
   * 删除商户
   */
  deleteMerchant(id: string): Promise<void>

  /**
   * 更新商户状态
   */
  updateMerchantStatus(id: string, status: string): Promise<void>

  /**
   * 获取商户应用列表
   */
  getMerchantApplications(merchantId: string): Promise<any>

  /**
   * 创建商户应用
   */
  createMerchantApplication(merchantId: string, data: { name: string }): Promise<any>

  /**
   * 删除商户应用
   */
  deleteMerchantApplication(merchantId: string, applicationId: string): Promise<void>

  /**
   * 更新商户应用状态
   */
  updateApplicationStatus(merchantId: string, applicationId: string, status: string): Promise<void>
} 