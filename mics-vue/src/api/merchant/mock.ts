import type { IMerchantService } from './interface'
import type {
  IMerchantQueryParams,
  IMerchantListResponse,
  IMerchantDetail,
  ICreateMerchantRequest,
  IUpdateMerchantRequest
} from './types'
import { createLogger } from '@/utils/logger'
import { MerchantStatus } from './types'

const logger = createLogger('merchant-service-mock')

/**
 * Mock 商户服务实现
 */
export class MockMerchantService implements IMerchantService {
  private mockData: IMerchantDetail[] = [
    {
      id: '1',
      code: 'M001',
      name: '测试商户1',
      contactName: '张三',
      contactPhone: '***********',
      contactEmail: 'zhang<PERSON>@example.com',
      status: MerchantStatus.ACTIVE,
      address: '北京市朝阳区',
      legalPerson: '张三',
      legalPersonIdCard: '110101199001011234',
      bankName: '中国银行',
      bankAccount: '6222021234567890123',
      businessLicense: 'https://example.com/license1.jpg',
      remarks: '测试商户1备注',
      channelType: '微信支付',
      merchantType: '服务商',
      createdAt: '2024-03-14T10:00:00Z',
      updatedAt: '2024-03-14T10:00:00Z',
      applications: []
    },
    {
      id: '2',
      code: 'M002',
      name: '测试商户2',
      contactName: '李四',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'INACTIVE',
      address: '上海市浦东新区',
      legalPerson: '李四',
      legalPersonIdCard: '310101199001011234',
      bankName: '工商银行',
      bankAccount: '6222021234567890124',
      businessLicense: 'https://example.com/license2.jpg',
      remarks: '测试商户2备注',
      channelType: '支付宝',
      merchantType: '普通商户',
      createdAt: '2024-03-14T11:00:00Z',
      updatedAt: '2024-03-14T11:00:00Z',
      applications: []
    },
    {
      id: '3',
      code: 'M003',
      name: '测试商户3',
      contactName: '王五',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'ACTIVE',
      address: '广州市天河区',
      legalPerson: '王五',
      legalPersonIdCard: '******************',
      bankName: '农业银行',
      bankAccount: '6222021234567890125',
      businessLicense: 'https://example.com/license3.jpg',
      remarks: '测试商户3备注',
      channelType: '银联支付',
      merchantType: '特约商户',
      createdAt: '2024-03-15T10:00:00Z',
      updatedAt: '2024-03-15T10:00:00Z',
      applications: []
    },
    {
      id: '4',
      code: 'M004',
      name: '测试商户4',
      contactName: '赵六',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'ACTIVE',
      address: '深圳市南山区',
      legalPerson: '赵六',
      legalPersonIdCard: '******************',
      bankName: '建设银行',
      bankAccount: '6222021234567890126',
      businessLicense: 'https://example.com/license4.jpg',
      remarks: '测试商户4备注',
      channelType: '京东支付',
      merchantType: '服务商',
      createdAt: '2024-03-16T10:00:00Z',
      updatedAt: '2024-03-16T10:00:00Z',
      applications: []
    },
    {
      id: '5',
      code: 'M005',
      name: '测试商户5',
      contactName: '钱七',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'INACTIVE',
      address: '成都市武侯区',
      legalPerson: '钱七',
      legalPersonIdCard: '510101199001011234',
      bankName: '交通银行',
      bankAccount: '6222021234567890127',
      businessLicense: 'https://example.com/license5.jpg',
      remarks: '测试商户5备注',
      channelType: '微信支付',
      merchantType: '普通商户',
      createdAt: '2024-03-17T10:00:00Z',
      updatedAt: '2024-03-17T10:00:00Z',
      applications: []
    },
    {
      id: '6',
      code: 'M006',
      name: '测试商户6',
      contactName: '孙八',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'ACTIVE',
      address: '杭州市西湖区',
      legalPerson: '孙八',
      legalPersonIdCard: '330101199001011234',
      bankName: '招商银行',
      bankAccount: '6222021234567890128',
      businessLicense: 'https://example.com/license6.jpg',
      remarks: '测试商户6备注',
      channelType: '支付宝',
      merchantType: '特约商户',
      createdAt: '2024-03-18T10:00:00Z',
      updatedAt: '2024-03-18T10:00:00Z',
      applications: []
    },
    {
      id: '7',
      code: 'M007',
      name: '测试商户7',
      contactName: '周九',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'INACTIVE',
      address: '南京市鼓楼区',
      legalPerson: '周九',
      legalPersonIdCard: '320101199001011234',
      bankName: '浦发银行',
      bankAccount: '6222021234567890129',
      businessLicense: 'https://example.com/license7.jpg',
      remarks: '测试商户7备注',
      channelType: '银联支付',
      merchantType: '服务商',
      createdAt: '2024-03-19T10:00:00Z',
      updatedAt: '2024-03-19T10:00:00Z',
      applications: []
    },
    {
      id: '8',
      code: 'M008',
      name: '测试商户8',
      contactName: '吴十',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'ACTIVE',
      address: '武汉市江汉区',
      legalPerson: '吴十',
      legalPersonIdCard: '******************',
      bankName: '中信银行',
      bankAccount: '6222021234567890130',
      businessLicense: 'https://example.com/license8.jpg',
      remarks: '测试商户8备注',
      channelType: '京东支付',
      merchantType: '普通商户',
      createdAt: '2024-03-20T10:00:00Z',
      updatedAt: '2024-03-20T10:00:00Z',
      applications: []
    },
    {
      id: '9',
      code: 'M009',
      name: '测试商户9',
      contactName: '郑十一',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'ACTIVE',
      address: '西安市雁塔区',
      legalPerson: '郑十一',
      legalPersonIdCard: '610101199001011234',
      bankName: '光大银行',
      bankAccount: '6222021234567890131',
      businessLicense: 'https://example.com/license9.jpg',
      remarks: '测试商户9备注',
      channelType: '微信支付',
      merchantType: '特约商户',
      createdAt: '2024-03-21T10:00:00Z',
      updatedAt: '2024-03-21T10:00:00Z',
      applications: []
    },
    {
      id: '10',
      code: 'M010',
      name: '测试商户10',
      contactName: '王十二',
      contactPhone: '***********',
      contactEmail: '<EMAIL>',
      status: 'INACTIVE',
      address: '重庆市渝中区',
      legalPerson: '王十二',
      legalPersonIdCard: '500101199001011234',
      bankName: '民生银行',
      bankAccount: '6222021234567890132',
      businessLicense: 'https://example.com/license10.jpg',
      remarks: '测试商户10备注',
      channelType: '支付宝',
      merchantType: '服务商',
      createdAt: '2024-03-22T10:00:00Z',
      updatedAt: '2024-03-22T10:00:00Z',
      applications: []
    }
  ]

  async getMerchantList(params: IMerchantQueryParams): Promise<IMerchantListResponse> {
    logger.info('Mock: 获取商户列表 (入口)', { params });
    try {
      logger.debug('Mock: this.mockData (原始数据):', this.mockData);
      logger.debug('Mock: typeof this.mockData:', typeof this.mockData);
      logger.debug('Mock: Array.isArray(this.mockData):', Array.isArray(this.mockData));
      logger.debug('Mock: this.mockData.length:', this.mockData ? this.mockData.length : 'undefined');

      const { page = 1, pageSize = 10 } = params;
      logger.debug('Mock: 分页参数:', { page, pageSize });

      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      logger.debug('Mock: slice参数:', { start, end });

      if (!Array.isArray(this.mockData)) {
        logger.error('Mock: this.mockData 不是一个数组!');
        throw new Error('Mock数据源错误: mockData 不是一个数组。');
      }

      if (typeof start !== 'number' || typeof end !== 'number' || isNaN(start) || isNaN(end)) {
        logger.error('Mock: start 或 end 不是有效数字!', { start, end });
        throw new Error('Mock数据源错误: 分页参数计算错误。');
      }

      const list = this.mockData.slice(start, end);
      logger.info('Mock: 获取商户列表明细 (slice结果)', list);

      return {
        list,
        total: this.mockData.length,
        page,
        pageSize
      };
    } catch (error) {
      logger.error('Mock: 获取商户列表失败 (捕获异常)', error);
      throw error;
    }
  }

  async getMerchantDetail(id: string): Promise<IMerchantDetail> {
    logger.info('Mock: 获取商户详情', { id })
    const merchant = this.mockData.find(item => item.id === id)
    if (!merchant) {
      throw new Error('商户不存在')
    }
    return merchant
  }

  async createMerchant(data: ICreateMerchantRequest): Promise<IMerchantDetail> {
    logger.info('Mock: 创建商户', data)
    const now = new Date().toISOString()
    const newMerchant: IMerchantDetail = {
      id: Math.random().toString(36).substring(2),
      code: `M${Math.random().toString(36).substring(2, 6).toUpperCase()}`,
      applications: [],
      createdAt: now,
      updatedAt: now,
      ...data
    }
    this.mockData.push(newMerchant)
    return newMerchant
  }

  async updateMerchant(data: IUpdateMerchantRequest): Promise<IMerchantDetail> {
    logger.info('Mock: 更新商户', data)
    const index = this.mockData.findIndex(item => item.id === data.id)
    if (index === -1) {
      throw new Error('商户不存在')
    }
    this.mockData[index] = { ...this.mockData[index], ...data }
    return this.mockData[index]
  }

  async deleteMerchant(id: string): Promise<void> {
    logger.info('Mock: 删除商户', { id })
    const index = this.mockData.findIndex(item => item.id === id)
    if (index === -1) {
      throw new Error('商户不存在')
    }
    this.mockData.splice(index, 1)
  }

  async updateMerchantStatus(id: string, status: string): Promise<void> {
    logger.info('Mock: 更新商户状态', { id, status })
    const merchant = this.mockData.find(item => item.id === id)
    if (!merchant) {
      throw new Error('商户不存在')
    }
    merchant.status = status
  }

  async getMerchantApplications(merchantId: string) {
    logger.info('Mock: 获取商户应用列表', { merchantId })
    const merchant = await this.getMerchantDetail(merchantId)
    return merchant.applications
  }

  async createMerchantApplication(merchantId: string, data: { name: string }) {
    logger.info('Mock: 创建商户应用', { merchantId, ...data })
    const merchant = this.mockData.find(item => item.id === merchantId)
    if (!merchant) {
      throw new Error('商户不存在')
    }

    const newApplication = {
      id: Math.random().toString(36).substring(2),
      name: data.name,
      appId: Math.random().toString(36).substring(2).toUpperCase(),
      appSecret: Math.random().toString(36).substring(2).toUpperCase(),
      status: 'ACTIVE',
      createdAt: new Date().toISOString()
    }

    merchant.applications.push(newApplication)
    return newApplication
  }

  async deleteMerchantApplication(merchantId: string, applicationId: string) {
    logger.info('Mock: 删除商户应用', { merchantId, applicationId })
    const merchant = this.mockData.find(item => item.id === merchantId)
    if (!merchant) {
      throw new Error('商户不存在')
    }

    const index = merchant.applications.findIndex(app => app.id === applicationId)
    if (index === -1) {
      throw new Error('应用不存在')
    }

    merchant.applications.splice(index, 1)
  }

  async updateApplicationStatus(merchantId: string, applicationId: string, status: string) {
    logger.info('Mock: 更新商户应用状态', { merchantId, applicationId, status })
    const merchant = this.mockData.find(item => item.id === merchantId)
    if (!merchant) {
      throw new Error('商户不存在')
    }

    const application = merchant.applications.find(app => app.id === applicationId)
    if (!application) {
      throw new Error('应用不存在')
    }

    application.status = status
  }
}
