import { get, post, put, del } from '@/utils/request'
import type { IMerchantService } from './interface'
import type {
  IMerchantQueryParams,
  IMerchantListResponse,
  IMerchantDetail,
  ICreateMerchantRequest,
  IUpdateMerchantRequest
} from './types'
import { createLogger } from '@/utils/logger'

const logger = createLogger('merchant-service')

/**
 * 真实商户服务实现
 */
export class RealMerchantService implements IMerchantService {
  async getMerchantList(params: IMerchantQueryParams): Promise<IMerchantListResponse> {
    logger.info('获取商户列表', params)
    
    // 转换参数
    const apiParams: Record<string, any> = {
      merchantNo: params.merchantNo || '',
      uMerchantNo: params.uMerchantNo || '',
      channelCode: params.channelCode || '',
      merchantType: params.merchantType || '',
      status: params.status || '',
      pageNo: params.page || params.pageNo || 1,
      pageSize: params.pageSize || 10
    }
    
    // 如果有关键字搜索，添加到merchantNo参数中
    if (params.keyword) {
      apiParams.merchantNo = params.keyword
    }
    
    try {
      const startTime = Date.now()
      const response = await get<IMerchantListResponse>('/api/merchant/queryList', apiParams)
      logger.info('获取商户列表成功', {
        duration: Date.now() - startTime,
        total: response.total
      })
      return response
    } catch (error) {
      logger.error('获取商户列表失败', error)
      throw error
    }
  }

  async getMerchantDetail(id: string): Promise<IMerchantDetail> {
    logger.info('获取商户详情', { id })
    try {
      const startTime = Date.now()
      const response = await get<IMerchantDetail>(`/api/merchant/queryByCid`, { cid: id })
      logger.info('获取商户详情成功', {
        duration: Date.now() - startTime,
        merchantId: response.cid
      })
      return response
    } catch (error) {
      logger.error('获取商户详情失败', error)
      throw error
    }
  }

  async createMerchant(data: ICreateMerchantRequest): Promise<IMerchantDetail> {
    logger.info('创建商户', data)
    try {
      const startTime = Date.now()
      // 转换请求参数
      const requestData = {
        merchantUniqueNumber: data.merchantUniqueNumber,
        merchantNumber: data.merchantNumber,
        channelCode: data.channelCode,
        merchantType: data.merchantType,
        comment: data.comment || '',
        status: "open",                         // 状态
        channelType: "merchant",                // 渠道类型
        parentMerchantId: data.parentMerchantId || undefined,
        channelAccountId: data.channelAccountId || undefined
      }
      const response = await post<IMerchantDetail>('/api/merchant/add', requestData)
      logger.info('创建商户成功', {
        duration: Date.now() - startTime,
        merchantId: response.cid
      })
      return response
    } catch (error) {
      logger.error('创建商户失败', error)
      throw error
    }
  }

  async updateMerchant(data: IUpdateMerchantRequest): Promise<IMerchantDetail> {
    logger.info('更新商户', data)
    try {
      // 转换请求数据
      const requestData = {
        cid: data.cid,
        channelCode: data.channelCode,
        merchantType: data.merchantType,
        status: data.status,
        comment: data.comment
      }
      
      const startTime = Date.now()
      // 将HTTP方法从PUT改为POST
      await post(`/api/merchant/modify`, requestData)
      
      // 更新成功后，获取最新的商户详情
      const merchantDetail = await this.getMerchantDetail(data.cid)
      
      logger.info('更新商户成功', {
        duration: Date.now() - startTime,
        merchantId: data.cid
      })
      
      return merchantDetail
    } catch (error) {
      logger.error('更新商户失败', error)
      throw error
    }
  }

  async deleteMerchant(id: string): Promise<void> {
    logger.info('删除商户', { id })
    try {
      const startTime = Date.now()
      await del(`/api/merchant/delete/${id}`)
      logger.info('删除商户成功', {
        duration: Date.now() - startTime,
        merchantId: id
      })
    } catch (error) {
      logger.error('删除商户失败', error)
      throw error
    }
  }

  async updateMerchantStatus(id: string, status: string): Promise<void> {
    logger.info('更新商户状态', { id, status })
    try {
      const startTime = Date.now()
      
      // 构造符合接口规范的请求数据
      const requestData = {
        cid: id,
        status: status
      }
      
      // 使用POST方法，URL为/api/merchant/updStatus
      await post(`/api/merchant/updStatus`, requestData)
      
      logger.info('更新商户状态成功', {
        duration: Date.now() - startTime,
        merchantId: id,
        status
      })
    } catch (error) {
      logger.error('更新商户状态失败', error)
      throw error
    }
  }

  async getMerchantApplications(merchantId: string) {
    logger.info('获取商户应用列表', { merchantId })
    try {
      const startTime = Date.now()
      const response = await get(`/api/merchant/${merchantId}/applications`)
      logger.info('获取商户应用列表成功', {
        duration: Date.now() - startTime,
        merchantId,
        applicationCount: response.length
      })
      return response
    } catch (error) {
      logger.error('获取商户应用列表失败', error)
      throw error
    }
  }

  async createMerchantApplication(merchantId: string, data: { name: string }) {
    logger.info('创建商户应用', { merchantId, ...data })
    try {
      const startTime = Date.now()
      const response = await post(`/api/merchant/${merchantId}/applications`, data)
      logger.info('创建商户应用成功', {
        duration: Date.now() - startTime,
        merchantId,
        applicationId: response.id
      })
      return response
    } catch (error) {
      logger.error('创建商户应用失败', error)
      throw error
    }
  }

  async deleteMerchantApplication(merchantId: string, applicationId: string) {
    logger.info('删除商户应用', { merchantId, applicationId })
    try {
      const startTime = Date.now()
      await del(`/api/merchant/${merchantId}/applications/${applicationId}`)
      logger.info('删除商户应用成功', {
        duration: Date.now() - startTime,
        merchantId,
        applicationId
      })
    } catch (error) {
      logger.error('删除商户应用失败', error)
      throw error
    }
  }

  async updateApplicationStatus(merchantId: string, applicationId: string, status: string) {
    logger.info('更新商户应用状态', { merchantId, applicationId, status })
    try {
      const startTime = Date.now()
      await put(`/api/merchant/${merchantId}/applications/${applicationId}/status`, {
        status
      })
      logger.info('更新商户应用状态成功', {
        duration: Date.now() - startTime,
        merchantId,
        applicationId,
        status
      })
    } catch (error) {
      logger.error('更新商户应用状态失败', error)
      throw error
    }
  }
} 