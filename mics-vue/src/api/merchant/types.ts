// 商户状态枚举
export enum MerchantStatus {
  ACTIVE = 'open',
  INACTIVE = 'close',
  PENDING = 'pending',
  REJECTED = 'rejected'
}

// 商户类型枚举
export enum MerchantType {
  ORDINARY = 'ordinary_merchant',
  SUB = 'sub_merchant'
}

// 商户基础信息接口
export interface IMerchant {
  id?: string
  cid: string
  merchantUniqueNumber: string
  merchantNumber: string
  channelCode: string
  merchantType: string
  status: string
  channelAccountId?: number
  channelAccountCid?: string
  parentMerchantId?: number
  parentMerchantCid?: string
  comment?: string
  createTime: string
  updateTime: string
  usages?: Array<{ code: string, value: string }>
  apps?: any[]
  channelCodeName?: string
  merchantTypeName?: string
  parentMerchantNumber?: string
}

// 商户列表查询参数
export interface IMerchantQueryParams {
  merchantNo?: string
  uMerchantNo?: string
  channelCode?: string
  merchantType?: string
  status?: string
  pageNo: number
  pageSize: number
  keyword?: string
  page?: number // 兼容前端分页参数
}

// 商户列表响应
export interface IMerchantListResponse {
  total: number
  data: IMerchant[]
}

// 商户应用信息
export interface IMerchantApplication {
  id: string
  name: string
  appId: string
  appSecret: string
  status: 'ACTIVE' | 'INACTIVE'
  createdAt: string
}

// 商户详细信息
export interface IMerchantDetail extends IMerchant {
  applications: IMerchantApplication[]
  businessLicense: string
  legalPerson: string
  legalPersonIdCard: string
  bankAccount: string
  bankName: string
  remarks: string
}

// 创建商户请求
export interface ICreateMerchantRequest {
  merchantUniqueNumber: string
  merchantNumber: string
  channelCode: string
  merchantType: string
  comment?: string
  parentMerchantId?: number
  channelAccountId?: number
}

// 更新商户请求
export interface IUpdateMerchantRequest extends Partial<ICreateMerchantRequest> {
  cid: string
} 