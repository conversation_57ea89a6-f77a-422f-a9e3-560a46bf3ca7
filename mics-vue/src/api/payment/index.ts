/**
 * 支付方式管理模块API服务工厂
 * 用于在真实API和Mock API之间无缝切换
 */
import { RealPaymentMethodService } from './real';
import { MockPaymentMethodService } from './mock';
import type { IPaymentMethodService } from './interface';
import type { ICreatePaymentMethodRequest, IUpdatePaymentMethodRequest } from './types';

// 根据环境变量决定是否使用Mock API
const USE_MOCK = import.meta.env.VITE_USE_MOCK === 'true';

/**
 * 获取支付方式服务实例
 * @returns 支付方式服务实例
 */
export function getPaymentMethodService(): IPaymentMethodService {
  return USE_MOCK
    ? new MockPaymentMethodService()
    : new RealPaymentMethodService();
}

// 创建服务实例
const paymentMethodService = getPaymentMethodService();

// 导出服务方法
export const getPaymentMethodList = (params: any) => paymentMethodService.getPaymentMethodList(params);
export const getPaymentMethodDetail = (id: string) => paymentMethodService.getPaymentMethodDetail(id);
export const updatePaymentMethodStatus = (id: string, status: boolean) => paymentMethodService.updatePaymentMethodStatus(id, status);
export const deletePaymentMethod = (id: string) => paymentMethodService.deletePaymentMethod(id);
export const createPaymentMethod = (data: ICreatePaymentMethodRequest) => paymentMethodService.createPaymentMethod(data);
export const updatePaymentMethod = (data: IUpdatePaymentMethodRequest) => paymentMethodService.updatePaymentMethod(data);

// 导出公共类型供使用方使用
export * from './types';

// 导出服务接口
export type { IPaymentMethodService }; 