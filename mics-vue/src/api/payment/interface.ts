/**
 * 支付方式服务接口定义
 */
import type { 
  IPaymentMethod, 
  IPaymentMethodListResponse, 
  IPaymentMethodQueryParams,
  ICreatePaymentMethodRequest,
  IUpdatePaymentMethodRequest
} from './types';

/**
 * 支付方式服务接口
 */
interface IPaymentMethodService {
  /**
   * 获取支付方式列表
   * @param params 查询参数
   */
  getPaymentMethodList(params: IPaymentMethodQueryParams): Promise<IPaymentMethodListResponse>;
  
  /**
   * 获取支付方式详情
   * @param id 支付方式ID
   */
  getPaymentMethodDetail(id: string): Promise<IPaymentMethod>;
  
  /**
   * 更新支付方式状态
   * @param id 支付方式ID
   * @param status 新状态
   */
  updatePaymentMethodStatus(id: string, status: boolean): Promise<void>;
  
  /**
   * 删除支付方式
   * @param id 支付方式ID
   */
  deletePaymentMethod(id: string): Promise<void>;

  /**
   * 创建支付方式
   * @param data 支付方式数据
   */
  createPaymentMethod(data: ICreatePaymentMethodRequest): Promise<void>;

  /**
   * 更新支付方式
   * @param data 支付方式数据
   */
  updatePaymentMethod(data: IUpdatePaymentMethodRequest): Promise<void>;
}

export { IPaymentMethodService }; 