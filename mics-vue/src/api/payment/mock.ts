/**
 * 支付方式服务Mock实现
 */
import type { IPaymentMethodService } from './interface';
import type { 
  IPaymentMethod, 
  IPaymentMethodListResponse, 
  IPaymentMethodQueryParams,
  ICreatePaymentMethodRequest,
  IUpdatePaymentMethodRequest
} from './types';
import { createLogger } from '@/utils/logger';

const logger = createLogger('PaymentMethodMockService');

/**
 * 模拟支付方式数据
 */
const mockPaymentMethods: IPaymentMethod[] = [
  {
    id: "1",
    name: "微信支付",
    code: "weixin",
    status: "open",
    icon: "wechat-pay.svg",
    products: ["JSAPI","APP","MINI"],
    payScenes: ["支付车费","支付高速费","支付感谢费"],
    createTime: "2023-06-15 10:30:45"
  },
  {
    id: "2",
    name: "支付宝",
    code: "alipay",
    status: "open",
    icon: "alipay.svg",
    products: ["JSAPI","APP","MINI","H5"],
    payScenes: ["支付车费","支付高速费","支付感谢费"],
    createTime: "2023-06-16 14:20:32"
  },
  {
    id: "3",
    name: "云闪付",
    code: "union_pay",
    status: "open",
    icon: "union-pay.svg",
    products: ["JSAPI","APP","MINI"],
    payScenes: ["支付车费","支付高速费","支付感谢费"],
    createTime: "2023-06-20 09:15:18"
  },
  {
    id: "4",
    name: "余额支付",
    code: "wallet",
    status: "open",
    icon: "wallet.svg",
    products: ["JSAPI","APP","MINI"],
    payScenes: ["支付车费","支付高速费","支付感谢费"],
    createTime: "2023-07-01 15:42:10"
  }
];

// 支付产品映射表
const productNameMap: Record<string, string> = {
  'JSAPI': 'JSAPI',
  'APP': 'APP支付',
  'MINI': '小程序支付',
  'NATIVE': '扫码支付',
  'H5': '手机网站支付',
  'F2F': '当面付',
  'GATEWAY': '网关支付'
};

/**
 * 支付方式服务Mock实现
 */
export class MockPaymentMethodService implements IPaymentMethodService {
  /**
   * 获取支付方式列表
   */
  async getPaymentMethodList(params: IPaymentMethodQueryParams): Promise<IPaymentMethodListResponse> {
    logger.info('获取支付方式列表', params);
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 筛选数据
    let filteredList = [...mockPaymentMethods];
    
    // 关键词搜索
    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      filteredList = filteredList.filter(item => 
        item.name.toLowerCase().includes(keyword) || 
        item.code.toLowerCase().includes(keyword)
      );
    }
    
    // 状态筛选
    if (params.status) {
      filteredList = filteredList.filter(item => item.status === params.status);
    }
    
    // 支付产品筛选
    if (params.paymentProduct) {
      filteredList = filteredList.filter(item => 
        item.products.some(p => p.code === params.paymentProduct)
      );
    }
    
    // 时间筛选
    if (params.startTime) {
      filteredList = filteredList.filter(item => 
        new Date(item.createTime) >= new Date(params.startTime)
      );
    }
    
    if (params.endTime) {
      filteredList = filteredList.filter(item => 
        new Date(item.createTime) <= new Date(params.endTime)
      );
    }
    
    // 计算总数
    const total = filteredList.length;
    
    // 分页处理
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const list = filteredList.slice(start, end);
    
    return {
      list,
      total,
      page,
      pageSize
    };
  }
  
  /**
   * 获取支付方式详情
   */
  async getPaymentMethodDetail(id: string): Promise<IPaymentMethod> {
    logger.info('获取支付方式详情', { id });
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const paymentMethod = mockPaymentMethods.find(item => item.id === id);
    
    if (!paymentMethod) {
      throw new Error(`支付方式不存在: ${id}`);
    }
    
    return paymentMethod;
  }
  
  /**
   * 更新支付方式状态
   */
  async updatePaymentMethodStatus(id: string, status: boolean): Promise<void> {
    logger.info('更新支付方式状态', { id, status });
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const paymentMethod = mockPaymentMethods.find(item => item.id === id);
    
    if (!paymentMethod) {
      throw new Error(`支付方式不存在: ${id}`);
    }
    
    paymentMethod.status = status ? 'ENABLED' : 'DISABLED';
  }
  
  /**
   * 删除支付方式
   */
  async deletePaymentMethod(id: string): Promise<void> {
    logger.info('删除支付方式', { id });
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const index = mockPaymentMethods.findIndex(item => item.id === id);
    
    if (index === -1) {
      throw new Error(`支付方式不存在: ${id}`);
    }
    
    mockPaymentMethods.splice(index, 1);
  }

  /**
   * 创建支付方式
   */
  async createPaymentMethod(data: ICreatePaymentMethodRequest): Promise<void> {
    logger.info('创建支付方式', data);
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 检查编码是否已存在
    if (mockPaymentMethods.some(item => item.code === data.code)) {
      throw new Error(`支付方式编码已存在: ${data.code}`);
    }
    
    // 创建新支付方式
    const newPaymentMethod: IPaymentMethod = {
      id: String(mockPaymentMethods.length + 1),
      name: data.name,
      code: data.code,
      status: data.status,
      icon: data.icon || '/images/default-payment.png',
      products: data.products.map(code => ({
        code,
        name: productNameMap[code] || code
      })),
      sceneCount: 0,
      channelCount: 0,
      createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      comment: data.comment
    };
    
    mockPaymentMethods.push(newPaymentMethod);
  }

  /**
   * 更新支付方式
   */
  async updatePaymentMethod(data: IUpdatePaymentMethodRequest): Promise<void> {
    logger.info('更新支付方式', data);
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const paymentMethod = mockPaymentMethods.find(item => item.id === data.id);
    
    if (!paymentMethod) {
      throw new Error(`支付方式不存在: ${data.id}`);
    }
    
    // 更新支付方式
    paymentMethod.name = data.name;
    paymentMethod.status = data.status;
    paymentMethod.icon = data.icon || paymentMethod.icon;
    paymentMethod.products = data.products.map(code => ({
      code,
      name: productNameMap[code] || code
    }));
    paymentMethod.comment = data.comment;
  }
} 