/**
 * 支付方式服务真实API实现
 */
import type { IPaymentMethodService } from './interface';
import type { 
  IPaymentMethod, 
  IPaymentMethodListResponse, 
  IPaymentMethodQueryParams,
  ICreatePaymentMethodRequest,
  IUpdatePaymentMethodRequest
} from './types';
import { createLogger } from '@/utils/logger';
import { get, post, put, del } from '@/utils/request';

const logger = createLogger('PaymentMethodRealService');

/**
 * 支付方式服务真实API实现
 */
export class RealPaymentMethodService implements IPaymentMethodService {
  /**
   * 获取支付方式列表
   */
  async getPaymentMethodList(params: IPaymentMethodQueryParams): Promise<IPaymentMethodListResponse> {
    logger.info('获取支付方式列表', params);
    return get<IPaymentMethodListResponse>('/api/payment/methods', params);
  }
  
  /**
   * 获取支付方式详情
   */
  async getPaymentMethodDetail(id: string): Promise<IPaymentMethod> {
    logger.info('获取支付方式详情', { id });
    return get<IPaymentMethod>(`/api/payment/methods/${id}`);
  }
  
  /**
   * 更新支付方式状态
   */
  async updatePaymentMethodStatus(id: string, status: boolean): Promise<void> {
    logger.info('更新支付方式状态', { id, status });
    await put(`/api/payment/methods/${id}/status`, { status: status ? 'ENABLED' : 'DISABLED' });
  }
  
  /**
   * 删除支付方式
   */
  async deletePaymentMethod(id: string): Promise<void> {
    logger.info('删除支付方式', { id });
    await del(`/api/payment/methods/${id}`);
  }

  /**
   * 创建支付方式
   */
  async createPaymentMethod(data: ICreatePaymentMethodRequest): Promise<void> {
    logger.info('创建支付方式', data);
    await post('/api/payment/methods', data);
  }

  /**
   * 更新支付方式
   */
  async updatePaymentMethod(data: IUpdatePaymentMethodRequest): Promise<void> {
    logger.info('更新支付方式', data);
    await put(`/api/payment/methods/${data.id}`, data);
  }
} 