/**
 * @file index.ts
 * @description 支付场景服务工厂与导出
 */

import { apiConfig } from '@/config/env'
import type IPaymentSceneService from './interface'
import { MockPaymentSceneService } from './mock'
import { RealPaymentSceneService } from './real'
import type { 
  PaymentSceneStatus, 
  IPaymentSceneQueryParams, 
  ICreatePaymentSceneRequest, 
  IUpdatePaymentSceneRequest 
} from './types'

// 根据配置决定使用mock数据还是真实API
const paymentSceneService: IPaymentSceneService = apiConfig.useMock
  ? new MockPaymentSceneService()
  : new RealPaymentSceneService()

// 导出服务方法，而不是具体实现
export const getPaymentSceneList = (params: IPaymentSceneQueryParams) => paymentSceneService.getPaymentSceneList(params)
export const getPaymentSceneDetail = (id: string) => paymentSceneService.getPaymentSceneDetail(id)
export const createPaymentScene = (data: ICreatePaymentSceneRequest) => paymentSceneService.createPaymentScene(data)
export const updatePaymentScene = (data: IUpdatePaymentSceneRequest) => paymentSceneService.updatePaymentScene(data)
export const deletePaymentScene = (id: string) => paymentSceneService.deletePaymentScene(id)
export const updatePaymentSceneStatus = (id: string, status: PaymentSceneStatus) => paymentSceneService.updatePaymentSceneStatus(id, status)

// 重新导出类型
export * from './types' 