/**
 * @file interface.ts
 * @description 支付场景服务接口定义
 */

import type {
  IPaymentSceneQueryParams,
  IPaymentSceneListResponse,
  IPaymentScene,
  ICreatePaymentSceneRequest,
  IUpdatePaymentSceneRequest,
  PaymentSceneStatus
} from './types'

/**
 * 支付场景服务接口
 */
export interface IPaymentSceneService {
  /**
   * 获取支付场景列表
   */
  getPaymentSceneList(params: IPaymentSceneQueryParams): Promise<IPaymentSceneListResponse>

  /**
   * 获取支付场景详情
   */
  getPaymentSceneDetail(id: string): Promise<IPaymentScene>

  /**
   * 创建支付场景
   */
  createPaymentScene(data: ICreatePaymentSceneRequest): Promise<IPaymentScene>

  /**
   * 更新支付场景
   */
  updatePaymentScene(data: IUpdatePaymentSceneRequest): Promise<IPaymentScene>

  /**
   * 删除支付场景
   */
  deletePaymentScene(id: string): Promise<void>

  /**
   * 更新支付场景状态
   */
  updatePaymentSceneStatus(id: string, status: PaymentSceneStatus): Promise<void>
}

// 默认导出接口，增加兼容性
export default IPaymentSceneService 