/**
 * @file mock.ts
 * @description 支付场景服务Mock实现
 */

import type IPaymentSceneService from './interface'
import type {
  IPaymentSceneQueryParams,
  IPaymentSceneListResponse,
  IPaymentScene,
  ICreatePaymentSceneRequest,
  IUpdatePaymentSceneRequest,
  PaymentSceneStatus
} from './types'

/**
 * 支付场景服务Mock实现
 */
export class MockPaymentSceneService implements IPaymentSceneService {
  // 模拟数据
  private mockData: IPaymentScene[] = [
    {
        id: "1",
        name: "顺风车车费场景",
        status: "published",
        payment_list: [
            "weixin",
            "alipay",
            "douyin"
        ],
        createTime: "2025-04-15 10:30:45"
    },
    {
        id: "2",
        name: "出租车车费场景",
        status: "published",
        payment_list: [
            "weixin",
            "alipay",
            "douyin"
        ],
        createTime: "2025-04-15 10:30:45"
    },
    {
        id: "3",
        name: "顺风车高速费",
        status: "published",
        payment_list: [
            "weixin",
            "alipay"
        ],
        createTime: "2025-04-15 10:30:45"
    },
    {
        id: "4",
        name: "顺风车感谢费",
        status: "published",
        payment_list: [
            "weixin",
            "alipay"
        ],
        createTime: "2025-04-15 10:30:45"
    }
]

  /**
   * 获取支付场景列表
   */
  async getPaymentSceneList(params: IPaymentSceneQueryParams): Promise<IPaymentSceneListResponse> {
    // 默认分页参数
    const { page = 1, pageSize = 10, keyword, status, sceneType } = params
    
    // 过滤数据
    let filteredData = [...this.mockData]
    
    // 按关键字过滤
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase()
      filteredData = filteredData.filter(
        item => item.id.toLowerCase().includes(lowerKeyword) || 
                item.name.toLowerCase().includes(lowerKeyword)
      )
    }
    
    // 按状态过滤
    if (status) {
      filteredData = filteredData.filter(item => item.status === status)
    }
    
    // 按场景类型过滤
    if (sceneType) {
      filteredData = filteredData.filter(item => item.sceneType === sceneType)
    }
    
    // 计算分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedData = filteredData.slice(startIndex, endIndex)
    
    // 返回分页结果
    return {
      list: paginatedData,
      total: filteredData.length,
      page,
      pageSize
    }
  }

  /**
   * 获取支付场景详情
   */
  async getPaymentSceneDetail(id: string): Promise<IPaymentScene> {
    const scene = this.mockData.find(item => item.id === id)
    
    if (!scene) {
      throw new Error(`支付场景不存在: ${id}`)
    }
    
    return { ...scene }
  }

  /**
   * 创建支付场景
   */
  async createPaymentScene(data: ICreatePaymentSceneRequest): Promise<IPaymentScene> {
    // 生成新ID (简单实现，实际可能更复杂)
    const newId = `PS${String(this.mockData.length + 1).padStart(3, '0')}`
    
    // 当前时间
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19)
    
    // 创建新场景
    const newScene: IPaymentScene = {
      id: newId,
      name: data.name,
      status: data.status || 'DRAFT',
      sceneType: data.sceneType,
      createTime: now,
      updateTime: now,
      description: data.description
    }
    
    // 添加到模拟数据
    this.mockData.push(newScene)
    
    return { ...newScene }
  }

  /**
   * 更新支付场景
   */
  async updatePaymentScene(data: IUpdatePaymentSceneRequest): Promise<IPaymentScene> {
    const index = this.mockData.findIndex(item => item.id === data.id)
    
    if (index === -1) {
      throw new Error(`支付场景不存在: ${data.id}`)
    }
    
    // 当前时间
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19)
    
    // 更新场景
    const updatedScene: IPaymentScene = {
      ...this.mockData[index],
      name: data.name,
      status: data.status || this.mockData[index].status,
      sceneType: data.sceneType || this.mockData[index].sceneType,
      description: data.description !== undefined ? data.description : this.mockData[index].description,
      updateTime: now
    }
    
    // 更新模拟数据
    this.mockData[index] = updatedScene
    
    return { ...updatedScene }
  }

  /**
   * 删除支付场景
   */
  async deletePaymentScene(id: string): Promise<void> {
    const index = this.mockData.findIndex(item => item.id === id)
    
    if (index === -1) {
      throw new Error(`支付场景不存在: ${id}`)
    }
    
    // 从模拟数据中删除
    this.mockData.splice(index, 1)
  }

  /**
   * 更新支付场景状态
   */
  async updatePaymentSceneStatus(id: string, status: PaymentSceneStatus): Promise<void> {
    const index = this.mockData.findIndex(item => item.id === id)
    
    if (index === -1) {
      throw new Error(`支付场景不存在: ${id}`)
    }
    
    // 当前时间
    const now = new Date().toISOString().replace('T', ' ').substring(0, 19)
    
    // 更新状态和更新时间
    this.mockData[index] = {
      ...this.mockData[index],
      status,
      updateTime: now
    }
  }
} 