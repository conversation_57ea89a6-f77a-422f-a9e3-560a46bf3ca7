/**
 * @file real.ts
 * @description 支付场景服务真实API实现
 */

import type IPaymentSceneService from './interface'
import type {
  IPaymentSceneQueryParams,
  IPaymentSceneListResponse,
  IPaymentScene,
  ICreatePaymentSceneRequest,
  IUpdatePaymentSceneRequest,
  PaymentSceneStatus
} from './types'
import { get, post, put, del } from '@/utils/request'

/**
 * 支付场景服务真实API实现
 */
export class RealPaymentSceneService implements IPaymentSceneService {
  /**
   * 获取支付场景列表
   */
  async getPaymentSceneList(params: IPaymentSceneQueryParams): Promise<IPaymentSceneListResponse> {
    return post<IPaymentSceneListResponse>('/api/payment/scene/list', params)
  }

  /**
   * 获取支付场景详情
   */
  async getPaymentSceneDetail(id: string): Promise<IPaymentScene> {
    return get<IPaymentScene>(`/api/payment/scene/detail/${id}`)
  }

  /**
   * 创建支付场景
   */
  async createPaymentScene(data: ICreatePaymentSceneRequest): Promise<IPaymentScene> {
    return post<IPaymentScene>('/api/payment/scene/create', data)
  }

  /**
   * 更新支付场景
   */
  async updatePaymentScene(data: IUpdatePaymentSceneRequest): Promise<IPaymentScene> {
    return put<IPaymentScene>('/api/payment/scene/update', data)
  }

  /**
   * 删除支付场景
   */
  async deletePaymentScene(id: string): Promise<void> {
    return del<void>(`/api/payment/scene/delete/${id}`)
  }

  /**
   * 更新支付场景状态
   */
  async updatePaymentSceneStatus(id: string, status: PaymentSceneStatus): Promise<void> {
    return put<void>('/api/payment/scene/status', { id, status })
  }
}
