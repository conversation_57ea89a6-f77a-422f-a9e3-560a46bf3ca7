/**
 * 支付场景管理相关类型定义
 */

/**
 * 支付场景状态枚举
 */
export const PaymentSceneStatusMap = {
  PUBLISHED: '已发布',
  DRAFT: '草稿',
  DISABLED: '已禁用'
} as const;

export type PaymentSceneStatus = keyof typeof PaymentSceneStatusMap;

/**
 * 支付场景类型枚举
 */
export const PaymentSceneTypeMap = {
  ONLINE: '线上场景',
  OFFLINE: '线下场景',
  APP: 'APP内'
} as const;

export type PaymentSceneType = keyof typeof PaymentSceneTypeMap;

/**
 * 支付场景查询参数
 */
export interface IPaymentSceneQueryParams {
  keyword?: string;
  status?: PaymentSceneStatus;
  sceneType?: PaymentSceneType;
  page?: number;
  pageSize?: number;
}

/**
 * 支付场景详情
 */
export interface IPaymentScene {
  id: string;
  name: string;
  status: PaymentSceneStatus;
  payment_list: string[];
  createTime: string;
  sceneType?: PaymentSceneType;
  updateTime?: string;
  description?: string;
}

/**
 * 支付场景列表响应
 */
export interface IPaymentSceneListResponse {
  list: IPaymentScene[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 创建支付场景请求
 */
export interface ICreatePaymentSceneRequest {
  name: string;
  sceneType?: PaymentSceneType;
  status?: PaymentSceneStatus;
  description?: string;
}

/**
 * 更新支付场景请求
 */
export interface IUpdatePaymentSceneRequest {
  id: string;
  name: string;
  sceneType?: PaymentSceneType;
  status?: PaymentSceneStatus;
  description?: string;
}

/**
 * 更新支付场景状态请求
 */
export interface IUpdatePaymentSceneStatusRequest {
  id: string;
  status: PaymentSceneStatus;
}