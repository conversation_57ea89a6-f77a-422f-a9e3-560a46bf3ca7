/**
 * 支付方式管理相关类型定义
 */

/**
 * 支付方式状态枚举
 */
export const PaymentMethodStatusMap = {
  ENABLED: '启用',
  DISABLED: '禁用'
} as const;

export type PaymentMethodStatus = keyof typeof PaymentMethodStatusMap;

/**
 * 支付方式查询参数
 */
export interface IPaymentMethodQueryParams {
  keyword?: string;
  status?: PaymentMethodStatus;
  paymentProduct?: string;
  startTime?: string;
  endTime?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 支付产品类型
 */
export interface IPaymentProduct {
  code: string;
  name: string;
}

/**
 * 支付方式详情
 */
export interface IPaymentMethod {
  id: string;
  name: string;
  code: string;
  status: PaymentMethodStatus;
  icon?: string;
  products: IPaymentProduct[];
  sceneCount: number;
  channelCount: number;
  createTime: string;
  comment?: string;
}

/**
 * 支付方式列表响应
 */
export interface IPaymentMethodListResponse {
  list: IPaymentMethod[];
  total: number;
  page: number;
  pageSize: number;
}

/**
 * 创建支付方式请求
 */
export interface ICreatePaymentMethodRequest {
  name: string;
  code: string;
  icon?: string;
  status: PaymentMethodStatus;
  products: string[];
  comment?: string;
}

/**
 * 更新支付方式请求
 */
export interface IUpdatePaymentMethodRequest {
  id: string;
  name: string;
  code: string;
  icon?: string;
  status: PaymentMethodStatus;
  products: string[];
  comment?: string;
} 