/* Tailwind CSS Directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Element Plus Styles (确保在 main.ts 中也引入了 JS 部分) */
/* 如果需要全局引入 Element Plus 样式，可以在这里 @import 'element-plus/dist/index.css'; */
/* 或者在 main.ts 中引入，通常后者更好，以配合按需引入组件 */

/* Font Awesome Styles (如果使用 CSS 版本) */
/* 通常 Font Awesome Vue 组件会自动处理样式，但如果需要全局 CSS，可以在这里引入 */

/* Global custom styles can go here */
body {
  font-family: 'Noto Sans SC', sans-serif; /* 根据 02_technology_stack.md 推荐字体 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} 