<template>
  <div class="glossary-list">
    <div class="glossary-search">
      <el-input
        v-model="searchTerm"
        placeholder="搜索术语"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-select
        v-if="showCategoryFilter"
        v-model="selectedCategory"
        placeholder="选择分类"
        clearable
      >
        <el-option
          v-for="category in categories"
          :key="category"
          :label="category"
          :value="category"
        />
      </el-select>
    </div>
    
    <el-empty v-if="filteredTerms.length === 0" description="没有找到匹配的术语" />
    
    <el-collapse v-else>
      <el-collapse-item
        v-for="term in filteredTerms"
        :key="term.id"
        :title="term.term"
        :name="term.id"
      >
        <div class="term-definition">{{ term.definition }}</div>
        <div v-if="term.category" class="term-category">
          分类: <el-tag size="small">{{ term.category }}</el-tag>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElInput, ElSelect, ElOption, ElCollapse, ElCollapseItem, ElTag, ElEmpty, ElIcon } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { getAllTerms, GlossaryTerm } from '@/utils/glossary';

const props = defineProps({
  // 是否显示分类筛选
  showCategoryFilter: {
    type: Boolean,
    default: true
  },
  // 初始分类
  initialCategory: {
    type: String,
    default: ''
  }
});

// 所有术语
const allTerms = ref<GlossaryTerm[]>([]);
// 搜索关键词
const searchTerm = ref('');
// 选中的分类
const selectedCategory = ref(props.initialCategory);

// 获取所有分类
const categories = computed(() => {
  const categorySet = new Set<string>();
  allTerms.value.forEach(term => {
    if (term.category) {
      categorySet.add(term.category);
    }
  });
  return Array.from(categorySet);
});

// 过滤后的术语列表
const filteredTerms = computed(() => {
  return allTerms.value.filter(term => {
    // 分类筛选
    if (selectedCategory.value && term.category !== selectedCategory.value) {
      return false;
    }
    
    // 关键词搜索
    if (searchTerm.value) {
      const keyword = searchTerm.value.toLowerCase();
      return term.term.toLowerCase().includes(keyword) || 
             term.definition.toLowerCase().includes(keyword);
    }
    
    return true;
  });
});

// 初始化
onMounted(() => {
  allTerms.value = getAllTerms();
});
</script>

<style scoped>
.glossary-list {
  width: 100%;
}

.glossary-search {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.term-definition {
  margin-bottom: 8px;
  line-height: 1.6;
}

.term-category {
  font-size: 12px;
  color: #909399;
}
</style> 