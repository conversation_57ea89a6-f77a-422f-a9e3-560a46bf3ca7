<template>
  <el-tooltip
    :content="termDefinition"
    :effect="effect"
    :placement="placement"
    :trigger="trigger"
    :popper-class="popperClass"
  >
    <span class="glossary-term">
      <slot>{{ term }}</slot>
      <el-icon class="glossary-icon" v-if="showIcon"><QuestionFilled /></el-icon>
    </span>
  </el-tooltip>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElTooltip, ElIcon } from 'element-plus';
import { QuestionFilled } from '@element-plus/icons-vue';
import { getTermByName, getTermById } from '@/utils/glossary';

const props = defineProps({
  // 术语名称
  term: {
    type: String,
    required: false,
    default: ''
  },
  // 术语ID
  termId: {
    type: String,
    required: false,
    default: ''
  },
  // 自定义解释（覆盖默认解释）
  customDefinition: {
    type: String,
    required: false,
    default: ''
  },
  // tooltip效果
  effect: {
    type: String,
    default: 'dark'
  },
  // tooltip位置
  placement: {
    type: String,
    default: 'top'
  },
  // 触发方式
  trigger: {
    type: String,
    default: 'hover'
  },
  // 是否显示问号图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 自定义popper类名
  popperClass: {
    type: String,
    default: 'glossary-tooltip'
  }
});

// 计算术语解释
const termDefinition = computed(() => {
  // 优先使用自定义解释
  if (props.customDefinition) {
    return props.customDefinition;
  }
  
  // 根据ID或名称查找术语
  let glossaryTerm;
  if (props.termId) {
    glossaryTerm = getTermById(props.termId);
  } else if (props.term) {
    glossaryTerm = getTermByName(props.term);
  }
  
  return glossaryTerm?.definition || '暂无解释';
});
</script>

<style scoped>
.glossary-term {
  border-bottom: 1px dashed #909399;
  cursor: help;
  display: inline-flex;
  align-items: center;
}

.glossary-icon {
  margin-left: 4px;
  font-size: 14px;
  color: #909399;
}

:deep(.glossary-tooltip) {
  max-width: 300px;
  line-height: 1.5;
}
</style> 