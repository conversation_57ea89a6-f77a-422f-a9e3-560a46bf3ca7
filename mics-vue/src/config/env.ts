/**
 * @file env.ts
 * @description 环境配置中心
 */

import { logger } from '@/utils/logger';

// 环境判断
export const isDev = import.meta.env.DEV;
export const isProd = import.meta.env.PROD;
export const mode = import.meta.env.MODE;

// 验证必要的环境变量
function validateEnv() {
  const requiredVars = ['VITE_API_BASE_URL', 'VITE_USE_MOCK'];

  for (const key of requiredVars) {
    if (import.meta.env[key] === undefined) {
      // 记录错误但不阻止应用启动
      logger.error(`环境变量 ${key} 未定义！`);
    }
  }
}

// 尝试转换为数字
function toNumber(value: string | undefined, defaultValue: number): number {
  if (value === undefined) return defaultValue;
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

// 尝试转换为布尔值
function toBoolean(value: string | undefined, defaultValue: boolean): boolean {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
}

// 尝试转换为数组
function toArray<T>(value: string | undefined, defaultValue: T[], mapper: (item: string) => T): T[] {
  if (value === undefined) return defaultValue;
  return value.split(',').map(mapper);
}

// API配置
export const apiConfig = {
  // API基础路径 - 这里不设置，依赖Vite的proxy配置
  baseURL: '',

  // 是否使用mock数据
  useMock: toBoolean(import.meta.env.VITE_USE_MOCK, isDev),

  // 超时时间
  timeout: toNumber(import.meta.env.VITE_API_TIMEOUT, 15000),

  // 请求重试次数
  retries: toNumber(import.meta.env.VITE_API_RETRIES, 3)
};

// 系统配置
export const systemConfig = {
  // 系统名称
  title: import.meta.env.VITE_APP_TITLE || 'MICS支付管理系统',

  // 系统版本
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',

  // 版权信息
  copyright: import.meta.env.VITE_APP_COPYRIGHT || '© 2025 MICS. All rights reserved.',

  // 默认分页大小
  defaultPageSize: toNumber(import.meta.env.VITE_DEFAULT_PAGE_SIZE, 10),

  // 分页选项
  pageSizes: toArray(import.meta.env.VITE_PAGE_SIZES, [10, 20, 50, 100], Number)
};

// 初始验证
validateEnv();

// 导出统一配置对象
export const config = {
  env: {
    isDev,
    isProd,
    mode
  },
  api: apiConfig,
  system: systemConfig
};

// 默认导出完整配置
export default config;
