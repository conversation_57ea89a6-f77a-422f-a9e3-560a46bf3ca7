import type { MenuItem } from '@/api/menu/types'

/**
 * 系统菜单配置
 */
export const menuConfig: MenuItem[] = [
  {
    id: 'dashboard',
    title: '仪表盘',
    icon: 'Odometer',
    path: '/dashboard'
  },
  {
    id: 'merchant',
    title: '商户管理',
    icon: 'Shop',
    path: '/merchant'
  },
  {
    id: 'payment',
    title: '支付管理',
    icon: 'Money',
    children: [
      {
        id: 'payment-method',
        title: '支付方式',
        path: '/payment/method'
      },
      {
        id: 'payment-scene',
        title: '支付场景',
        path: '/payment/scene'
      }
    ]
  },
  {
    id: 'channel',
    title: '渠道管理',
    icon: 'Connection',
    path: '/channel',
    children: [
      {
        id: 'channel-account',
        title: '渠道账户',
        path: '/channel/account'
      },
      {
        id: 'channel-route',
        title: '渠道路由',
        path: '/channel/route'
      }
    ]
  },
  {
    id: 'system',
    title: '系统管理',
    icon: 'Setting',
    children: [
      {
        id: 'system-role',
        title: '角色管理',
        path: '/system/role'
      },
      {
        id: 'system-user',
        title: '用户管理',
        path: '/system/user'
      },
      {
        id: 'system-log',
        title: '操作日志',
        path: '/system/log'
      }
    ]
  }
] 