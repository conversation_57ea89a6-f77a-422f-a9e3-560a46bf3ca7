// import './assets/main.css' // create-vue 默认的 css，我们用 scss
import '@/assets/styles/main.scss' // 引入全局 SCSS

import { createApp } from 'vue'
import { createPinia } from 'pinia'

// 配置验证
import validateEnvironment from '@/utils/envValidator'
import { config, systemConfig } from '@/config/env'
import { logger } from '@/utils/logger'

// 验证环境变量
validateEnvironment()

// 记录环境信息
logger.info(`应用启动: ${systemConfig.title} v${systemConfig.version}`)
logger.info(`运行环境: ${config.env.mode}`)

// Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css' // Element Plus 样式
// 如果需要 Element Plus 的暗黑主题，可以引入 import 'element-plus/theme-chalk/dark/css-vars.css'

// Font Awesome
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { fas } from '@fortawesome/free-solid-svg-icons' // 引入所有 solid 图标，可按需引入特定图标
// import { fab } from '@fortawesome/free-brands-svg-icons'; // 如果需要 brands 图标
// import { far } from '@fortawesome/free-regular-svg-icons'; // 如果需要 regular 图标

library.add(fas) // 将图标添加到库中
// library.add(fab);
// library.add(far);

import App from './App.vue'
import router from './router'
import GlossaryTooltip from '@/components/common/GlossaryTooltip.vue'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ElementPlus) // 注册 Element Plus
app.component('font-awesome-icon', FontAwesomeIcon) // 注册 Font Awesome 组件

// 注册全局组件
app.component('GlossaryTooltip', GlossaryTooltip)

app.mount('#app')
