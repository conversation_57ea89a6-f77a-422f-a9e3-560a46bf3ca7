/**
 * @file menu.ts
 * @description 菜单状态管理
 */

import { defineStore } from 'pinia';
import type { MenuItem, MenuState } from '@/api/types/menu';
import { createMenuAPI } from '@/api/menu';

export const useMenuStore = defineStore('menu', {
  state: (): MenuState => ({
    menuItems: [],
    loading: false,
    error: null,
  }),

  getters: {
    /**
     * 获取展平的菜单列表（用于路由匹配）
     */
    flatMenuItems(): MenuItem[] {
      const flatten = (items: MenuItem[]): MenuItem[] => {
        return items.reduce((acc: MenuItem[], item) => {
          if (item.children) {
            return [...acc, item, ...flatten(item.children)];
          }
          return [...acc, item];
        }, []);
      };
      return flatten(this.menuItems);
    },

    /**
     * 获取有权限的菜单列表
     */
    authorizedMenuItems(): MenuItem[] {
      console.log('Current menuItems:', this.menuItems);
      return this.menuItems;
    },
  },

  actions: {
    /**
     * 获取菜单列表
     */
    async fetchMenuItems() {
      this.loading = true;
      this.error = null;

      try {
        const menuAPI = await createMenuAPI();
        console.log('Menu API created:', menuAPI);
        const items = await menuAPI.getMenuList();
        console.log('Fetched menu items:', items);
        this.menuItems = items;
      } catch (err) {
        console.error('Failed to fetch menu items:', err);
        this.error = err as Error;
        throw err;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取用户权限下的菜单列表
     * @param userId 用户ID
     */
    async fetchUserMenus(userId: string) {
      this.loading = true;
      this.error = null;

      try {
        const menuAPI = await createMenuAPI();
        const items = await menuAPI.getUserMenus(userId);
        console.log('Fetched user menus:', items);
        this.menuItems = items;
      } catch (err) {
        console.error('Failed to fetch user menus:', err);
        this.error = err as Error;
        throw err;
      } finally {
        this.loading = false;
      }
    },

    /**
     * 重置状态
     */
    reset() {
      this.menuItems = [];
      this.loading = false;
      this.error = null;
    },
  },
}); 