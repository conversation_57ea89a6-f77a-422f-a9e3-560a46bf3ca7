/**
 * 全局字典工具
 * 用于在应用中方便地获取和使用字典数据
 */
import { ref, reactive, computed } from 'vue'
import { getDictDataService } from '@/api/dict'
import type { IDictData } from '@/api/dict'

// 字典数据服务
const dictDataService = getDictDataService()

// 缓存的字典数据
const dictCache = reactive<Record<string, IDictData[]>>({})

// 字典数据加载状态
const loadingDict = ref<Record<string, boolean>>({})

// 加载错误记录
const dictErrors = reactive<Record<string, string>>({})

/**
 * 加载字典数据
 * @param dictCode 字典编码
 * @returns 加载后的字典数据
 */
export async function loadDict(dictCode: string): Promise<IDictData[]> {
  // 如果已经加载过，直接返回缓存
  if (dictCache[dictCode]) {
    return dictCache[dictCode]
  }

  // 设置加载状态
  loadingDict.value[dictCode] = true
  
  try {
    // 获取字典数据
    const result = await dictDataService.getDictDataList({
      dictCode,
      pageNo: 1,
      pageSize: 1000, // 获取足够多的字典项
      status: 'open' // 只获取启用状态的
    })
    
    // 缓存字典数据
    dictCache[dictCode] = result.data
    
    // 清除错误
    if (dictErrors[dictCode]) {
      delete dictErrors[dictCode]
    }
    
    return result.data
  } catch (error) {
    // 记录错误
    dictErrors[dictCode] = `加载字典[${dictCode}]失败: ${error}`
    console.error(`加载字典[${dictCode}]失败`, error)
    return []
  } finally {
    // 清除加载状态
    loadingDict.value[dictCode] = false
  }
}

/**
 * 根据编码获取字典项标签
 * @param dictCode 字典编码
 * @param uniqueCode 字典项编码
 * @param defaultLabel 默认标签
 * @returns 字典项标签
 */
export function getDictLabel(dictCode: string, uniqueCode: string, defaultLabel: string = ''): string {
  if (!dictCache[dictCode]) {
    loadDict(dictCode).catch(err => console.error(err))
    return defaultLabel
  }
  
  const item = dictCache[dictCode].find(item => 
    item.uniqueCode === uniqueCode || item.keyData === uniqueCode
  )
  return item ? item.label : defaultLabel
}

/**
 * 字典选项hook
 * @param dictCode 字典编码
 * @returns 字典选项和加载状态
 */
export function useDictOptions(dictCode: string) {
  // 加载字典数据
  if (!dictCache[dictCode]) {
    loadDict(dictCode).catch(err => console.error(err))
  }
  
  // 字典选项
  const options = computed(() => {
    return dictCache[dictCode]?.map(item => ({
      label: item.label,
      value: (item as any).keyData || item.uniqueCode
    })) || []
  })
  
  // 加载状态
  const loading = computed(() => !!loadingDict.value[dictCode])
  
  // 错误信息
  const error = computed(() => dictErrors[dictCode] || '')
  
  return {
    options,
    loading,
    error,
    reload: () => loadDict(dictCode)
  }
}

/**
 * 清除字典缓存
 * @param dictCode 字典编码，不传则清除所有
 */
export function clearDictCache(dictCode?: string) {
  if (dictCode) {
    delete dictCache[dictCode]
  } else {
    Object.keys(dictCache).forEach(key => {
      delete dictCache[key]
    })
  }
}

export default {
  loadDict,
  getDictLabel,
  useDictOptions,
  clearDictCache
} 