/**
 * @file envValidator.ts
 * @description 环境变量验证工具
 */

import { logger } from './logger';

// 环境变量验证规则类型
interface EnvRule {
  // 变量名
  name: string;
  // 是否必须
  required: boolean;
  // 验证函数
  validator?: (value: string) => boolean;
  // 错误信息
  errorMessage?: string;
}

/**
 * 验证环境变量
 * @param rules 验证规则
 * @returns 验证结果
 */
export function validateEnv(rules: EnvRule[]): boolean {
  let isValid = true;
  const env = import.meta.env;
  const missingVars: string[] = [];
  const invalidVars: string[] = [];

  for (const rule of rules) {
    const value = env[rule.name] as string | undefined;
    
    // 检查必需变量是否存在
    if (rule.required && (value === undefined || value === '')) {
      missingVars.push(rule.name);
      isValid = false;
      continue;
    }
    
    // 如果有值且有验证函数，则进行验证
    if (value !== undefined && rule.validator && !rule.validator(value)) {
      invalidVars.push(`${rule.name}${rule.errorMessage ? ` (${rule.errorMessage})` : ''}`);
      isValid = false;
    }
  }

  // 记录验证结果
  if (missingVars.length > 0) {
    logger.error(`缺少必要的环境变量: ${missingVars.join(', ')}`);
  }
  
  if (invalidVars.length > 0) {
    logger.error(`无效的环境变量: ${invalidVars.join(', ')}`);
  }
  
  if (isValid) {
    logger.info('环境变量验证通过');
  }

  return isValid;
}

// 预定义的验证器
export const validators = {
  // 验证URL格式
  isUrl: (value: string) => {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  },
  
  // 验证布尔值
  isBoolean: (value: string) => {
    return value === 'true' || value === 'false';
  },
  
  // 验证数字
  isNumber: (value: string) => {
    return !isNaN(Number(value));
  },
  
  // 验证逗号分隔的数字列表
  isNumberList: (value: string) => {
    return value.split(',').every(item => !isNaN(Number(item.trim())));
  }
};

// 导出默认的环境变量验证规则
export const defaultEnvRules: EnvRule[] = [
  { name: 'VITE_API_BASE_URL', required: true },
  { 
    name: 'VITE_API_TIMEOUT', 
    required: false, 
    validator: validators.isNumber,
    errorMessage: '必须是数字' 
  },
  { 
    name: 'VITE_API_RETRIES', 
    required: false, 
    validator: validators.isNumber,
    errorMessage: '必须是数字' 
  },
  { 
    name: 'VITE_USE_MOCK', 
    required: true, 
    validator: validators.isBoolean,
    errorMessage: '必须是 true 或 false' 
  },
  { name: 'VITE_APP_TITLE', required: false },
  { name: 'VITE_APP_VERSION', required: false },
  { name: 'VITE_APP_COPYRIGHT', required: false },
  { 
    name: 'VITE_DEFAULT_PAGE_SIZE', 
    required: false, 
    validator: validators.isNumber,
    errorMessage: '必须是数字' 
  },
  { 
    name: 'VITE_PAGE_SIZES', 
    required: false, 
    validator: validators.isNumberList,
    errorMessage: '必须是逗号分隔的数字列表' 
  }
];

// 默认验证函数
export function validateEnvironment(): boolean {
  return validateEnv(defaultEnvRules);
}

// 导出默认验证函数
export default validateEnvironment; 