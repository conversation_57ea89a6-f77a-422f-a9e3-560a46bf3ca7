/**
 * 术语词典服务
 * 用于管理系统中的专业术语及其解释
 */

export interface GlossaryTerm {
  id: string;
  term: string;
  definition: string;
  category?: string;
}

// 存储所有术语
const glossaryTerms: GlossaryTerm[] = [
  {
    id: 'third-party-platform',
    term: '三方平台',
    definition: '通常指第三方平台管理平台，如支付宝商户平台、支付宝开发者平台、微信商户平台等。',
    category: '平台'
  },
  {
    id: 'app-type',
    term: '应用类型',
    definition: '应用程序的分类，如小程序、APP、网站等。',
    category: '应用'
  },
  {
    id: 'merchant-type',
    term: '商户类型',
    definition: '商户的分类，如服务商、特约商户、直连商户(普通商户)等。',
    category: '商户'
  },
  {
    id: 'merchant-number',
    term: '商户号',
    definition: '商户平台分配给商户的唯一标识，用于识别商户身份。',
    category: '商户'
  },
  {
    id: 'merchant-unique-number',
    term: '商户唯一编号',
    definition: '由渠道标识+商户号组成的唯一标识，属于自定义的概念。',
    category: '商户'
  },
  {
    id: 'app-id',
    term: '应用号',
    definition: '在开发者平台上注册的应用程序的唯一标识(appId)。',
    category: '应用'
  },
  {
    id: 'login-account',
    term: '渠道账号',
    definition: '用户用于访问开发者平台或商户平台的账号。',
    category: '账户'
  },
  {
    id: 'payment-product',
    term: '支付产品',
    definition: '支付服务提供商提供的具体支付解决方案或产品，如JSAPI、H5支付、先乘后付等。',
    category: '支付'
  },
  {
    id: 'payment-method',
    term: '支付方式',
    definition: '用户在进行支付时选择的具体方式，如支付宝支付、微信支付、抖音支付等。支付方式可以关联多个支付产品和支付渠道。',
    category: '支付'
  },
  {
    id: 'payment-scene',
    term: '支付场景',
    definition: '支付发生的具体情境或环境，如车费支付场景、购买周卡场景、支付高速费场景等。每个支付场景可以配置多个支付方式。',
    category: '支付'
  },
  {
    id: 'payment-channel',
    term: '支付渠道',
    definition: '提供支付服务的通道，如微信支付、支付宝、银盛、通联等。每个支付方式可以使用多个支付渠道。',
    category: '支付'
  },
  {
    id: 'channel-route',
    term: '渠道路由',
    definition: '管理支付请求的路由规则，决定支付请求通过哪个渠道进行处理。包括优先级设置、时间策略、金额范围配置等。',
    category: '支付'
  }
];

/**
 * 获取所有术语
 * @returns 所有术语列表
 */
export function getAllTerms(): GlossaryTerm[] {
  return glossaryTerms;
}

/**
 * 根据ID获取术语
 * @param id 术语ID
 * @returns 术语对象或undefined
 */
export function getTermById(id: string): GlossaryTerm | undefined {
  return glossaryTerms.find(term => term.id === id);
}

/**
 * 根据术语名称获取术语
 * @param name 术语名称
 * @returns 术语对象或undefined
 */
export function getTermByName(name: string): GlossaryTerm | undefined {
  return glossaryTerms.find(term => term.term === name);
}

/**
 * 根据分类获取术语
 * @param category 分类名称
 * @returns 术语列表
 */
export function getTermsByCategory(category: string): GlossaryTerm[] {
  return glossaryTerms.filter(term => term.category === category);
} 