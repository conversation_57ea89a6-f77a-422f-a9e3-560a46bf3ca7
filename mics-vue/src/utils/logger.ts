/**
 * @file logger.ts
 * @description 日志工具类
 */

// 日志级别
type LogLevel = 'debug' | 'info' | 'warn' | 'error'

// 日志样式配置
const LOG_STYLES = {
  debug: 'color: #8a8a8a',
  info: 'color: #2196f3',
  warn: 'color: #ff9800',
  error: 'color: #f44336; font-weight: bold'
}

/**
 * 日志配置
 */
interface LoggerConfig {
  // 是否启用开发环境日志
  enableDevLogs: boolean;
  // 是否启用生产环境日志
  enableProdLogs: boolean;
  // 最低日志级别
  minLevel: LogLevel;
  // 是否发送错误日志到服务器
  sendErrorLogs: boolean;
}

// 从环境变量获取日志配置
const DEFAULT_LOGGER_CONFIG: LoggerConfig = {
  enableDevLogs: true,
  enableProdLogs: false,
  minLevel: 'info',
  sendErrorLogs: true
};

// 当前环境
const isDev = import.meta.env.MODE === 'development';
const isProd = import.meta.env.PROD;

class Logger {
  private module: string;
  private config: LoggerConfig;

  constructor(module: string, config: Partial<LoggerConfig> = {}) {
    this.module = module;
    this.config = { ...DEFAULT_LOGGER_CONFIG, ...config };
  }

  private shouldLog(level: LogLevel): boolean {
    const levelOrder: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
    
    // 根据环境和配置决定是否显示日志
    if (isDev && !this.config.enableDevLogs) return false;
    if (isProd && !this.config.enableProdLogs) return false;
    
    // 根据日志级别决定是否显示
    return levelOrder[level] >= levelOrder[this.config.minLevel];
  }

  private log(level: LogLevel, message: string, ...args: any[]) {
    if (!this.shouldLog(level)) return;
    
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}] [${this.module}]`;
    
    if (isDev) {
      switch (level) {
        case 'debug':
          console.debug(`%c${prefix} ${message}`, LOG_STYLES[level], ...args);
          break;
        case 'info':
          console.info(`%c${prefix} ${message}`, LOG_STYLES[level], ...args);
          break;
        case 'warn':
          console.warn(`%c${prefix} ${message}`, LOG_STYLES[level], ...args);
          break;
        case 'error':
          console.error(`%c${prefix} ${message}`, LOG_STYLES[level], ...args);
          break;
      }
    }
    
    // 在生产环境下，可以将错误日志发送到日志服务器
    if (isProd && level === 'error' && this.config.sendErrorLogs) {
      // TODO: 发送错误日志到服务器
      // this.sendErrorLog({ level, message, args, module: this.module });
    }
  }

  debug(message: string, ...args: any[]) {
    this.log('debug', message, ...args);
  }

  info(message: string, ...args: any[]) {
    this.log('info', message, ...args);
  }

  warn(message: string, ...args: any[]) {
    this.log('warn', message, ...args);
  }

  error(message: string, ...args: any[]) {
    this.log('error', message, ...args);
  }

  // 记录 API 请求日志
  logApiRequest(method: string, url: string, params?: any, data?: any) {
    this.debug(`API Request: ${method.toUpperCase()} ${url}`, {
      params,
      data
    });
  }

  // 记录 API 响应日志
  logApiResponse(method: string, url: string, response: any, duration: number) {
    this.debug(`API Response: ${method.toUpperCase()} ${url} (${duration}ms)`, response);
  }

  // 记录 API 错误日志
  logApiError(method: string, url: string, error: any) {
    this.error(`API Error: ${method.toUpperCase()} ${url}`, error);
  }
}

// 创建默认日志实例
export const logger = new Logger('App');

// 创建自定义模块日志实例
export const createLogger = (module: string, config?: Partial<LoggerConfig>) => new Logger(module, config);

export default logger; 