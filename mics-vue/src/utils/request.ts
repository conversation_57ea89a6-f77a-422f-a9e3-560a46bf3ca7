/**
 * @file request.ts
 * @description axios请求封装
 */

import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage } from 'element-plus';
import { apiConfig } from '@/config/env';
import { createLogger } from './logger';

// 创建API请求日志记录器
const logger = createLogger('API');

// 扩展AxiosRequestConfig类型，添加元数据字段
declare module 'axios' {
  export interface AxiosRequestConfig {
    metadata?: {
      startTime: number;
    };
  }
}

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: apiConfig.baseURL,
  timeout: apiConfig.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 记录请求开始时间
    config.metadata = { startTime: new Date().getTime() };
    
    // 记录请求日志
    logger.info(`${config.method?.toUpperCase()} ${config.url}`, { 
      params: config.params,
      data: config.data 
    });
    
    // 在发送请求之前做些什么
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    logger.error('请求配置错误', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    // 计算请求耗时
    const requestDuration = response.config.metadata 
      ? new Date().getTime() - response.config.metadata.startTime
      : 0;
    
    // 对响应数据做点什么
    const res = response.data;
    
    // 记录响应日志
    logger.info(`${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status} (${requestDuration}ms)`, {
      response: res
    });
    
    // 适配后端返回的数据结构 {code: 0, message: "", data: {...}}
    if (res.code !== undefined) {
      if (res.code === 0) {
        return res.data;
      } else {
        const errorMsg = res.message || '请求失败';
        logger.warn(`请求失败: ${errorMsg}`, {
          url: response.config.url,
          method: response.config.method,
          code: res.code,
          data: response.config.data
        });
        
        ElMessage.error(errorMsg);
        return Promise.reject(new Error(errorMsg));
      }
    }
    
    return res;
  },
  (error) => {
    // 记录错误日志
    logger.error('请求错误', {
      url: error.config?.url,
      method: error.config?.method,
      message: error.message,
      stack: error.stack,
      response: error.response?.data
    });
    
    // 对响应错误做点什么
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权，跳转到登录页
          logger.warn('用户未授权，跳转登录页');
          ElMessage.error('请重新登录');
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          logger.warn('权限不足', { url: error.config?.url });
          ElMessage.error('没有权限访问');
          break;
        case 404:
          logger.warn('资源不存在', { url: error.config?.url });
          ElMessage.error('请求的资源不存在');
          break;
        case 500:
          logger.error('服务器错误', { 
            url: error.config?.url,
            response: error.response.data
          });
          ElMessage.error('服务器错误');
          break;
        default:
          logger.error(`未处理的错误状态码: ${error.response.status}`, {
            url: error.config?.url,
            data: error.response.data
          });
          ElMessage.error(error.response.data?.message || '请求失败');
      }
    } else if (error.request) {
      logger.error('网络请求错误', {
        url: error.config?.url,
        timeout: error.config?.timeout
      });
      ElMessage.error('网络错误，请检查网络连接');
    } else {
      logger.error('请求被取消或发生其他错误', {
        message: error.message
      });
      ElMessage.error(error.message || '请求失败');
    }
    return Promise.reject(error);
  }
);

// 封装 GET 请求
export const get = <T>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request.get(url, { params, ...config });
};

// 将对象转换为URL编码格式
function objectToURLEncoded(obj: any): string {
  return Object.keys(obj)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
    .join('&');
}

// 封装 POST 表单请求（application/x-www-form-urlencoded）
export const postForm = <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  const formData = objectToURLEncoded(data || {});
  const configs = {
    ...config,
    headers: {
      ...(config?.headers || {}),
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };
  return request.post(url, formData, configs);
};

// 封装 POST 请求
export const post = <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request.post(url, data, config);
};

// 封装 PUT 请求
export const put = <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request.put(url, data, config);
};

// 封装 DELETE 请求
export const del = <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  return request.delete(url, config);
};

export { request };