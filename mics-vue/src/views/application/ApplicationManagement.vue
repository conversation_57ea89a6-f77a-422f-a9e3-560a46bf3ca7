<script setup lang="ts">
import { ref } from 'vue'
import type { IMerchantApplication } from '@/api/merchant/types.ts'
import {
  createMerchantApplication,
  deleteMerchantApplication,
  updateApplicationStatus
} from '@/api/merchant'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps<{
  merchantId: string
  applications: IMerchantApplication[]
}>()

const emit = defineEmits(['refresh'])

// 新建应用对话框
const dialogVisible = ref(false)
const formData = ref({
  name: ''
})
const formLoading = ref(false)

// 创建应用
const handleCreate = async () => {
  if (!formData.value.name) {
    ElMessage.warning('请输入应用名称')
    return
  }

  formLoading.value = true
  try {
    await createMerchantApplication(props.merchantId, formData.value)
    ElMessage.success('创建成功')
    dialogVisible.value = false
    formData.value.name = ''
    emit('refresh')
  } catch (error) {
    ElMessage.error('创建失败')
  } finally {
    formLoading.value = false
  }
}

// 删除应用
const handleDelete = async (application: IMerchantApplication) => {
  try {
    await ElMessageBox.confirm('确认删除该应用吗？', '提示', {
      type: 'warning'
    })
    await deleteMerchantApplication(props.merchantId, application.id)
    ElMessage.success('删除成功')
    emit('refresh')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 更新应用状态
const handleStatusChange = async (application: IMerchantApplication) => {
  try {
    const newStatus = application.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
    await updateApplicationStatus(props.merchantId, application.id, newStatus)
    ElMessage.success('状态更新成功')
    emit('refresh')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

// 复制内容到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}
</script>

<template>
  <el-card>
    <template #header>
      <div class="flex justify-between items-center">
        <span class="text-lg font-medium">应用管理</span>
        <el-button type="primary" @click="dialogVisible = true">新建应用</el-button>
      </div>
    </template>

    <el-table :data="applications" border stripe>
      <el-table-column prop="name" label="应用名称" min-width="150" />
      <el-table-column prop="appId" label="AppID" width="220">
        <template #default="{ row }">
          <div class="flex items-center space-x-2">
            <span>{{ row.appId }}</span>
            <el-button
              link
              type="primary"
              @click="copyToClipboard(row.appId)"
            >
              复制
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="appSecret" label="AppSecret" width="220">
        <template #default="{ row }">
          <div class="flex items-center space-x-2">
            <span>{{ row.appSecret }}</span>
            <el-button
              link
              type="primary"
              @click="copyToClipboard(row.appSecret)"
            >
              复制
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'danger'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180" />
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button
            link
            :type="row.status === 'ACTIVE' ? 'danger' : 'success'"
            @click="handleStatusChange(row)"
          >
            {{ row.status === 'ACTIVE' ? '禁用' : '启用' }}
          </el-button>
          <el-button link type="danger" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新建应用对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="新建应用"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="formData" label-width="80px">
        <el-form-item label="应用名称" required>
          <el-input v-model="formData.name" placeholder="请输入应用名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="formLoading"
            @click="handleCreate"
          >
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<style scoped>
.dialog-footer {
  @apply flex justify-end space-x-2;
}
</style>
