/**
 * @file Channel.vue
 * @description 渠道管理页面
 * <AUTHOR> Assistant
 */
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

// 跳转到渠道账户管理页面
const goToChannelAccount = () => {
  router.push('/channel/account')
}

// 其他渠道管理功能可以在这里添加
</script>

<template>
  <div class="channel-container bg-white p-6 rounded-lg shadow-sm">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-800">渠道管理</h2>
      <p class="text-gray-500 mt-1">管理支付渠道相关信息，包括渠道账户、渠道路由等</p>
    </div>
    
    <!-- 渠道管理模块导航卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- 渠道账户管理 -->
      <div 
        class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-lg shadow-sm hover:shadow-md transition cursor-pointer"
        @click="goToChannelAccount"
      >
        <div class="flex items-center mb-4">
          <i class="fas fa-credit-card text-blue-500 text-3xl"></i>
          <h3 class="text-xl font-medium text-gray-800 ml-3">渠道账户</h3>
        </div>
        <p class="text-gray-600">
          管理支付渠道账户信息，包含基本信息、密钥配置、员工管理等
        </p>
        <div class="mt-4 flex justify-end">
          <i class="fas fa-arrow-right text-blue-500"></i>
        </div>
      </div>
      
      <!-- 渠道路由管理 - 待开发 -->
      <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-lg shadow-sm opacity-70">
        <div class="flex items-center mb-4">
          <i class="fas fa-route text-gray-500 text-3xl"></i>
          <h3 class="text-xl font-medium text-gray-800 ml-3">渠道路由</h3>
        </div>
        <p class="text-gray-600">
          配置支付渠道路由规则，实现智能支付渠道选择
        </p>
        <div class="mt-4 flex justify-end">
          <span class="text-xs px-2 py-1 bg-gray-200 text-gray-600 rounded">开发中</span>
        </div>
      </div>
      
      <!-- 渠道对账管理 - 待开发 -->
      <div class="bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-lg shadow-sm opacity-70">
        <div class="flex items-center mb-4">
          <i class="fas fa-file-invoice-dollar text-gray-500 text-3xl"></i>
          <h3 class="text-xl font-medium text-gray-800 ml-3">渠道对账</h3>
        </div>
        <p class="text-gray-600">
          处理渠道交易对账、差错处理及结算管理
        </p>
        <div class="mt-4 flex justify-end">
          <span class="text-xs px-2 py-1 bg-gray-200 text-gray-600 rounded">开发中</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.channel-container {
  min-height: calc(100vh - 120px);
}
</style> 