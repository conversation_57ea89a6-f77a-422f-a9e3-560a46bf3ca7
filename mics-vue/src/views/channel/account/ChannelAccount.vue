/**
 * @file ChannelAccount.vue
 * @description 渠道账户组件
 * <AUTHOR> Assistant
 */
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import channelAccountService from '@/api/channel/account'
import { ChannelType, ChannelAccountStatus } from '@/api/channel/account/types'
import type { 
  IChannelAccount, 
  IChannelAccountQueryParams
} from '@/api/channel/account'
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

// 表格加载状态
const isLoading = ref(false)

// 渠道账户列表数据
const accountList = ref<IChannelAccount[]>([])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 查询参数
const queryParams = reactive<IChannelAccountQueryParams>({
  page: 1,
  pageSize: 10,
  keyword: '',
  channelType: undefined,
  status: undefined,
  sortField: 'createdAt',
  sortOrder: 'descend'
})

// 支付渠道类型选项
const channelTypeOptions = [
  { label: '微信支付', value: ChannelType.WECHAT },
  { label: '支付宝', value: ChannelType.ALIPAY },
  { label: '银联支付', value: ChannelType.UNIONPAY },
  { label: '京东支付', value: ChannelType.JD_PAY },
  { label: '其他', value: ChannelType.OTHER }
]

// 状态选项
const statusOptions = [
  { label: '正常', value: ChannelAccountStatus.ACTIVE },
  { label: '停用', value: ChannelAccountStatus.INACTIVE },
  { label: '待审核', value: ChannelAccountStatus.PENDING },
  { label: '已关闭', value: ChannelAccountStatus.CLOSED }
]

// 渠道状态翻译函数
const getStatusText = (status: ChannelAccountStatus): string => {
  const statusMap: Record<ChannelAccountStatus, string> = {
    [ChannelAccountStatus.ACTIVE]: '正常',
    [ChannelAccountStatus.INACTIVE]: '停用',
    [ChannelAccountStatus.PENDING]: '待审核',
    [ChannelAccountStatus.CLOSED]: '已关闭'
  }
  return statusMap[status] || '未知'
}

// 渠道类型翻译函数
const getChannelTypeText = (type: ChannelType): string => {
  const typeMap: Record<ChannelType, string> = {
    [ChannelType.WECHAT]: '微信支付',
    [ChannelType.ALIPAY]: '支付宝',
    [ChannelType.UNIONPAY]: '银联支付',
    [ChannelType.JD_PAY]: '京东支付',
    [ChannelType.OTHER]: '其他'
  }
  return typeMap[type] || '未知'
}

// 获取状态标签类型
const getStatusTagType = (status: ChannelAccountStatus): string => {
  const tagMap: Record<ChannelAccountStatus, string> = {
    [ChannelAccountStatus.ACTIVE]: 'success',
    [ChannelAccountStatus.INACTIVE]: 'warning',
    [ChannelAccountStatus.PENDING]: 'info',
    [ChannelAccountStatus.CLOSED]: 'danger'
  }
  return tagMap[status] || 'info'
}

// 获取渠道图标类名
const getChannelIconClass = (type: ChannelType): string => {
  const iconMap: Record<ChannelType, string> = {
    [ChannelType.WECHAT]: 'fab fa-weixin text-green-500',
    [ChannelType.ALIPAY]: 'fab fa-alipay text-blue-500',
    [ChannelType.UNIONPAY]: 'fas fa-credit-card text-red-500',
    [ChannelType.JD_PAY]: 'fas fa-shopping-cart text-red-500',
    [ChannelType.OTHER]: 'fas fa-money-bill-wave text-gray-500'
  }
  return iconMap[type] || 'fas fa-question-circle'
}

/**
 * 获取渠道账户列表
 */
const fetchAccountList = async () => {
  isLoading.value = true
  
  try {
    const params: IChannelAccountQueryParams = {
      ...queryParams,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    }
    
    const response = await channelAccountService.getChannelAccountList(params)
    accountList.value = response.items
    pagination.total = response.total
  } catch (error) {
    ElMessage.error('获取渠道账户列表失败')
    console.error('获取渠道账户列表失败', error)
  } finally {
    isLoading.value = false
  }
}

/**
 * 处理页面变化
 */
const handlePageChange = (page: number) => {
  pagination.currentPage = page
  fetchAccountList()
}

/**
 * 处理每页条数变化
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchAccountList()
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.currentPage = 1
  fetchAccountList()
}

/**
 * 重置搜索条件
 */
const resetSearch = () => {
  Object.assign(queryParams, {
    keyword: '',
    channelType: undefined,
    status: undefined
  })
  handleSearch()
}

/**
 * 处理排序
 */
const handleSortChange = (column: any) => {
  if (column.prop && column.order) {
    queryParams.sortField = column.prop
    queryParams.sortOrder = column.order === 'descending' ? 'descend' : 'ascend'
    fetchAccountList()
  }
}

/**
 * 查看详情
 */
const goToDetail = (id: string) => {
  router.push(`/channel/account/detail/${id}`)
}

/**
 * 查看渠道员工
 */
const goToEmployeeList = (id: string) => {
  router.push(`/channel/account/employee/${id}`)
}

/**
 * 编辑渠道账户
 */
const goToEdit = (id: string) => {
  router.push(`/channel/account/edit/${id}`)
}

/**
 * 新建渠道账户
 */
const goToCreate = () => {
  router.push('/channel/account/create')
}

/**
 * 删除渠道账户
 */
const handleDelete = (id: string) => {
  ElMessageBox.confirm('确定要删除此渠道账户吗？删除后无法恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await channelAccountService.deleteChannelAccount(id)
      ElMessage.success('删除成功')
      fetchAccountList()
    } catch (error) {
      ElMessage.error('删除失败')
      console.error('删除渠道账户失败', error)
    }
  }).catch(() => {
    // 取消操作
  })
}

/**
 * 更新渠道账户状态
 */
const handleUpdateStatus = (id: string, status: ChannelAccountStatus) => {
  const nextStatus = status === ChannelAccountStatus.ACTIVE 
    ? ChannelAccountStatus.INACTIVE 
    : ChannelAccountStatus.ACTIVE
  
  const statusText = nextStatus === ChannelAccountStatus.ACTIVE ? '启用' : '停用'
  
  ElMessageBox.confirm(`确定要${statusText}此渠道账户吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await channelAccountService.updateChannelAccountStatus(id, nextStatus)
      ElMessage.success(`${statusText}成功`)
      fetchAccountList()
    } catch (error) {
      ElMessage.error(`${statusText}失败`)
      console.error('更新渠道账户状态失败', error)
    }
  }).catch(() => {
    // 取消操作
  })
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAccountList()
})
</script>

<template>
  <div class="channel-account-container bg-white p-6 rounded-lg shadow-sm">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h2 class="text-2xl font-bold text-gray-800">渠道账户管理</h2>
      <p class="text-gray-500 mt-1">管理支付渠道账户信息，包含账户基本信息、密钥配置等</p>
    </div>
    
    <!-- 搜索过滤区 -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <el-input
          v-model="queryParams.keyword"
          placeholder="搜索渠道名称/编码/联系人"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <i class="fas fa-search text-gray-400"></i>
          </template>
        </el-input>
        
        <el-select
          v-model="queryParams.channelType"
          placeholder="渠道类型"
          clearable
        >
          <el-option
            v-for="option in channelTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <el-select
          v-model="queryParams.status"
          placeholder="账户状态"
          clearable
        >
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <div class="flex space-x-2">
          <el-button type="primary" @click="handleSearch">
            <i class="fas fa-search mr-1"></i> 搜索
          </el-button>
          <el-button @click="resetSearch">
            <i class="fas fa-redo mr-1"></i> 重置
          </el-button>
          <el-button type="success" @click="goToCreate">
            <i class="fas fa-plus mr-1"></i> 新建
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 渠道账户列表 -->
    <el-table
      v-loading="isLoading"
      :data="accountList"
      border
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="name" label="渠道名称" min-width="180">
        <template #default="{ row }">
          <div class="flex items-center">
            <i :class="getChannelIconClass(row.channelType)" class="text-xl mr-2"></i>
            <span class="font-medium">{{ row.name }}</span>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="code" label="渠道编码" min-width="120" />
      
      <el-table-column prop="channelType" label="渠道类型" min-width="120">
        <template #default="{ row }">
          {{ getChannelTypeText(row.channelType) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="contactName" label="联系人" min-width="120" />
      
      <el-table-column prop="contactPhone" label="联系电话" min-width="150" />
      
      <el-table-column prop="status" label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="merchantName" label="所属商户" min-width="150">
        <template #default="{ row }">
          <span v-if="row.merchantName">{{ row.merchantName }}</span>
          <span v-else class="text-gray-400">--</span>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="createdAt" 
        label="创建时间" 
        min-width="180"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ new Date(row.createdAt).toLocaleString() }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" fixed="right" width="250">
        <template #default="{ row }">
          <div class="flex space-x-2">
            <el-button 
              size="small" 
              @click="goToDetail(row.id)"
              type="primary" 
              text
            >
              详情
            </el-button>
            
            <el-button 
              size="small" 
              @click="goToEmployeeList(row.id)"
              type="info" 
              text
            >
              员工
            </el-button>
            
            <el-button 
              size="small" 
              @click="goToEdit(row.id)"
              type="success" 
              text
            >
              编辑
            </el-button>
            
            <el-button 
              size="small" 
              @click="handleUpdateStatus(row.id, row.status)"
              :type="row.status === ChannelAccountStatus.ACTIVE ? 'warning' : 'success'"
              text
            >
              {{ row.status === ChannelAccountStatus.ACTIVE ? '停用' : '启用' }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="flex justify-end mt-4">
      <el-pagination
        v-model:currentPage="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.channel-account-container {
  min-height: calc(100vh - 120px);
}
</style> 