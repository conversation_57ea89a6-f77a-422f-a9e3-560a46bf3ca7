/**
 * @file ChannelEmployee.vue
 * @description 渠道员工组件
 * <AUTHOR> Assistant
 */
<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import channelAccountService from '@/api/channel/account'
import type { 
  IChannelEmployee, 
  IChannelEmployeeQueryParams,
  IChannelAccountDetail,
  ICreateChannelEmployeeRequest,
  IChannelAccount,
  IChannelAccountQueryParams
} from '@/api/channel/account'
import { useRouter, useRoute } from 'vue-router'

// 路由实例
const router = useRouter()
const route = useRoute()

// 获取渠道账户ID
const channelAccountId = computed(() => route.params.id as string)
const hasAccountId = computed(() => !!channelAccountId.value)

// 表格加载状态
const isLoading = ref(false)
const accountLoading = ref(false)
const accountListLoading = ref(false)

// 渠道账户信息
const accountInfo = ref<IChannelAccountDetail | null>(null)

// 渠道账户列表（用于在没有指定id时选择）
const accountList = ref<IChannelAccount[]>([])

// 员工列表数据
const employeeList = ref<IChannelEmployee[]>([])

// 分页数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 查询参数
const queryParams = reactive<IChannelEmployeeQueryParams>({
  page: 1,
  pageSize: 10,
  channelAccountId: '',
  keyword: '',
  status: undefined,
  sortField: 'createdAt',
  sortOrder: 'descend'
})

// 员工表单对话框
const employeeDialogVisible = ref(false)
const employeeFormLoading = ref(false)
const employeeForm = reactive<ICreateChannelEmployeeRequest>({
  name: '',
  jobTitle: '',
  phone: '',
  email: '',
  channelAccountId: '',
  department: '',
  remarks: ''
})

// 表单校验规则
const formRules = {
  name: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  jobTitle: [
    { required: true, message: '请输入职位', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 当前编辑的员工ID
const currentEmployeeId = ref<string | null>(null)

// 当前选中的渠道账户ID（用于没有路由参数时）
const selectedAccountId = ref<string>('')

/**
 * 获取渠道账户列表
 */
const fetchChannelAccountList = async () => {
  if (hasAccountId.value) return
  
  accountListLoading.value = true
  
  try {
    const params: IChannelAccountQueryParams = {
      page: 1,
      pageSize: 100
    }
    const response = await channelAccountService.getChannelAccountList(params)
    accountList.value = response.items
    
    // 如果有账户，默认选中第一个
    if (response.items.length > 0) {
      selectedAccountId.value = response.items[0].id
      queryParams.channelAccountId = selectedAccountId.value
      fetchChannelAccount(selectedAccountId.value)
      fetchEmployeeList()
    }
  } catch (error) {
    ElMessage.error('获取渠道账户列表失败')
    console.error('获取渠道账户列表失败', error)
  } finally {
    accountListLoading.value = false
  }
}

/**
 * 获取渠道账户信息
 */
const fetchChannelAccount = async (id?: string) => {
  const accountId = id || channelAccountId.value
  if (!accountId) return
  
  accountLoading.value = true
  
  try {
    accountInfo.value = await channelAccountService.getChannelAccountDetail(accountId)
  } catch (error) {
    ElMessage.error('获取渠道账户信息失败')
    console.error('获取渠道账户信息失败', error)
  } finally {
    accountLoading.value = false
  }
}

/**
 * 获取员工列表
 */
const fetchEmployeeList = async () => {
  const accountId = hasAccountId.value ? channelAccountId.value : selectedAccountId.value
  if (!accountId) return
  
  isLoading.value = true
  
  try {
    queryParams.channelAccountId = accountId
    queryParams.page = pagination.currentPage
    queryParams.pageSize = pagination.pageSize
    
    const response = await channelAccountService.getChannelEmployeeList(queryParams)
    employeeList.value = response.items
    pagination.total = response.total
  } catch (error) {
    ElMessage.error('获取渠道员工列表失败')
    console.error('获取渠道员工列表失败', error)
  } finally {
    isLoading.value = false
  }
}

// 更改当前选中的渠道账户
const handleAccountChange = (accountId: string) => {
  selectedAccountId.value = accountId
  queryParams.channelAccountId = accountId
  
  fetchChannelAccount(accountId)
  pagination.currentPage = 1
  fetchEmployeeList()
}

/**
 * 处理页面变化
 */
const handlePageChange = (page: number) => {
  pagination.currentPage = page
  fetchEmployeeList()
}

/**
 * 处理每页条数变化
 */
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  fetchEmployeeList()
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  pagination.currentPage = 1
  fetchEmployeeList()
}

/**
 * 重置搜索条件
 */
const resetSearch = () => {
  queryParams.keyword = ''
  queryParams.status = undefined
  handleSearch()
}

/**
 * 处理排序
 */
const handleSortChange = (column: any) => {
  if (column.prop && column.order) {
    queryParams.sortField = column.prop
    queryParams.sortOrder = column.order === 'descending' ? 'descend' : 'ascend'
    fetchEmployeeList()
  }
}

/**
 * 打开新增员工对话框
 */
const openAddEmployeeDialog = () => {
  const accountId = hasAccountId.value ? channelAccountId.value : selectedAccountId.value
  if (!accountId) {
    ElMessage.warning('请先选择渠道账户')
    return
  }

  currentEmployeeId.value = null
  Object.assign(employeeForm, {
    name: '',
    jobTitle: '',
    phone: '',
    email: '',
    channelAccountId: accountId,
    department: '',
    remarks: ''
  })
  employeeDialogVisible.value = true
}

/**
 * 打开编辑员工对话框
 */
const openEditEmployeeDialog = async (id: string) => {
  currentEmployeeId.value = id
  employeeFormLoading.value = true
  
  try {
    const employee = await channelAccountService.getChannelEmployeeDetail(id)
    Object.assign(employeeForm, {
      name: employee.name,
      jobTitle: employee.jobTitle,
      phone: employee.phone,
      email: employee.email,
      channelAccountId: employee.channelAccountId,
      department: employee.department || '',
      remarks: employee.remarks || ''
    })
    employeeDialogVisible.value = true
  } catch (error) {
    ElMessage.error('获取员工信息失败')
    console.error('获取员工信息失败', error)
  } finally {
    employeeFormLoading.value = false
  }
}

/**
 * 提交员工表单
 */
const submitEmployeeForm = async (formEl: any) => {
  if (!formEl) return
  
  await formEl.validate(async (valid: boolean) => {
    if (valid) {
      employeeFormLoading.value = true
      
      try {
        if (currentEmployeeId.value) {
          // 编辑
          await channelAccountService.updateChannelEmployee({
            id: currentEmployeeId.value,
            ...employeeForm
          })
          ElMessage.success('更新员工成功')
        } else {
          // 新增
          await channelAccountService.createChannelEmployee(employeeForm)
          ElMessage.success('添加员工成功')
        }
        
        employeeDialogVisible.value = false
        fetchEmployeeList()
      } catch (error) {
        const action = currentEmployeeId.value ? '更新' : '添加'
        ElMessage.error(`${action}员工失败`)
        console.error(`${action}员工失败`, error)
      } finally {
        employeeFormLoading.value = false
      }
    }
  })
}

/**
 * 删除员工
 */
const handleDeleteEmployee = (id: string) => {
  ElMessageBox.confirm('确定要删除此员工吗？删除后无法恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await channelAccountService.deleteChannelEmployee(id)
      ElMessage.success('删除成功')
      fetchEmployeeList()
    } catch (error) {
      ElMessage.error('删除失败')
      console.error('删除员工失败', error)
    }
  }).catch(() => {
    // 取消操作
  })
}

/**
 * 更新员工状态
 */
const handleUpdateEmployeeStatus = (id: string, status: 'ACTIVE' | 'INACTIVE') => {
  const nextStatus = status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
  const statusText = nextStatus === 'ACTIVE' ? '启用' : '停用'
  
  ElMessageBox.confirm(`确定要${statusText}此员工吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await channelAccountService.updateChannelEmployeeStatus(id, nextStatus)
      ElMessage.success(`${statusText}成功`)
      fetchEmployeeList()
    } catch (error) {
      ElMessage.error(`${statusText}失败`)
      console.error('更新员工状态失败', error)
    }
  }).catch(() => {
    // 取消操作
  })
}

/**
 * 返回渠道账户列表
 */
const goBack = () => {
  router.push('/channel/account')
}

// 组件挂载时获取数据
onMounted(() => {
  if (hasAccountId.value) {
    fetchChannelAccount()
    fetchEmployeeList()
  } else {
    fetchChannelAccountList()
  }
})
</script>

<template>
  <div class="channel-employee-container bg-white p-6 rounded-lg shadow-sm">
    <!-- 页面标题 -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <div class="flex items-center mb-2">
            <el-button icon="el-icon-back" @click="goBack" text>返回</el-button>
            <h2 class="text-2xl font-bold text-gray-800 ml-2">渠道员工管理</h2>
          </div>
          <p class="text-gray-500" v-if="accountInfo">
            渠道: {{ accountInfo.name }}
            <el-tag size="small" class="ml-2" :type="accountInfo.status === 'ACTIVE' ? 'success' : 'warning'">
              {{ accountInfo.status === 'ACTIVE' ? '正常' : '停用' }}
            </el-tag>
          </p>
        </div>
        <div>
          <el-button type="primary" @click="openAddEmployeeDialog">
            <i class="fas fa-plus mr-1"></i> 添加员工
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 选择渠道账户（当没有从URL参数获取到账户ID时） -->
    <div v-if="!hasAccountId" class="bg-blue-50 p-4 rounded-lg mb-6" v-loading="accountListLoading">
      <h3 class="font-medium mb-3">请选择渠道账户：</h3>
      <el-select 
        v-model="selectedAccountId" 
        placeholder="选择渠道账户" 
        class="w-full md:w-2/3 lg:w-1/2"
        @change="handleAccountChange"
      >
        <el-option 
          v-for="account in accountList" 
          :key="account.id" 
          :label="account.name" 
          :value="account.id"
        >
          <div class="flex items-center justify-between">
            <span>{{ account.name }}</span>
            <el-tag size="small" :type="account.status === 'ACTIVE' ? 'success' : 'warning'">
              {{ account.status === 'ACTIVE' ? '正常' : '停用' }}
            </el-tag>
          </div>
        </el-option>
      </el-select>
      <p class="text-xs text-gray-500 mt-2" v-if="accountList.length === 0">
        暂无渠道账户，请先<router-link to="/channel/account" class="text-blue-500">创建渠道账户</router-link>
      </p>
    </div>
    
    <!-- 账户信息卡片 -->
    <div class="bg-blue-50 p-4 rounded-lg mb-6" v-loading="accountLoading" v-if="accountInfo && (hasAccountId || selectedAccountId)">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <p class="text-gray-500 mb-1">渠道编码</p>
          <p class="font-medium">{{ accountInfo.code }}</p>
        </div>
        <div>
          <p class="text-gray-500 mb-1">联系人</p>
          <p class="font-medium">{{ accountInfo.contactName }}</p>
        </div>
        <div>
          <p class="text-gray-500 mb-1">联系电话</p>
          <p class="font-medium">{{ accountInfo.contactPhone }}</p>
        </div>
      </div>
    </div>
    
    <!-- 搜索过滤区 -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6" v-if="accountInfo && (hasAccountId || selectedAccountId)">
      <div class="flex items-center space-x-4">
        <el-input
          v-model="queryParams.keyword"
          placeholder="搜索员工姓名/电话/邮箱"
          style="width: 300px"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <i class="fas fa-search text-gray-400"></i>
          </template>
        </el-input>
        
        <el-select
          v-model="queryParams.status"
          placeholder="员工状态"
          clearable
          style="width: 120px"
        >
          <el-option label="正常" value="ACTIVE" />
          <el-option label="停用" value="INACTIVE" />
        </el-select>
        
        <el-button type="primary" @click="handleSearch">
          <i class="fas fa-search mr-1"></i> 搜索
        </el-button>
        <el-button @click="resetSearch">
          <i class="fas fa-redo mr-1"></i> 重置
        </el-button>
      </div>
    </div>
    
    <!-- 员工列表 -->
    <el-table
      v-if="accountInfo && (hasAccountId || selectedAccountId)"
      v-loading="isLoading"
      :data="employeeList"
      border
      style="width: 100%"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="name" label="姓名" min-width="120" />
      
      <el-table-column prop="jobTitle" label="职位" min-width="120" />
      
      <el-table-column prop="department" label="部门" min-width="120">
        <template #default="{ row }">
          <span v-if="row.department">{{ row.department }}</span>
          <span v-else class="text-gray-400">--</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="phone" label="联系电话" min-width="150" />
      
      <el-table-column prop="email" label="邮箱" min-width="200" />
      
      <el-table-column prop="status" label="状态" min-width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 'ACTIVE' ? 'success' : 'warning'" size="small">
            {{ row.status === 'ACTIVE' ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="remarks" label="备注" min-width="200">
        <template #default="{ row }">
          <span v-if="row.remarks">{{ row.remarks }}</span>
          <span v-else class="text-gray-400">--</span>
        </template>
      </el-table-column>
      
      <el-table-column 
        prop="createdAt" 
        label="创建时间" 
        min-width="180"
        sortable="custom"
      >
        <template #default="{ row }">
          {{ new Date(row.createdAt).toLocaleString() }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" fixed="right" width="180">
        <template #default="{ row }">
          <div class="flex space-x-2">
            <el-button 
              size="small" 
              @click="openEditEmployeeDialog(row.id)"
              type="primary" 
              text
            >
              编辑
            </el-button>
            
            <el-button 
              size="small" 
              @click="handleUpdateEmployeeStatus(row.id, row.status)"
              :type="row.status === 'ACTIVE' ? 'warning' : 'success'"
              text
            >
              {{ row.status === 'ACTIVE' ? '停用' : '启用' }}
            </el-button>
            
            <el-button 
              size="small" 
              @click="handleDeleteEmployee(row.id)"
              type="danger" 
              text
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="flex justify-end mt-4">
      <el-pagination
        v-model:currentPage="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
    
    <!-- 员工表单对话框 -->
    <el-dialog
      :title="currentEmployeeId ? '编辑员工' : '添加员工'"
      v-model="employeeDialogVisible"
      width="500px"
    >
      <el-form
        ref="employeeFormRef"
        :model="employeeForm"
        :rules="formRules"
        label-width="100px"
        v-loading="employeeFormLoading"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="employeeForm.name" placeholder="请输入员工姓名" />
        </el-form-item>
        
        <el-form-item label="职位" prop="jobTitle">
          <el-input v-model="employeeForm.jobTitle" placeholder="请输入职位" />
        </el-form-item>
        
        <el-form-item label="部门" prop="department">
          <el-input v-model="employeeForm.department" placeholder="请输入部门" />
        </el-form-item>
        
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="employeeForm.phone" placeholder="请输入联系电话" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="employeeForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="备注" prop="remarks">
          <el-input
            v-model="employeeForm.remarks"
            type="textarea"
            placeholder="请输入备注信息"
            rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="employeeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEmployeeForm(employeeFormRef)">
            {{ currentEmployeeId ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.channel-employee-container {
  min-height: calc(100vh - 120px);
}
</style> 