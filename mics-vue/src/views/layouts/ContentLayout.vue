/**
 * @file ContentLayout.vue
 * @description 内容区布局组件，包含路由视图 (面包屑导航已移除)
 * <AUTHOR> Assistant
 */
<script setup lang="ts">
import { useRoute, RouterLink } from 'vue-router'
import { computed } from 'vue'

// 路由实例
const route = useRoute()

/**
 * 计算面包屑导航数据
 * 根据当前路由匹配的路径生成导航层级
 * 注意：面包屑导航当前已在UI中隐藏，但保留计算逻辑以便将来可能的恢复
 */
const breadcrumbs = computed(() => {
  return route.matched.map(item => ({
    title: item.meta.title || item.name, // 优先使用路由元数据中的标题
    path: item.path
  }))
})

/**
 * 判断是否为最后一个面包屑项
 */
const isLastItem = (index: number) => {
  return index === breadcrumbs.value.length - 1
}
</script>

<template>
  <!-- 内容区容器 -->
  <div class="content-layout">
    <!-- 面包屑导航已移除 -->

    <!-- 路由视图容器 -->
    <div class="bg-white p-4 rounded-lg min-h-[calc(100vh-120px)]">
      <!-- 路由视图，带过渡动画 -->
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </div>
  </div>
</template>

<style scoped>
/* 页面切换淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 