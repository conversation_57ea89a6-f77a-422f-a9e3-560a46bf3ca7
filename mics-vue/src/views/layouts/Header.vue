/**
 * @file Header.vue
 * @description 顶部导航栏组件，包含系统标题、通知和用户信息
 */
<script setup lang="ts">
import { ref } from 'vue'
import { ElDropdown, ElDropdownMenu, ElDropdownItem, ElBadge } from 'element-plus'
import { Bell, User, Setting, SwitchButton } from '@element-plus/icons-vue'

// 定义组件事件
defineEmits(['toggle-sidebar'])

// 用户菜单显示状态
const showUserMenu = ref(false)

// 未读消息数量
const unreadCount = ref(3)

// 切换用户菜单显示状态
const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value
}

// 处理退出登录
const handleLogout = () => {
  // TODO: 实现退出登录逻辑
  console.log('退出登录')
}
</script>

<template>
  <div class="header-container w-full h-full flex justify-between items-center">
    <!-- 左侧 Logo 和标题 -->
    <div class="flex items-center h-full pl-4">
      <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
      <h1 class="text-lg font-bold text-white ml-3">收银台及渠道管理系统</h1>
    </div>

    <!-- 右侧通知和用户信息 -->
    <div class="flex items-center h-full px-6">
      <!-- 通知按钮 -->
      <div class="relative">
        <ElBadge :value="unreadCount" class="notification-badge">
          <button class="p-2 rounded-full hover:bg-white/10 transition-colors">
            <el-icon class="text-white text-xl">
              <Bell />
            </el-icon>
          </button>
        </ElBadge>
      </div>

      <!-- 用户信息下拉菜单 -->
      <ElDropdown trigger="click" @command="handleLogout" class="ml-5">
        <div class="flex items-center space-x-2 cursor-pointer">
          <img
            src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg"
            alt="User"
            class="rounded-full h-8 w-8 object-cover border-2 border-white/50"
          >
          <span class="text-white">管理员</span>
          <i class="fas fa-chevron-down text-white/70 text-xs"></i>
        </div>
        <template #dropdown>
          <ElDropdownMenu>
            <ElDropdownItem>
              <el-icon><User /></el-icon>
              <span class="ml-2">个人信息</span>
            </ElDropdownItem>
            <ElDropdownItem>
              <el-icon><Setting /></el-icon>
              <span class="ml-2">系统设置</span>
            </ElDropdownItem>
            <ElDropdownItem divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              <span class="ml-2 text-red-500">退出登录</span>
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </div>
  </div>
</template>

<style scoped>
.header-container {
  background-image: linear-gradient(to right, rgb(59, 124, 254), rgb(108, 92, 231));
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.notification-badge :deep(.el-badge__content) {
  background-color: #ff4d4f;
  border: 2px solid #fff;
}

.el-dropdown-menu {
  padding: 4px 0;
  margin-top: 8px;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
}

.el-dropdown-menu__item:hover {
  background-color: #f5f7fa;
}

.el-icon {
  font-size: 16px;
}

/* 下拉菜单箭头颜色 */
:deep(.el-popper__arrow::before) {
  background: linear-gradient(45deg, #fff, #fff);
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
}
</style> 