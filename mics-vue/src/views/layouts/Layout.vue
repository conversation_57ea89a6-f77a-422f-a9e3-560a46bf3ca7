/**
 * @file Layout.vue
 * @description 主布局组件，负责整体页面结构的组织和管理
 * <AUTHOR> Assistant
 */
<script setup lang="ts">
import { ref } from 'vue'
import { ElContainer, ElAside, ElMain, ElHeader } from 'element-plus'
import Header from './Header.vue'
import Sidebar from './Sidebar.vue'
import ContentLayout from './ContentLayout.vue'

// 侧边栏折叠状态
const isCollapse = ref(false)

/**
 * 切换侧边栏折叠状态
 * 当用户点击折叠按钮时触发
 */
const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <ElHeader class="fixed top-0 left-0 right-0 z-30 h-16 p-0">
      <Header @toggle-sidebar="toggleSidebar" />
    </ElHeader>

    <!-- 主容器 -->
    <ElContainer class="h-screen pt-16">
      <!-- 侧边栏 -->
      <ElAside 
        :width="isCollapse ? '64px' : '240px'" 
        class="fixed left-0 top-16 bottom-0 bg-white shadow-md transition-all z-20"
      >
        <Sidebar :is-collapse="isCollapse" />
      </ElAside>

      <!-- 主内容区域 -->
      <ElMain 
        :class="[
          'transition-all duration-300 min-h-screen bg-gray-50 py-5 px-6',
          isCollapse ? 'ml-[64px]' : 'ml-[240px]'
        ]"
      >
        <ContentLayout />
      </ElMain>
    </ElContainer>
  </div>
</template>

<style scoped>
.layout-container {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.el-aside {
  transition: width 0.3s;
  overflow-x: hidden;
  border-right: 1px solid #e6e6e6;
}

:deep(.el-header) {
  padding: 0;
}

/* 自定义滚动条样式 */
.el-aside::-webkit-scrollbar {
  width: 6px;
}

.el-aside::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.el-aside::-webkit-scrollbar-track {
  background: #f5f7fa;
}
</style> 