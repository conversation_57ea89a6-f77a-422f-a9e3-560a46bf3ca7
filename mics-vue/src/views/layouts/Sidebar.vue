/**
 * @file Sidebar.vue
 * @description 侧边栏导航组件，支持多级菜单和路由跳转
 * <AUTHOR> Assistant
 */
<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMenu, ElMenuItem, ElSubMenu } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import * as ElementPlusIcons from '@element-plus/icons-vue'
import { useMenuStore } from '@/stores/menu.ts'
import { Loading, Warning } from '@element-plus/icons-vue'
import SidebarMenuItem from './SidebarMenuItem.vue'
import type { MenuItem } from '@/api/types/menu'

// 定义组件属性
const props = defineProps<{
  isCollapse: boolean // 控制侧边栏折叠状态
}>()

// 路由实例
const router = useRouter()
const route = useRoute()

// 菜单状态管理
const menuStore = useMenuStore()
console.log('[Sidebar] Menu store initialized');

const menuItems = computed(() => {
  console.log('[Sidebar.menuItems] Getting authorized menu items');
  const items = menuStore.authorizedMenuItems;
  console.log('[Sidebar.menuItems] Current items:', JSON.stringify(items, null, 2));
  return items;
});

const loading = computed(() => {
  const isLoading = menuStore.loading;
  console.log('[Sidebar.loading] Loading state:', isLoading);
  return isLoading;
});

const error = computed(() => {
  const err = menuStore.error;
  if (err) {
    console.log('[Sidebar.error] Current error:', err);
  }
  return err;
});

// 当前激活的菜单项，与路由路径同步
const activeMenu = ref('')

// 根据当前路由设置激活的菜单项
const setActiveMenu = () => {
  const { path } = route
  console.log('[Sidebar.setActiveMenu] Setting active menu for path:', path);
  activeMenu.value = path
}

/**
 * 处理菜单项选择
 * @param key 选中菜单项的路由路径
 */
const handleSelect = (key: string) => {
  console.log('[Sidebar.handleSelect] Menu item selected:', key);
  const menuItem = menuStore.flatMenuItems.find(item => item.id === key)
  console.log('[Sidebar.handleSelect] Found menu item:', menuItem);
  if (menuItem?.path) {
    console.log('[Sidebar.handleSelect] Navigating to:', menuItem.path);
    router.push(menuItem.path)
  }
}

/**
 * 获取图标组件
 * @param iconName 图标名称
 */
const getIcon = (iconName?: string) => {
  if (!iconName) return null
  console.log('[Sidebar.getIcon] Getting icon for:', iconName);
  return ElementPlusIcons[iconName as keyof typeof ElementPlusIcons]
}

// 重试加载
const retryLoad = async () => {
  console.log('[Sidebar.retryLoad] Retrying menu load');
  try {
    await menuStore.fetchMenuItems()
    console.log('[Sidebar.retryLoad] Retry successful');
  } catch (err) {
    console.error('[Sidebar.retryLoad] Retry failed:', err)
  }
}

// 组件挂载时获取菜单数据
onMounted(async () => {
  console.log('[Sidebar.onMounted] Component mounted, fetching menu items');
  try {
    await menuStore.fetchMenuItems()
    console.log('[Sidebar.onMounted] Menu items fetched successfully');
    setActiveMenu()
  } catch (err) {
    console.error('[Sidebar.onMounted] Failed to initialize menu:', err)
  }
})

// 监听路由变化
watch(() => route.path, (newPath) => {
  console.log('[Sidebar.watch] Route path changed to:', newPath);
  setActiveMenu()
})
</script>

<template>
  <div class="sidebar-container h-full">
    <div v-if="loading" class="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
    <div v-else-if="error" class="error">
      <el-icon><Warning /></el-icon>
      <span>{{ error.message || '加载失败' }}</span>
      <el-button type="text" @click="retryLoad">重试</el-button>
    </div>
    <el-menu
      v-else
      :default-active="activeMenu"
      :collapse="isCollapse"
      :collapse-transition="false"
      @select="handleSelect"
      class="h-full border-0"
    >
      <template v-for="item in menuItems" :key="item.id">
        <SidebarMenuItem :menu-item="item" />
      </template>
    </el-menu>
  </div>
</template>

<style scoped>
.sidebar-container {
  background-color: #fff;
}

:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item) {
  height: 50px;
  line-height: 50px;
  margin: 4px 0;
  font-size: 15px !important;
}

:deep(.el-sub-menu .el-menu-item) {
  height: 44px;
  line-height: 44px;
  padding-left: 60px !important;
  font-size: 14px !important;
}

:deep(.el-menu-item.is-active) {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border-right: 2px solid var(--el-color-primary);
}

:deep(.el-sub-menu__title) {
  height: 50px;
  line-height: 50px;
  font-size: 15px !important;
}

:deep(.el-menu--collapse) {
  width: 64px;
}

:deep(.el-menu:not(.el-menu--collapse)) {
  width: 240px;
}

/* 图标样式 */
.el-icon {
  font-size: 18px;
  margin-right: 12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 子菜单样式 */
.submenu-item {
  font-size: 14px;
}

/* 悬停效果 */
:deep(.el-menu-item:not(.is-active):hover),
:deep(.el-sub-menu__title:hover) {
  background-color: var(--el-color-primary-light-9) !important;
  color: var(--el-color-primary) !important;
}

/* 子菜单展开图标 */
:deep(.el-sub-menu__icon-arrow) {
  font-size: 12px;
  margin-top: -6px;
  right: 16px;
}

/* 菜单项内容间距 */
:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  padding: 0 16px !important;
}

/* 子菜单内容缩进 */
:deep(.el-menu--inline) {
  padding: 4px 0;
}

.loading,
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--el-text-color-secondary);
  gap: 8px;
}
</style>
