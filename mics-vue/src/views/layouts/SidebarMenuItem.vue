/**
 * @file SidebarMenuItem.vue
 * @description 侧边栏菜单项组件，支持递归渲染
 */

<template>
  <el-sub-menu
    v-if="menuItem.children?.length"
    :index="menuItem.id"
  >
    <template #title>
      <el-icon v-if="menuItem.icon">
        <component :is="getIcon(menuItem.icon)" />
      </el-icon>
      <span>{{ menuItem.title }}</span>
    </template>
    <sidebar-menu-item
      v-for="child in menuItem.children"
      :key="child.id"
      :menu-item="child"
    />
  </el-sub-menu>

  <el-menu-item
    v-else
    :index="menuItem.id"
    @click="handleMenuClick"
  >
    <el-icon v-if="menuItem.icon">
      <component :is="getIcon(menuItem.icon)" />
    </el-icon>
    <template #title>
      <span>{{ menuItem.title }}</span>
    </template>
  </el-menu-item>
</template>

<script setup lang="ts">
import { ElSubMenu, ElMenuItem, ElIcon } from 'element-plus';
import * as ElementPlusIcons from '@element-plus/icons-vue';
import type { MenuItem } from '@/api/menu/types.ts';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps<{
  menuItem: MenuItem;
}>();

/**
 * 获取图标组件
 * @param iconName 图标名称
 */
const getIcon = (iconName: string) => {
  return ElementPlusIcons[iconName as keyof typeof ElementPlusIcons];
};

/**
 * 处理菜单点击事件
 */
const handleMenuClick = () => {
  if (props.menuItem.path) {
    router.push(props.menuItem.path);
  }
};
</script>

<style scoped>
.el-menu-item.is-active {
  background-color: var(--el-menu-hover-bg-color);
}
</style>
