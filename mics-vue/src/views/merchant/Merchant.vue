<script setup lang="ts">
import { ref } from 'vue'
import MerchantList from './components/MerchantList.vue'
import MerchantDetail from './components/MerchantDetail.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 页面状态
const currentView = ref<'list' | 'detail'>('list')
const selectedMerchantId = ref<string>('')

// 查看商户详情
const handleViewDetail = (merchantId: string) => {
  selectedMerchantId.value = merchantId
  currentView.value = 'detail'
}

// 编辑商户
const handleEdit = (merchantId: string) => {
  router.push(`/merchant/edit/${merchantId}`)
}

// 返回列表
const handleBack = () => {
  currentView.value = 'list'
  selectedMerchantId.value = ''
}
</script>

<template>
  <div class="merchant-page">
    
    <!-- 商户列表 -->
    <MerchantList
      v-if="currentView === 'list'"
      @view-detail="handleViewDetail"
      @edit="handleEdit"
    />

    <!-- 商户详情 -->
    <MerchantDetail
      v-else
      :merchant-id="selectedMerchantId"
      @close="handleBack"
      @edit="handleEdit"
    />
  </div>
</template>

<style scoped>
.merchant-page {
  width: 100%;
}
</style> 