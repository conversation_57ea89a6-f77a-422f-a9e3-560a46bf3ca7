<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { IMerchantDetail } from '@/api/merchant/types'
import { getMerchantDetail } from '@/api/merchant'
import { ElMessage } from 'element-plus'
import ApplicationManagement from '../../application/ApplicationManagement.vue'

const props = defineProps<{
  merchantId: string
}>()

const emit = defineEmits(['close', 'edit'])

// 商户详情数据
const merchantDetail = ref<IMerchantDetail>()
const loading = ref(false)

// 获取商户详情
const fetchDetail = async () => {
  loading.value = true
  try {
    merchantDetail.value = await getMerchantDetail(props.merchantId)
  } catch (error) {
    ElMessage.error('获取商户详情失败')
  } finally {
    loading.value = false
  }
}

// 编辑商户
const handleEdit = () => {
  emit('edit', props.merchantId)
}

// 返回列表
const handleBack = () => {
  emit('close')
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<template>
  <div class="merchant-detail">
    <el-card v-loading="loading" class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-medium">商户详情</span>
          <div class="flex space-x-2">
            <el-button type="primary" @click="handleEdit">编辑</el-button>
            <el-button @click="handleBack">返回</el-button>
          </div>
        </div>
      </template>

      <div v-if="merchantDetail" class="grid grid-cols-2 gap-4">
        <div class="info-item">
          <span class="label">商户编码：</span>
          <span>{{ merchantDetail.code }}</span>
        </div>
        <div class="info-item">
          <span class="label">商户名称：</span>
          <span>{{ merchantDetail.name }}</span>
        </div>
        <div class="info-item">
          <span class="label">联系人：</span>
          <span>{{ merchantDetail.contactName }}</span>
        </div>
        <div class="info-item">
          <span class="label">联系电话：</span>
          <span>{{ merchantDetail.contactPhone }}</span>
        </div>
        <div class="info-item">
          <span class="label">联系邮箱：</span>
          <span>{{ merchantDetail.contactEmail }}</span>
        </div>
        <div class="info-item">
          <span class="label">状态：</span>
          <el-tag :type="merchantDetail.status === 'ACTIVE' ? 'success' : 'danger'">
            {{ merchantDetail.status }}
          </el-tag>
        </div>
        <div class="info-item col-span-2">
          <span class="label">地址：</span>
          <span>{{ merchantDetail.address }}</span>
        </div>
        <div class="info-item">
          <span class="label">法人代表：</span>
          <span>{{ merchantDetail.legalPerson }}</span>
        </div>
        <div class="info-item">
          <span class="label">身份证号：</span>
          <span>{{ merchantDetail.legalPersonIdCard }}</span>
        </div>
        <div class="info-item">
          <span class="label">开户行：</span>
          <span>{{ merchantDetail.bankName }}</span>
        </div>
        <div class="info-item">
          <span class="label">银行账号：</span>
          <span>{{ merchantDetail.bankAccount }}</span>
        </div>
        <div class="info-item col-span-2">
          <span class="label">营业执照：</span>
          <el-image
            v-if="merchantDetail.businessLicense"
            :src="merchantDetail.businessLicense"
            fit="cover"
            class="w-64 h-40 object-cover"
          />
        </div>
        <div class="info-item col-span-2">
          <span class="label">备注：</span>
          <span>{{ merchantDetail.remarks }}</span>
        </div>
      </div>
    </el-card>

    <!-- 应用管理 -->
    <ApplicationManagement
      v-if="merchantDetail"
      :merchant-id="merchantDetail.cid"
      :applications="merchantDetail.applications"
      @refresh="fetchDetail"
    />
  </div>
</template>

<style scoped>
.merchant-detail {
  @apply space-y-4;
}

.info-item {
  @apply py-2;
}

.label {
  @apply font-medium mr-2 text-gray-600;
}
</style>
