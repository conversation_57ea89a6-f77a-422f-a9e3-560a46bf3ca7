<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { IMerchant, IMerchantQueryParams, ICreateMerchantRequest, IUpdateMerchantRequest } from '@/api/merchant/types'
import { MerchantStatus, MerchantType } from '@/api/merchant/types'
import { getMerchantList, updateMerchantStatus, deleteMerchant, createMerchant, getMerchantDetail, updateMerchant } from '@/api/merchant'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDictOptions, getDictLabel } from '@/utils/dict'

const emit = defineEmits(['view-detail', 'edit'])

// 使用字典工具获取枚举值
const { options: statusOptions, loading: statusLoading } = useDictOptions('data_state')
const { options: merchantTypeOptions, loading: typeLoading } = useDictOptions('merchant_types')
const { options: channelOptions, loading: channelLoading } = useDictOptions('payment_channel')

// 查询参数
const queryParams = ref<IMerchantQueryParams>({
  pageNo: 1,
  pageSize: 10,
  keyword: '',
  status: undefined,
  merchantType: undefined,
  channelCode: undefined
})

// 表格数据
const tableData = ref<IMerchant[]>([])
const total = ref(0)
const loading = ref(false)

// 商户弹窗
const merchantDialogVisible = ref(false)
const merchantFormRef = ref()
const merchantFormLoading = ref(false)
const isEdit = ref(false)
const merchantForm = ref<ICreateMerchantRequest | IUpdateMerchantRequest>({
  merchantUniqueNumber: '',
  merchantNumber: '',
  channelCode: '',
  merchantType: '',
  comment: '',
  status: 'open'
})

// 表单校验规则
const merchantFormRules = {
  merchantUniqueNumber: [
    { required: true, message: '请输入商户唯一编号', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  merchantNumber: [
    { required: true, message: '请输入商户号', trigger: 'blur' },
    { min: 3, max: 30, message: '长度在 3 到 30 个字符', trigger: 'blur' }
  ],
  channelCode: [
    { required: true, message: '请选择渠道编码', trigger: 'change' }
  ],
  merchantType: [
    { required: true, message: '请选择商户类型', trigger: 'change' }
  ]
}

// 获取商户列表
const fetchList = async () => {
  loading.value = true
  try {
    const response = await getMerchantList(queryParams.value)
    // 处理数据，构建树形结构
    const merchants = response.data
    const merchantMap = new Map()
    const rootMerchants: IMerchant[] = []

    // 第一步：创建映射
    merchants.forEach(merchant => {
      merchantMap.set(merchant.cid, { ...merchant, children: [] })
    })

    // 第二步：构建树形结构
    merchants.forEach(merchant => {
      const merchantWithChildren = merchantMap.get(merchant.cid)
      if (merchant.parentMerchantCid && merchantMap.has(merchant.parentMerchantCid)) {
        // 有父节点，添加到父节点的children中
        const parent = merchantMap.get(merchant.parentMerchantCid)
        parent.children.push(merchantWithChildren)
      } else {
        // 没有父节点或父节点不在当前数据中，作为根节点
        rootMerchants.push(merchantWithChildren)
      }
    })

    tableData.value = rootMerchants
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取商户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  queryParams.value.pageNo = 1
  fetchList()
}

// 重置搜索
const handleReset = () => {
  queryParams.value = {
    pageNo: 1,
    pageSize: 10,
    keyword: '',
    status: undefined,
    merchantType: undefined,
    channelCode: undefined
  }
  fetchList()
}

// 分页变化
const handlePageChange = (page: number) => {
  queryParams.value.pageNo = page
  fetchList()
}

// 每页条数变化
const handleSizeChange = (size: number) => {
  queryParams.value.pageSize = size
  queryParams.value.pageNo = 1
  fetchList()
}

// 查看详情
const handleView = (row: IMerchant) => {
  emit('view-detail', row.cid)
}

// 编辑商户
const handleEdit = (row: IMerchant) => {
  openEditDialog(row.cid)
}

// 删除商户
const handleDelete = async (row: IMerchant) => {
  try {
    await ElMessageBox.confirm('确认删除该商户吗？', '提示', {
      type: 'warning'
    })
    await deleteMerchant(row.cid)
    ElMessage.success('删除成功')
    fetchList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 更新状态
const handleStatusChange = async (row: IMerchant) => {
  try {
    const newStatus = row.status === 'open' ? 'close' : 'open'
    await updateMerchantStatus(row.cid, newStatus)
    ElMessage.success('状态更新成功')
    fetchList()
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

// 获取渠道类型的标签类型
const getChannelTypeTagType = (channelCode: string): string => {
  const typeMap: Record<string, string> = {
    'weixin': 'primary',
    'alipay': 'success',
    'tonglian': 'warning',
    'xianbank': 'info',
    'douyinpay': 'danger'
  }
  return typeMap[channelCode] || 'info'
}

// 获取商户类型样式
const getMerchantTypeClass = (merchantType: string): string => {
  const classMap: Record<string, string> = {
    'ordinary_merchant': 'text-gray-600',
    'sub_merchant': 'text-blue-600'
  }
  return classMap[merchantType] || 'text-gray-600'
}

// 打开新建商户弹窗
const openCreateDialog = () => {
  isEdit.value = false
  merchantForm.value = {
    merchantUniqueNumber: '',
    merchantNumber: '',
    channelCode: '',
    merchantType: '',
    comment: '',
    status: 'open'
  }
  merchantDialogVisible.value = true
}

// 打开编辑商户弹窗
const openEditDialog = async (cid: string) => {
  isEdit.value = true
  merchantFormLoading.value = true

  try {
    const merchantDetail = await getMerchantDetail(cid)
    merchantForm.value = {
      cid: merchantDetail.cid,
      merchantUniqueNumber: merchantDetail.merchantUniqueNumber,
      merchantNumber: merchantDetail.merchantNumber,
      channelCode: merchantDetail.channelCode,
      merchantType: merchantDetail.merchantType,
      comment: merchantDetail.comment || '',
      status: merchantDetail.status
    }
    merchantDialogVisible.value = true
  } catch (error) {
    console.error('获取商户详情失败', error)
    ElMessage.error('获取商户详情失败')
  } finally {
    merchantFormLoading.value = false
  }
}

// 提交商户表单
const submitMerchantForm = async () => {
  if (!merchantFormRef.value) return

  merchantFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return

    merchantFormLoading.value = true
    try {
      if (isEdit.value) {
        // 编辑模式
        await updateMerchant(merchantForm.value as IUpdateMerchantRequest)
        ElMessage.success('更新商户成功')
      } else {
        // 新建模式
        await createMerchant(merchantForm.value as ICreateMerchantRequest)
        ElMessage.success('创建商户成功')
      }
      merchantDialogVisible.value = false
      fetchList()
    } catch (error) {
      console.error(isEdit.value ? '更新商户失败' : '创建商户失败', error)
      ElMessage.error(isEdit.value ? '更新商户失败' : '创建商户失败')
    } finally {
      merchantFormLoading.value = false
    }
  })
}

// 格式化日期时间
const formatDateTime = (dateStr: string): string => {
  if (!dateStr) return '-'
  return dateStr
}

// 初始化
onMounted(() => {
  fetchList()
})
</script>

<template>
  <div class="merchant-list bg-white shadow-sm w-full p-0">
    <!-- 页面标题 -->
    <div class="px-6 pt-6 mb-6">
      <h2 class="text-2xl font-bold text-gray-800">商户管理</h2>
      <p class="text-gray-500 mt-1">
        管理系统商户信息，包含商户基本信息、支付渠道等
      </p>
    </div>

    <!-- 搜索过滤区 -->
    <div class="bg-gray-50 p-4 mx-6 rounded-lg mb-6">
      <div class="grid grid-cols-1 md:grid-cols-6 gap-2">
        <el-input
          v-model="queryParams.keyword"
          placeholder="搜索商户号"
          clearable
          @keyup.enter="handleSearch"
          class="max-w-[180px]"
        >
          <template #prefix>
            <i class="fas fa-search text-gray-400"></i>
          </template>
        </el-input>

        <el-select
          v-model="queryParams.status"
          placeholder="商户状态"
          clearable
          :loading="statusLoading"
          class="max-w-[180px]"
        >
          <template #prefix>
            <glossary-tooltip term="商户类型" :show-icon="false">
              <i class="fas fa-info-circle text-gray-400"></i>
            </glossary-tooltip>
          </template>
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <el-select
          v-model="queryParams.merchantType"
          placeholder="商户类型"
          clearable
          :loading="typeLoading"
          class="max-w-[180px]"
        >
          <template #prefix>
            <glossary-tooltip term="商户类型" :show-icon="false">
              <i class="fas fa-info-circle text-gray-400"></i>
            </glossary-tooltip>
          </template>
          <el-option
            v-for="option in merchantTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <el-select
          v-model="queryParams.channelCode"
          placeholder="渠道类型"
          clearable
          :loading="channelLoading"
          class="max-w-[180px]"
        >
          <template #prefix>
            <glossary-tooltip term="支付渠道" :show-icon="false">
              <i class="fas fa-info-circle text-gray-400"></i>
            </glossary-tooltip>
          </template>
          <el-option
            v-for="option in channelOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <div class="flex space-x-2 md:col-span-2">
          <el-button type="primary" @click="handleSearch">
            <i class="fas fa-search mr-1"></i> 搜索
          </el-button>
          <el-button @click="handleReset">
            <i class="fas fa-redo mr-1"></i> 重置
          </el-button>
          <el-button type="success" @click="openCreateDialog">
            <i class="fas fa-plus mr-1"></i> 新建
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="px-0 w-full">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        row-key="cid"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        style="width: 100%"
      >
        <el-table-column prop="merchantUniqueNumber" label="商户唯一编号" width="200">
          <template #header>
            <glossary-tooltip term="商户唯一编号">商户唯一编号</glossary-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="merchantNumber" label="商户号" width="150">
          <template #header>
            <glossary-tooltip term="商户号">商户号</glossary-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="渠道类型" width="120">
          <template #header>
            <glossary-tooltip term="支付渠道">渠道类型</glossary-tooltip>
          </template>
          <template #default="{ row }">
            <el-tag
              :type="getChannelTypeTagType(row.channelCode)"
              size="small"
              class="rounded-full"
            >
              {{ getDictLabel('payment_channel', row.channelCode, row.channelCode) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="商户类型" width="120">
          <template #header>
            <glossary-tooltip term="商户类型">商户类型</glossary-tooltip>
          </template>
          <template #default="{ row }">
            <span :class="getMerchantTypeClass(row.merchantType)">
              {{ getDictLabel('merchant_types', row.merchantType, '未知类型') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="渠道账号" width="150">
          <template #default="{ row }">
            <el-link
              type="primary"
              v-if="row.channelAccountId"
            >
              {{ row.channelAccountId }}
            </el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.status === 'open' ? 'success' : 'info'"
              size="small"
              class="rounded-full px-3"
            >
              {{ getDictLabel('data_state', row.status, row.status === 'open' ? '启用' : '禁用') }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-2">
              <el-button
                type="primary"
                size="small"
                text
                @click="handleView(row)"
                class="text-blue-600"
              >
                <i class="el-icon-view mr-1"></i>查看
              </el-button>
              <el-button
                type="primary"
                size="small"
                text
                @click="handleEdit(row)"
                class="text-blue-600"
              >
                <i class="el-icon-edit mr-1"></i>编辑
              </el-button>
              <el-button
                :type="row.status === 'open' ? 'danger' : 'success'"
                size="small"
                text
                @click="handleStatusChange(row)"
              >
                <i :class="row.status === 'open' ? 'el-icon-close' : 'el-icon-check'" class="mr-1"></i>
                {{ row.status === 'open' ? '禁用' : '启用' }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="flex justify-end mt-4 px-6 pb-6">
      <el-pagination
        v-model:current-page="queryParams.pageNo"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 商户弹窗 -->
    <el-dialog
      v-model="merchantDialogVisible"
      :title="isEdit ? '编辑商户' : '新建商户'"
      width="500px"
      append-to-body
      v-loading="merchantFormLoading"
    >
      <el-form
        ref="merchantFormRef"
        :model="merchantForm"
        :rules="merchantFormRules"
        label-width="120px"
      >
        <el-form-item label="商户唯一编号" prop="merchantUniqueNumber">
          <template #label>
            <glossary-tooltip term="商户唯一编号" :show-icon="false">商户唯一编号</glossary-tooltip>
          </template>
          <el-input 
            v-model="merchantForm.merchantUniqueNumber" 
            placeholder="请输入商户唯一编号"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="商户号" prop="merchantNumber">
          <template #label>
            <glossary-tooltip term="商户号" :show-icon="false">商户号</glossary-tooltip>
          </template>
          <el-input 
            v-model="merchantForm.merchantNumber" 
            placeholder="请输入商户号"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="渠道编码" prop="channelCode">
          <template #label>
            <glossary-tooltip term="支付渠道" :show-icon="false">渠道编码</glossary-tooltip>
          </template>
          <el-select
            v-model="merchantForm.channelCode"
            placeholder="请选择渠道编码"
            style="width: 100%"
            :loading="channelLoading"
          >
            <el-option
              v-for="option in channelOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商户类型" prop="merchantType">
          <template #label>
            <glossary-tooltip term="商户类型" :show-icon="false">商户类型</glossary-tooltip>
          </template>
          <el-select
            v-model="merchantForm.merchantType"
            placeholder="请选择商户类型"
            style="width: 100%"
            :loading="typeLoading"
          >
            <el-option
              v-for="option in merchantTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="isEdit">
          <el-select
            v-model="merchantForm.status"
            placeholder="请选择状态"
            style="width: 100%"
            :loading="statusLoading"
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="comment">
          <el-input
            v-model="merchantForm.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="merchantDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="merchantFormLoading"
            @click="submitMerchantForm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.merchant-list {
  min-height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  width: 100%;
}

/* 自定义表格头部样式 */
:deep(.el-table th) {
  background: linear-gradient(to right, #f6f8fb, #eef2f7);
  color: #64748b;
  font-weight: 500;
  padding: 12px 0;
}

:deep(.el-table) {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.el-table__inner-wrapper) {
  width: 100% !important;
}

:deep(.el-table__header-wrapper),
:deep(.el-scrollbar__wrap) {
  width: 100% !important;
}

:deep(.el-table__header) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__body) {
  width: 100% !important;
  table-layout: fixed;
}

/* 自定义分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: var(--el-color-primary);
  color: white;
  border-radius: 0.375rem;
  box-shadow: 0 2px 5px rgba(59, 124, 254, 0.3);
}

:deep(.el-pagination.is-background .el-pager li) {
  margin: 0 3px;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled):hover) {
  background-color: #e8f0ff;
  color: var(--el-color-primary);
}
</style>
