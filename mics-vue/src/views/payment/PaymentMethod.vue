<script setup lang="ts">
import { ref } from 'vue';
import PaymentMethodListComponent from './components/PaymentMethodList.vue';
import PaymentMethodDetail from './components/PaymentMethodDetail.vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 页面状态
const currentView = ref<'list' | 'detail'>('list');
const selectedPaymentMethodId = ref<string>('');

// 查看支付方式详情
const handleViewDetail = (paymentMethodId: string) => {
  selectedPaymentMethodId.value = paymentMethodId;
  currentView.value = 'detail';
};

// 编辑支付方式
const handleEdit = (paymentMethodId: string) => {
  router.push(`/payment/method/detail/${paymentMethodId}`);
};

// 返回列表
const handleBack = () => {
  currentView.value = 'list';
  selectedPaymentMethodId.value = '';
};
</script>

<template>
  <div class="payment-method-page">
    <!-- 支付方式列表 -->
    <PaymentMethodListComponent
      v-if="currentView === 'list'"
      @view-detail="handleViewDetail"
      @edit="handleEdit"
    />

    <!-- 支付方式详情 -->
    <PaymentMethodDetail
      v-else
      :payment-method-id="selectedPaymentMethodId"
      @close="handleBack"
      @edit="handleEdit"
    />
  </div>
</template>

<style scoped>
.payment-method-page {
  width: 100%;
}
</style> 