<script setup lang="ts">
import { ref } from 'vue'
import PaymentSceneListComponent from './components/PaymentSceneList.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 页面状态
const currentView = ref<'list' | 'detail'>('list')
const selectedSceneId = ref<string>('')

// 查看支付场景详情
const handleViewDetail = (sceneId: string) => {
  selectedSceneId.value = sceneId
  router.push(`/payment/scene/detail/${sceneId}`)
}

// 编辑支付场景
const handleEdit = (sceneId: string) => {
  router.push(`/payment/scene/edit/${sceneId}`)
}
</script>

<template>
  <div class="payment-scene-page">
    <!-- 支付场景列表 -->
    <PaymentSceneListComponent
      v-if="currentView === 'list'"
      @view-detail="handleViewDetail"
      @edit="handleEdit"
    />
  </div>
</template>

<style scoped>
.payment-scene-page {
  width: 100%;
}
</style> 