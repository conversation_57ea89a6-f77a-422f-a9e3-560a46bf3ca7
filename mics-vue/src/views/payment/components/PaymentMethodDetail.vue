<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getPaymentMethodDetail } from '@/api/payment';
import type { IPaymentMethod } from '@/api/payment/types';
import { ElMessage } from 'element-plus';
import { createLogger } from '@/utils/logger';

const logger = createLogger('PaymentMethodDetail');

// 定义属性
const props = defineProps<{
  paymentMethodId: string;
}>();

// 定义事件
const emit = defineEmits(['close', 'edit']);

// 支付方式详情
const paymentMethod = ref<IPaymentMethod | null>(null);
const loading = ref(false);

// 获取支付方式详情
const fetchDetail = async () => {
  if (!props.paymentMethodId) return;
  
  loading.value = true;
  try {
    const data = await getPaymentMethodDetail(props.paymentMethodId);
    paymentMethod.value = data;
  } catch (error) {
    logger.error('获取支付方式详情失败', error);
    ElMessage.error('获取支付方式详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回列表
const handleBack = () => {
  emit('close');
};

// 编辑支付方式
const handleEdit = () => {
  emit('edit', props.paymentMethodId);
};

// 初始化获取详情
onMounted(() => {
  fetchDetail();
});
</script>

<template>
  <div class="payment-method-detail">
    <el-card v-loading="loading">
      <!-- 头部 -->
      <div class="flex justify-between items-center mb-6">
        <div class="flex items-center">
          <el-button @click="handleBack">
            <el-icon><Back /></el-icon>返回
          </el-button>
          <h2 class="text-xl font-bold ml-4">
            <glossary-tooltip term="支付方式">支付方式</glossary-tooltip>详情
          </h2>
        </div>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>编辑
        </el-button>
      </div>
      
      <!-- 详情内容 -->
      <div v-if="paymentMethod" class="payment-detail-content">
        <div class="flex items-center mb-6">
          <el-image
            :src="paymentMethod.icon"
            class="h-16 w-16 rounded mr-4"
            fit="cover"
          >
            <template #error>
              <div class="h-16 w-16 flex items-center justify-center bg-gray-200 rounded">
                <el-icon :size="30"><Money /></el-icon>
              </div>
            </template>
          </el-image>
          <div>
            <h3 class="text-lg font-bold">{{ paymentMethod.name }}</h3>
            <p class="text-gray-500">{{ paymentMethod.code }}</p>
          </div>
        </div>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="状态">
            <el-tag :type="paymentMethod.status === 'ENABLED' ? 'success' : 'info'">
              {{ paymentMethod.status === 'ENABLED' ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ paymentMethod.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="关联支付产品" :span="2">
            <template #label>
              <glossary-tooltip term="支付产品">关联支付产品</glossary-tooltip>
            </template>
            <div class="flex flex-wrap gap-2">
              <el-tag v-for="product in paymentMethod.products" :key="product.code" size="small">
                {{ product.name }}
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="关联场景数">
            <template #label>
              <glossary-tooltip term="支付场景">关联场景数</glossary-tooltip>
            </template>
            {{ paymentMethod.sceneCount }}
          </el-descriptions-item>
          <el-descriptions-item label="关联渠道数">
            <template #label>
              <glossary-tooltip term="支付渠道">关联渠道数</glossary-tooltip>
            </template>
            {{ paymentMethod.channelCount }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else-if="!loading" class="text-center py-8 text-gray-500">
        未找到支付方式信息
      </div>
    </el-card>
  </div>
</template>

<style scoped>
.payment-method-detail {
  padding: 20px;
}
</style> 