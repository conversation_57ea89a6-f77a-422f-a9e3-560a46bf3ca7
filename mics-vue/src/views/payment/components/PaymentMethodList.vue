<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { getPaymentMethodList, updatePaymentMethodStatus, createPaymentMethod, updatePaymentMethod, getPaymentMethodDetail } from '@/api/payment';
import type { IPaymentMethod, IPaymentMethodQueryParams, PaymentMethodStatus, ICreatePaymentMethodRequest, IUpdatePaymentMethodRequest } from '@/api/payment/types';
import { PaymentMethodStatusMap } from '@/api/payment/types';
import { ElMessage, ElMessageBox } from 'element-plus';
import { createLogger } from '@/utils/logger';
import { Money, Edit, TrendCharts, Plus, Refresh } from '@element-plus/icons-vue';
import { useDictOptions, getDictLabel } from '@/utils/dict';

const logger = createLogger('PaymentMethodList');

// 定义事件
const emit = defineEmits(['view-detail', 'edit']);

// 查询参数
const queryParams = reactive<IPaymentMethodQueryParams>({
  keyword: '',
  status: undefined,
  paymentProduct: '',
  startTime: '',
  endTime: '',
  page: 1,
  pageSize: 10
});

// 表格数据
const tableData = ref<IPaymentMethod[]>([]);
const total = ref(0);
const loading = ref(false);

// 从数据字典获取状态选项和支付产品选项
const { options: statusOptions, loading: statusLoading } = useDictOptions('data_state');
const { options: paymentProductOptions, loading: productsLoading } = useDictOptions('channel_products');

// 支付方式弹窗相关
const paymentMethodDialogVisible = ref(false);
const paymentMethodFormRef = ref();
const paymentMethodFormLoading = ref(false);
const isEdit = ref(false);
const paymentMethodForm = ref<ICreatePaymentMethodRequest | IUpdatePaymentMethodRequest>({
  name: '',
  code: '',
  icon: '',
  status: 'ENABLED',
  products: [],
  comment: ''
});

// 表单校验规则
const paymentMethodFormRules = {
  name: [
    { required: true, message: '请输入支付方式名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入支付方式编码', trigger: 'blur' },
    { min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur' }
  ],
  products: [
    { required: true, message: '请选择关联支付产品', trigger: 'change' }
  ]
};

/**
 * 获取图标URL
 */
function getIconUrl(icon: string): string {
  if (!icon) return '';
  // 使用静态路径，避免在模板中使用import.meta.url
  return `/src/assets/icons/${icon}`;
}

/**
 * 获取支付方式列表
 */
async function fetchList() {
  loading.value = true;
  try {
    const res = await getPaymentMethodList(queryParams);
    console.log('支付方式列表API响应:', res); // 添加调试日志
    tableData.value = res.data || [];
    total.value = res.total || 0;
  } catch (error) {
    logger.error('获取支付方式列表失败', error);
    ElMessage.error('获取支付方式列表失败');
  } finally {
    loading.value = false;
  }
}

/**
 * 处理查询
 */
function handleQuery() {
  queryParams.page = 1;
  fetchList();
}

/**
 * 重置查询条件
 */
function resetQuery() {
  Object.assign(queryParams, {
    keyword: '',
    status: undefined,
    paymentProduct: '',
    startTime: '',
    endTime: '',
    page: 1
  });
  fetchList();
}

/**
 * 处理分页变化
 */
function handlePageChange(page: number) {
  queryParams.page = page;
  fetchList();
}

/**
 * 处理每页条数变化
 */
function handleSizeChange(size: number) {
  queryParams.pageSize = size;
  queryParams.page = 1;
  fetchList();
}

/**
 * 处理状态切换
 */
async function handleStatusChange(row: IPaymentMethod) {
  const isEnabled = row.status === 'open';
  const newStatus = isEnabled ? 'closed' : 'open';
  
  try {
    await ElMessageBox.confirm(
      `确认要${isEnabled ? '禁用' : '启用'}支付方式 "${row.name}" 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await updatePaymentMethodStatus(row.id, !isEnabled);
    row.status = newStatus;
    ElMessage.success(`${isEnabled ? '禁用' : '启用'}成功`);
  } catch (error) {
    if (error !== 'cancel') {
      logger.error('更新支付方式状态失败', error);
      ElMessage.error('操作失败，请重试');
      // 恢复原状态
      row.status = isEnabled ? 'open' : 'closed';
    }
  }
}

/**
 * 查看详情
 */
function handleViewDetail(row: IPaymentMethod) {
  emit('view-detail', row.id);
}

/**
 * 编辑支付方式
 */
function handleEdit(row: IPaymentMethod) {
  openEditDialog(row.id);
}

/**
 * 获取产品标签
 */
function getProductLabel(code: string): string {
  return getDictLabel('channel_products', code, code);
}

/**
 * 格式化状态
 */
function formatStatus(status: PaymentMethodStatus) {
  return getDictLabel('data_state', status, PaymentMethodStatusMap[status] || '未知');
}

/**
 * 打开新建支付方式弹窗
 */
function openCreateDialog() {
  isEdit.value = false;
  paymentMethodForm.value = {
    name: '',
    code: '',
    icon: '',
    status: 'ENABLED',
    products: [],
    comment: ''
  };
  paymentMethodDialogVisible.value = true;
}

/**
 * 打开编辑支付方式弹窗
 */
async function openEditDialog(id: string) {
  isEdit.value = true;
  paymentMethodFormLoading.value = true;

  try {
    const detail = await getPaymentMethodDetail(id);
    paymentMethodForm.value = {
      id: detail.id,
      name: detail.name,
      code: detail.code,
      icon: detail.icon,
      status: detail.status,
      products: detail.products,
      comment: detail.comment || ''
    };
    paymentMethodDialogVisible.value = true;
  } catch (error) {
    logger.error('获取支付方式详情失败', error);
    ElMessage.error('获取支付方式详情失败');
  } finally {
    paymentMethodFormLoading.value = false;
  }
}

/**
 * 提交支付方式表单
 */
async function submitPaymentMethodForm() {
  if (!paymentMethodFormRef.value) return;

  paymentMethodFormRef.value.validate(async (valid: boolean) => {
    if (!valid) return;

    paymentMethodFormLoading.value = true;
    try {
      if (isEdit.value) {
        // 编辑模式
        await updatePaymentMethod(paymentMethodForm.value as IUpdatePaymentMethodRequest);
        ElMessage.success('更新支付方式成功');
      } else {
        // 新建模式
        await createPaymentMethod(paymentMethodForm.value as ICreatePaymentMethodRequest);
        ElMessage.success('创建支付方式成功');
      }
      paymentMethodDialogVisible.value = false;
      fetchList();
    } catch (error) {
      logger.error(isEdit.value ? '更新支付方式失败' : '创建支付方式失败', error);
      ElMessage.error(isEdit.value ? '更新支付方式失败' : '创建支付方式失败');
    } finally {
      paymentMethodFormLoading.value = false;
    }
  });
}

// 初始化获取数据
onMounted(() => {
  fetchList();
});
</script>

<template>
  <div class="payment-method-list bg-white shadow-sm w-full p-0">
    <!-- 页面标题 -->
    <div class="px-6 pt-6 mb-6">
      <h2 class="text-2xl font-bold text-gray-800">
        <glossary-tooltip term="支付方式">支付方式</glossary-tooltip>管理
      </h2>
      <p class="text-gray-500 mt-1">
        管理系统支付方式信息，包含
        <glossary-tooltip term="支付产品">支付产品</glossary-tooltip>、关联
        <glossary-tooltip term="支付场景">支付场景</glossary-tooltip>和
        <glossary-tooltip term="支付渠道">支付渠道</glossary-tooltip>等
      </p>
    </div>

    <!-- 搜索过滤区 -->
    <div class="bg-gray-50 p-4 mx-6 rounded-lg mb-6">
      <div class="grid grid-cols-1 md:grid-cols-6 gap-2">
        <el-input
          v-model="queryParams.keyword"
          placeholder="搜索支付方式名称/编码"
          clearable
          @keyup.enter="handleQuery"
          class="max-w-[180px]"
        >
          <template #prefix>
            <i class="fas fa-search text-gray-400"></i>
          </template>
        </el-input>

        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          clearable
          class="max-w-[180px]"
          :loading="statusLoading"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <el-select
          v-model="queryParams.paymentProduct"
          placeholder="支付产品"
          clearable
          class="max-w-[180px]"
          :loading="productsLoading"
        >
          <el-option
            v-for="item in paymentProductOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <div class="flex space-x-2 md:col-span-3">
          <el-button type="primary" @click="handleQuery">
            <i class="fas fa-search mr-1"></i> 搜索
          </el-button>
          <el-button @click="resetQuery">
            <i class="fas fa-redo mr-1"></i> 重置
          </el-button>
          <el-button type="success" @click="openCreateDialog">
            <i class="fas fa-plus mr-1"></i> 新建
          </el-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="px-0 w-full">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        row-key="id"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="支付方式" min-width="180">
          <template #header>
            <glossary-tooltip term="支付方式">支付方式</glossary-tooltip>
          </template>
          <template #default="{ row }">
            <div class="flex items-center">
              <el-image
                :src="getIconUrl(row.icon)"
                class="h-8 w-8 rounded mr-3"
                fit="cover"
                :preview-src-list="[getIconUrl(row.icon)]"
              >
                <template #error>
                  <div class="h-8 w-8 flex items-center justify-center bg-gray-200 rounded">
                    <el-icon><Money /></el-icon>
                  </div>
                </template>
              </el-image>
              <span class="font-medium">{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="编码" min-width="120" />
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag 
              :type="row.status === 'open' ? 'success' : 'info'"
              size="small"
              class="rounded-full px-3"
            >
              {{ row.status === 'open' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="关联支付产品" min-width="200">
          <template #header>
            <glossary-tooltip term="支付产品">关联支付产品</glossary-tooltip>
          </template>
          <template #default="{ row }">
            <div class="flex flex-wrap gap-1">
              <el-tag v-for="product in row.products" :key="product" size="small">
                {{ getProductLabel(product) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="关联场景" align="center" width="150">
          <template #header>
            <glossary-tooltip term="支付场景">关联场景</glossary-tooltip>
          </template>
          <template #default="{ row }">
            <div class="flex flex-wrap gap-1 justify-center">
              <el-tag type="info" size="small">{{ row.payScenes?.length || 0 }}个场景</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-2">
              <el-button
                type="primary"
                size="small"
                text
                @click="handleViewDetail(row)"
                class="text-blue-600"
              >
                <el-icon><TrendCharts /></el-icon>趋势
              </el-button>
              <el-button
                type="primary"
                size="small"
                text
                @click="handleEdit(row)"
                class="text-blue-600"
              >
                <el-icon><Edit /></el-icon>编辑
              </el-button>
              <el-switch
                v-model="row.status"
                :active-value="'open'"
                :inactive-value="'closed'"
                @change="() => handleStatusChange(row)"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="flex justify-end mt-4 px-6 pb-6">
      <el-pagination
        v-model:current-page="queryParams.page"
        v-model:page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 支付方式弹窗 -->
    <el-dialog
      v-model="paymentMethodDialogVisible"
      :title="isEdit ? '编辑支付方式' : '新建支付方式'"
      width="500px"
      append-to-body
    >
      <div v-loading="paymentMethodFormLoading">
        <el-form
          ref="paymentMethodFormRef"
          :model="paymentMethodForm"
          :rules="paymentMethodFormRules"
          label-width="120px"
        >
          <el-form-item label="支付方式名称" prop="name">
            <el-tooltip content="用户在进行支付时选择的具体方式，如支付宝支付、微信支付等" placement="top">
              <span class="label-tooltip">支付方式名称</span>
            </el-tooltip>
            <el-input 
              v-model="paymentMethodForm.name" 
              placeholder="请输入支付方式名称"
            />
          </el-form-item>
          <el-form-item label="支付方式编码" prop="code">
            <el-input 
              v-model="paymentMethodForm.code" 
              placeholder="请输入支付方式编码"
              :disabled="isEdit"
            />
          </el-form-item>
          <el-form-item label="图标地址" prop="icon">
            <el-input 
              v-model="paymentMethodForm.icon" 
              placeholder="请输入图标文件名，如：wechat-pay.svg"
            />
          </el-form-item>
          <el-form-item label="关联支付产品" prop="products">
            <glossary-tooltip term="支付产品" placement="top-start">关联支付产品</glossary-tooltip>
            <el-select
              v-model="paymentMethodForm.products"
              placeholder="请选择关联支付产品"
              style="width: 100%"
              multiple
              :loading="productsLoading"
            >
              <el-option
                v-for="item in paymentProductOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="状态" prop="status" v-if="isEdit">
            <el-select
              v-model="paymentMethodForm.status"
              placeholder="请选择状态"
              style="width: 100%"
            >
              <el-option
                label="启用"
                value="open"
              />
              <el-option
                label="禁用"
                value="closed"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="comment">
            <el-input
              v-model="paymentMethodForm.comment"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="paymentMethodDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="paymentMethodFormLoading"
            @click="submitPaymentMethodForm"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.payment-method-list {
  min-height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  width: 100%;
}

/* 自定义表格头部样式 */
:deep(.el-table th) {
  background: linear-gradient(to right, #f6f8fb, #eef2f7);
  color: #64748b;
  font-weight: 500;
  padding: 12px 0;
}

:deep(.el-table) {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.el-table__inner-wrapper) {
  width: 100% !important;
}

:deep(.el-table__header-wrapper),
:deep(.el-scrollbar__wrap) {
  width: 100% !important;
}

:deep(.el-table__header) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__body) {
  width: 100% !important;
  table-layout: fixed;
}

/* 自定义分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: var(--el-color-primary);
  color: white;
  border-radius: 0.375rem;
  box-shadow: 0 2px 5px rgba(59, 124, 254, 0.3);
}

:deep(.el-pagination.is-background .el-pager li) {
  margin: 0 3px;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled):hover) {
  background-color: #e8f0ff;
  color: var(--el-color-primary);
}

.text-primary {
  color: var(--el-color-primary);
}

.label-tooltip {
  cursor: help;
  border-bottom: 1px dashed #ccc;
}
</style> 