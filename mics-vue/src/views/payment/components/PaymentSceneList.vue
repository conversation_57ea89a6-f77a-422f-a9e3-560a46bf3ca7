<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getPaymentSceneList, 
  updatePaymentSceneStatus,
  PaymentSceneStatusMap,
  PaymentSceneTypeMap,
  type IPaymentScene, 
  type PaymentSceneStatus,
  type PaymentSceneType
} from '@/api/payment/scene'
import { createLogger } from '@/utils/logger'
import { Edit, View, Check, Close, Plus, Search, Refresh } from '@element-plus/icons-vue'
import { useDictOptions, getDictLabel } from '@/utils/dict'

// 创建日志记录器
const logger = createLogger('PaymentSceneList')

// 定义组件的事件
const emit = defineEmits<{
  'viewDetail': [id: string]
  'edit': [id: string]
}>()

// 表格加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<IPaymentScene[]>([])

// 分页信息
const pagination = reactive({
  total: 0,
  page: 1,
  pageSize: 10
})

// 查询条件
const queryParams = reactive({
  keyword: '',
  status: undefined as PaymentSceneStatus | undefined,
  sceneType: undefined as PaymentSceneType | undefined
})

// 从数据字典获取状态选项和场景类型选项
const { options: statusOptions, loading: statusLoading } = useDictOptions('data_state')
const { options: sceneTypeOptions, loading: sceneTypeLoading } = useDictOptions('scene_types')
// 支付方式选项
const { options: paymentOptions, loading: paymentLoading } = useDictOptions('payment_methods')

// 获取表格数据
async function fetchTableData() {
  loading.value = true
  try {
    const { list, total, page, pageSize } = await getPaymentSceneList({
      ...queryParams,
      page: pagination.page,
      pageSize: pagination.pageSize
    })
    tableData.value = list
    pagination.total = total
    pagination.page = page
    pagination.pageSize = pageSize
  } catch (error) {
    logger.error('获取支付场景列表失败', error)
    ElMessage.error('获取支付场景列表失败')
  } finally {
    loading.value = false
  }
}

// 处理查询
function handleSearch() {
  pagination.page = 1
  fetchTableData()
}

// 处理重置
function handleReset() {
  // 重置查询条件
  queryParams.keyword = ''
  queryParams.status = undefined
  queryParams.sceneType = undefined
  // 重置分页
  pagination.page = 1
  // 重新加载数据
  fetchTableData()
}

// 处理页码变化
function handlePageChange(page: number) {
  pagination.page = page
  fetchTableData()
}

// 处理页大小变化
function handleSizeChange(size: number) {
  pagination.pageSize = size
  pagination.page = 1
  fetchTableData()
}

// 查看详情
function handleViewDetail(id: string) {
  emit('viewDetail', id)
}

// 编辑
function handleEdit(id: string) {
  emit('edit', id)
}

// 更新状态
async function handleStatusChange(id: string, status: PaymentSceneStatus) {
  try {
    // 确认操作
    await ElMessageBox.confirm(
      `确定要${status === 'DISABLED' ? '禁用' : '启用'}该支付场景吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 更新状态
    await updatePaymentSceneStatus(id, status)
    ElMessage.success(`${status === 'DISABLED' ? '禁用' : '启用'}成功`)

    // 刷新数据
    fetchTableData()
  } catch (error) {
    if (error !== 'cancel') {
      logger.error('更新支付场景状态失败', error)
      ElMessage.error('操作失败')
    }
  }
}

/**
 * 获取场景类型标签
 */
function getSceneTypeLabel(type?: PaymentSceneType): string {
  if (!type) return '未知类型'
  return getDictLabel('scene_types', type, PaymentSceneTypeMap[type] || '未知类型')
}

/**
 * 格式化状态
 */
function formatStatus(status: PaymentSceneStatus) {
  return getDictLabel('data_state', status, PaymentSceneStatusMap[status] || '未知')
}

/**
 * 获取支付方式名称
 */
function getPaymentMethodName(code: string): string {
  return getDictLabel('payment_methods', code, code)
}

/**
 * 标准化状态值（处理大小写不一致的问题）
 */
function normalizeStatus(status: string): PaymentSceneStatus {
  if (typeof status === 'string') {
    // 转为大写以匹配枚举类型
    const upperStatus = status.toUpperCase() as PaymentSceneStatus
    return upperStatus
  }
  return status as PaymentSceneStatus
}

// 组件挂载时加载数据
onMounted(() => {
  fetchTableData()
})
</script>

<template>
  <div class="payment-scene-list bg-white shadow-sm w-full p-0">
    <!-- 页面标题 -->
    <div class="px-6 pt-6 mb-6">
      <h2 class="text-2xl font-bold text-gray-800">
        <glossary-tooltip term="支付场景">支付场景</glossary-tooltip>管理
      </h2>
      <p class="text-gray-500 mt-1">
        管理系统支付场景信息，包括不同类型的支付场景及其状态
      </p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-gray-50 p-4 mx-6 rounded-lg mb-6">
      <div class="grid grid-cols-1 md:grid-cols-6 gap-2">
        <el-input
          v-model="queryParams.keyword"
          placeholder="搜索场景名称或ID"
          clearable
          @keyup.enter="handleSearch"
          class="max-w-[180px]"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-select
          v-model="queryParams.sceneType"
          placeholder="场景类型（全部）"
          clearable
          class="max-w-[180px]"
          :loading="sceneTypeLoading"
        >
          <el-option
            v-for="[key, value] in Object.entries(PaymentSceneTypeMap)"
            :key="key"
            :label="value"
            :value="key"
          />
        </el-select>
        
        <el-select
          v-model="queryParams.status"
          placeholder="状态（全部）"
          clearable
          class="max-w-[180px]"
          :loading="statusLoading"
        >
          <el-option
            v-for="[key, value] in Object.entries(PaymentSceneStatusMap)"
            :key="key"
            :label="value"
            :value="key"
          />
        </el-select>
        
        <div class="flex space-x-2 md:col-span-3">
          <el-button type="primary" @click="handleSearch">
            <el-icon class="mr-1"><Search /></el-icon> 搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon class="mr-1"><Refresh /></el-icon> 重置
          </el-button>
          <el-button type="success">
            <router-link to="/payment/scene/create" class="flex items-center">
              <el-icon class="mr-1"><Plus /></el-icon> 新建
            </router-link>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 支付场景列表 -->
    <div class="px-0 w-full">
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        row-key="id"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="场景ID" min-width="120" />
        <el-table-column prop="name" label="场景名称" min-width="150">
          <template #header>
            <glossary-tooltip term="支付场景">场景名称</glossary-tooltip>
          </template>
        </el-table-column>
        <!-- 支付方式列表 -->
        <el-table-column label="支付方式" min-width="200">
          <template #default="{ row }">
            <div class="flex flex-wrap gap-1">
              <el-tag 
                v-for="method in row.payment_list" 
                :key="method" 
                size="small" 
                type="info"
                class="mr-1 mb-1"
              >
                {{ getPaymentMethodName(method) }}
              </el-tag>
              <span v-if="!row.payment_list || row.payment_list.length === 0" class="text-gray-400 text-sm">
                未设置
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="
                normalizeStatus(row.status) === 'PUBLISHED'
                  ? 'success'
                  : normalizeStatus(row.status) === 'DRAFT'
                  ? 'warning'
                  : 'danger'
              "
              size="small"
              class="rounded-full px-3"
            >
              {{ PaymentSceneStatusMap[normalizeStatus(row.status)] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="160" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-2">
              <el-button
                type="primary"
                size="small"
                text
                @click="handleViewDetail(row.id)"
                class="text-blue-600"
              >
                <el-icon><View /></el-icon>查看
              </el-button>
              <el-button
                type="primary"
                size="small"
                text
                @click="handleEdit(row.id)"
                class="text-blue-600"
              >
                <el-icon><Edit /></el-icon>编辑
              </el-button>
              <el-switch
                :model-value="normalizeStatus(row.status) === 'PUBLISHED'"
                @change="(val) => handleStatusChange(row.id, val ? 'PUBLISHED' : 'DISABLED')"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="flex justify-end mt-4 px-6 pb-6">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<style scoped>
.payment-scene-list {
  min-height: calc(100vh - 120px);
  margin: 0;
  padding: 0;
  width: 100%;
}

/* 自定义表格头部样式 */
:deep(.el-table th) {
  background: linear-gradient(to right, #f6f8fb, #eef2f7);
  color: #64748b;
  font-weight: 500;
  padding: 12px 0;
}

:deep(.el-table) {
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

:deep(.el-table__inner-wrapper) {
  width: 100% !important;
}

:deep(.el-table__header-wrapper),
:deep(.el-scrollbar__wrap) {
  width: 100% !important;
}

:deep(.el-table__header) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__body) {
  width: 100% !important;
  table-layout: fixed;
}

/* 自定义分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: var(--el-color-primary);
  color: white;
  border-radius: 0.375rem;
  box-shadow: 0 2px 5px rgba(59, 124, 254, 0.3);
}

:deep(.el-pagination.is-background .el-pager li) {
  margin: 0 3px;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled):hover) {
  background-color: #e8f0ff;
  color: var(--el-color-primary);
}

.text-primary {
  color: var(--el-color-primary);
}

.label-tooltip {
  cursor: help;
  border-bottom: 1px dashed #ccc;
}
</style> 