<template>
  <div class="glossary-page">
    <el-card class="glossary-card">
      <template #header>
        <div class="card-header">
          <h1 class="page-title">专业术语表</h1>
        </div>
      </template>
      
      <p class="page-description">
        以下是系统中使用的专业术语及其解释，帮助您更好地理解系统中的专业概念。
      </p>
      
      <glossary-list />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import GlossaryList from '@/components/common/GlossaryList.vue';
</script>

<style scoped>
.glossary-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.glossary-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.page-description {
  color: #606266;
  margin-bottom: 24px;
}
</style> 