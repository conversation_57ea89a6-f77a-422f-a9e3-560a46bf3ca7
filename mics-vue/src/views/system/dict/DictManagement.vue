<script setup lang="ts">
import { ref } from 'vue'
import DictTypeList from './components/DictTypeList.vue'
import DictDataList from './components/DictDataList.vue'
import type { IDictType } from '@/api/dict/types'
import { createLogger } from '@/utils/logger'

// 创建日志记录器
const logger = createLogger('DictManagement')

// 当前选中的字典类型
const currentDictType = ref<IDictType | null>(null)

/**
 * 字典类型选择变更
 */
function handleDictTypeSelected(dictType: IDictType | null) {
  logger.info('字典类型选择变更', dictType ? { cid: dictType.cid, dictCode: dictType.dictCode } : 'null')
  currentDictType.value = dictType
}

/**
 * 刷新数据
 */
function handleRefresh() {
  logger.info('刷新数据')
}
</script>

<template>
  <div class="dict-management">
    <el-row :gutter="20">
      <!-- 字典类型面板 -->
      <el-col :span="10">
        <DictTypeList @select-dict-type="handleDictTypeSelected" @refresh="handleRefresh" />
      </el-col>
      
      <!-- 字典数据面板 -->
      <el-col :span="14">
        <DictDataList :selected-dict-type="currentDictType" @refresh="handleRefresh" />
      </el-col>
    </el-row>
  </div>
</template>

<style scoped>
.dict-management {
  padding: 20px;
}
</style> 