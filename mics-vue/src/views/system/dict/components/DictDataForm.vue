<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import type { IDictDataForm, IDictType } from '@/api/dict/types'
import { DictStatus } from '@/api/dict/types'
import { ElMessage } from 'element-plus'
import { mockDictTypes } from '@/api/dict/mock'

const props = defineProps<{
  visible: boolean
  title: string
  formData?: IDictDataForm
  selectedDictCode?: string
}>()

const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 字典数据表单
const form = reactive<IDictDataForm>({
  dictCode: '',
  uniqueCode: '',
  label: '',
  status: DictStatus.OPEN,
  remark: '',
  sort: 0
})

// 表单校验规则
const rules = {
  dictCode: [
    { required: true, message: '请选择字典类型', trigger: 'change' }
  ],
  uniqueCode: [
    { required: true, message: '请输入唯一代码', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  label: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 状态选项
const statusOptions = [
  { label: '开启', value: DictStatus.OPEN },
  { label: '关闭', value: DictStatus.CLOSE }
]

// 字典类型选项
const dictTypeOptions = ref<{ label: string; value: string }[]>([])

// 获取字典类型选项
const getDictTypeOptions = () => {
  // 实际开发中应该从API获取数据
  // 这里使用Mock数据
  dictTypeOptions.value = mockDictTypes.map(item => ({
    label: `${item.dictName} (${item.dictCode})`,
    value: item.dictCode
  }))
}

// 表单引用
const formRef = ref()

// 监听弹窗显示状态
watch(() => props.visible, (val) => {
  if (val) {
    if (props.formData) {
      // 编辑模式，填充表单数据
      Object.assign(form, props.formData)
    } else if (props.selectedDictCode) {
      // 新增模式，但指定了字典类型代码
      form.dictCode = props.selectedDictCode
    }
  }
})

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      emit('submit', { ...form })
    } else {
      ElMessage.error('请填写必填项')
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  Object.keys(form).forEach(key => {
    if (key !== 'status' && key !== 'dictCode' && key !== 'sort') {
      form[key as keyof IDictDataForm] = ''
    } else if (key === 'status') {
      form[key] = DictStatus.OPEN
    } else if (key === 'dictCode' && props.selectedDictCode) {
      form[key] = props.selectedDictCode
    } else if (key === 'sort') {
      form[key] = 0
    } else {
      form[key] = ''
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}

// 初始化
onMounted(() => {
  getDictTypeOptions()
})
</script>

<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    width="500px"
    @update:model-value="(val) => emit('update:visible', val)"
    @closed="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="dict-data-form"
    >
      <el-form-item label="字典类型" prop="dictCode">
        <el-select
          v-model="form.dictCode"
          placeholder="请选择字典类型"
          :disabled="!!props.formData?.cid || !!props.selectedDictCode"
          class="w-full"
        >
          <el-option
            v-for="item in dictTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="唯一代码" prop="uniqueCode">
        <el-input
          v-model="form.uniqueCode"
          placeholder="请输入唯一代码"
          :disabled="!!props.formData?.cid"
        />
      </el-form-item>
      
      <el-form-item label="标签名称" prop="label">
        <el-input v-model="form.label" placeholder="请输入标签名称" />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择状态" class="w-full">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="3"
        />
      </el-form-item>
      
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          :min="0"
          :max="9999"
          placeholder="请输入排序值"
          class="w-full"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dict-data-form {
  margin: 10px 0;
}
.w-full {
  width: 100%;
}
</style> 