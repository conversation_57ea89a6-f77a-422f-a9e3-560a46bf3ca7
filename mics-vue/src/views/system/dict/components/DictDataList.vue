<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from 'vue'
import { Search, Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { IDictType, IDictData, IDictDataQueryParams, IDictDataForm } from '@/api/dict/types'
import { DictStatus } from '@/api/dict/types'
import { getDictDataService, getDictTypeService } from '@/api/dict'
import { createLogger } from '@/utils/logger'

// 创建日志记录器
const logger = createLogger('DictDataList')

// 获取字典数据服务
const dictDataService = getDictDataService()
// 获取字典类型服务（用于获取字典类型名称）
const dictTypeService = getDictTypeService()

const props = defineProps<{
  selectedDictType?: IDictType | null
}>()

const emit = defineEmits(['refresh'])

// 字典数据表格数据
const dictDataList = ref<IDictData[]>([])
const dictDataTotal = ref(0)
const dictDataLoading = ref(false)
const dictDataParams = reactive<IDictDataQueryParams>({
  pageNo: 1,
  pageSize: 10,
  dictCode: '',
  keyData: '',
  label: '',
  status: null
})

// 状态选项
const statusOptions = [
  { value: DictStatus.OPEN, label: '开启' },
  { value: DictStatus.CLOSE, label: '关闭' }
]

// 字典数据表单
const dictDataFormVisible = ref(false)
const dictDataFormTitle = ref('')
const dictDataForm = reactive({
  cid: undefined as number | undefined,
  dictCode: '',
  keyData: '',
  label: '',
  status: DictStatus.OPEN,
  remark: '',
  sort: 0
})

const dictDataFormRules = {
  dictCode: [
    { required: true, message: '请输入字典类型编码', trigger: 'blur' }
  ],
  keyData: [
    { required: true, message: '请输入字典项编码', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  label: [
    { required: true, message: '请输入字典项标签', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 字典数据搜索框引用
const dictDataSearchFormRef = ref()
// 字典数据表单引用
const dictDataFormRef = ref()

// 计算属性：字典数据查询是否被禁用（需要先选择字典类型）
const dictDataSearchDisabled = computed(() => !props.selectedDictType)

// 计算属性：当前选中的字典类型名称
const currentDictTypeName = computed(() => {
  return props.selectedDictType ? props.selectedDictType.dictName : '请选择字典类型'
})

// 监听选中的字典类型变化
watch(() => props.selectedDictType, (val) => {
  if (val) {
    dictDataParams.dictCode = val.dictCode
    dictDataParams.pageNo = 1
    loadDictDataList()
  } else {
    // 清空字典数据表格
    dictDataList.value = []
    dictDataTotal.value = 0
  }
}, { immediate: true })

/**
 * 加载字典数据列表
 */
async function loadDictDataList() {
  if (!props.selectedDictType) return

  dictDataLoading.value = true
  logger.info('加载字典数据列表', {
    dictCode: props.selectedDictType.dictCode,
    params: dictDataParams
  })

  try {
    // 设置当前选中字典类型的编码
    dictDataParams.dictCode = props.selectedDictType.dictCode

    const result = await dictDataService.getDictDataList(dictDataParams)
    dictDataList.value = result.data
    dictDataTotal.value = result.total
    logger.info('加载字典数据列表成功', {
      dictCode: props.selectedDictType.dictCode,
      count: result.total
    })
  } catch (error) {
    console.error('获取字典数据列表失败', error)
    logger.error('获取字典数据列表失败', {
      dictCode: props.selectedDictType.dictCode,
      error
    })
    ElMessage.error('获取字典数据列表失败')
  } finally {
    dictDataLoading.value = false
  }
}

/**
 * 字典数据搜索
 */
function handleDictDataSearch() {
  logger.info('字典数据搜索', dictDataParams)
  dictDataParams.pageNo = 1
  loadDictDataList()
}

/**
 * 重置字典数据搜索条件
 */
function resetDictDataSearch() {
  logger.info('重置字典数据搜索')
  dictDataSearchFormRef.value.resetFields()
  dictDataParams.keyData = ''
  dictDataParams.label = ''
  dictDataParams.status = null
  dictDataParams.pageNo = 1
  loadDictDataList()
}

/**
 * 打开新增字典数据表单
 */
function openAddDictDataForm() {
  if (!props.selectedDictType) {
    logger.warn('尝试新增字典数据但未选择字典类型')
    ElMessage.warning('请先选择字典类型')
    return
  }

  logger.info('打开新增字典数据表单', { dictCode: props.selectedDictType.dictCode })
  dictDataFormTitle.value = '新增字典数据'
  dictDataForm.cid = undefined
  dictDataForm.dictCode = props.selectedDictType.dictCode
  dictDataForm.keyData = ''
  dictDataForm.label = ''
  dictDataForm.status = DictStatus.OPEN
  dictDataForm.remark = ''

  // 获取当前数据列表中的最大排序号，并加1
  const maxSort = dictDataList.value && dictDataList.value.length > 0
    ? Math.max(...dictDataList.value.map(item => Number(item.sortNo || item.sort || 0)))
    : -1
  dictDataForm.sort = maxSort + 1

  dictDataFormVisible.value = true
}

/**
 * 打开编辑字典数据表单
 */
function openEditDictDataForm(row: IDictData) {
  logger.info('打开编辑字典数据表单', {
    cid: row.cid,
    dictCode: row.dictCode,
    keyData: row.keyData
  })
  // 调试查看后端返回的数据字段
  console.log('字典数据行数据:', row)

  dictDataFormTitle.value = '编辑字典数据'
  dictDataForm.cid = row.cid
  dictDataForm.dictCode = row.dictCode
  dictDataForm.keyData = row.keyData
  dictDataForm.label = row.label
  dictDataForm.status = row.status
  dictDataForm.remark = row.comment || row.remark || ''
  dictDataForm.sort = row.sortNo || row.sort || 0
  dictDataFormVisible.value = true
}

/**
 * 提交字典数据表单
 */
async function submitDictDataForm() {
  dictDataFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      logger.warn('字典数据表单验证失败')
      return
    }

    try {
      if (dictDataForm.cid) {
        // 编辑
        logger.info('提交编辑字典数据表单', {
          cid: dictDataForm.cid,
          dictCode: dictDataForm.dictCode,
          keyData: dictDataForm.keyData,
          sort: dictDataForm.sort
        })
        await dictDataService.updateDictData({
          cid: dictDataForm.cid,
          dictCode: dictDataForm.dictCode,
          keyData: dictDataForm.keyData,
          label: dictDataForm.label,
          status: dictDataForm.status,
          remark: dictDataForm.remark,
          sort: dictDataForm.sort
        })
        ElMessage.success('修改字典数据成功')
        logger.info('修改字典数据成功', { cid: dictDataForm.cid })
      } else {
        // 新增
        logger.info('提交新增字典数据表单', {
          dictCode: dictDataForm.dictCode,
          keyData: dictDataForm.keyData,
          sort: dictDataForm.sort
        })
        await dictDataService.addDictData({
          dictCode: dictDataForm.dictCode,
          keyData: dictDataForm.keyData,
          label: dictDataForm.label,
          status: dictDataForm.status,
          remark: dictDataForm.remark,
          sort: dictDataForm.sort
        })
        ElMessage.success('新增字典数据成功')
        logger.info('新增字典数据成功', {
          dictCode: dictDataForm.dictCode,
          keyData: dictDataForm.keyData
        })
      }
      // 关闭表单
      dictDataFormVisible.value = false
      // 重新加载列表
      loadDictDataList()
      // 触发刷新事件
      emit('refresh')
    } catch (error) {
      console.error('保存字典数据失败', error)
      logger.error('保存字典数据失败', {
        form: dictDataForm,
        error
      })
      ElMessage.error('保存字典数据失败')
    }
  })
}

/**
 * 删除字典数据
 */
function handleDeleteDictData(row: IDictData) {
  logger.info('准备删除字典数据', {
    cid: row.cid,
    dictCode: row.dictCode,
    keyData: row.keyData
  })
  ElMessageBox.confirm(
    `确定要删除字典数据"${row.label}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        logger.info('确认删除字典数据', { cid: row.cid })
        await dictDataService.deleteDictData(row.cid)
        ElMessage.success('删除字典数据成功')
        logger.info('删除字典数据成功', { cid: row.cid })
        // 重新加载列表
        loadDictDataList()
      } catch (error) {
        console.error('删除字典数据失败', error)
        logger.error('删除字典数据失败', { cid: row.cid, error })
        ElMessage.error('删除字典数据失败')
      }
    })
    .catch(() => {
      // 取消删除
      logger.info('取消删除字典数据', { cid: row.cid })
    })
}

/**
 * 字典数据分页变更
 */
function handleDictDataPageChange(pageNo: number) {
  logger.info('字典数据分页变更', { pageNo })
  dictDataParams.pageNo = pageNo
  loadDictDataList()
}

/**
 * 字典数据每页条数变更
 */
function handleDictDataSizeChange(pageSize: number) {
  logger.info('字典数据每页条数变更', { pageSize })
  dictDataParams.pageSize = pageSize
  dictDataParams.pageNo = 1
  loadDictDataList()
}
</script>

<template>
  <div class="dict-data-list">
    <el-card shadow="hover" class="dict-data-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ currentDictTypeName }}</span>
          <el-button
            type="primary"
            :icon="Plus"
            :disabled="!selectedDictType"
            @click="openAddDictDataForm"
          >
            新增
          </el-button>
        </div>
      </template>

      <!-- 字典数据搜索表单 -->
      <el-form
        ref="dictDataSearchFormRef"
        :model="dictDataParams"
        inline
        class="dict-search-form"
        :disabled="dictDataSearchDisabled"
      >
        <el-form-item label="" prop="keyData">
          <el-input
            v-model="dictDataParams.keyData"
            placeholder="请输入数据编码"
            clearable
          />
        </el-form-item>
        <el-form-item label="" prop="label">
          <el-input
            v-model="dictDataParams.label"
            placeholder="请输入数据标签"
            clearable
          />
        </el-form-item>
        <el-form-item label="" prop="status">
          <el-select v-model="dictDataParams.status" placeholder="请选择状态" clearable style="width: 120px;">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="Search"
            :disabled="dictDataSearchDisabled"
            @click="handleDictDataSearch"
          >
            搜索
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 字典数据表格 -->
      <el-table
        v-loading="dictDataLoading"
        :data="dictDataList"
        highlight-current-row
      >
        <el-table-column prop="keyData" label="数据编码" show-overflow-tooltip />
        <el-table-column prop="label" label="数据标签" show-overflow-tooltip />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === DictStatus.OPEN ? 'success' : 'info'">
              {{ scope.row.status === DictStatus.OPEN ? '开启' : '关闭' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sortNo" label="排序" width="80" />
        <el-table-column prop="comment" label="备注" show-overflow-tooltip />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button
              type="primary"
              :icon="Edit"
              circle
              plain
              @click="openEditDictDataForm(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 字典数据分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="dictDataParams.pageNo"
          v-model:page-size="dictDataParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="dictDataTotal"
          @size-change="handleDictDataSizeChange"
          @current-change="handleDictDataPageChange"
        />
      </div>
    </el-card>

    <!-- 字典数据表单对话框 -->
    <el-dialog v-model="dictDataFormVisible" :title="dictDataFormTitle" width="500px">
      <el-form
        ref="dictDataFormRef"
        :model="dictDataForm"
        :rules="dictDataFormRules"
        label-width="100px"
      >
        <el-form-item label="字典类型" prop="dictCode">
          <el-input
            v-model="dictDataForm.dictCode"
            disabled
            placeholder="字典类型编码"
          />
        </el-form-item>
        <el-form-item label="数据编码" prop="keyData">
          <el-input
            v-model="dictDataForm.keyData"
            placeholder="请输入数据编码"
            :disabled="!!dictDataForm.cid"
          />
        </el-form-item>
        <el-form-item label="数据标签" prop="label">
          <el-input v-model="dictDataForm.label" placeholder="请输入数据标签" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="dictDataForm.status">
            <el-radio
              v-for="item in statusOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="dictDataForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="dictDataForm.sort"
            :min="0"
            :max="9999"
            placeholder="请输入排序值"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dictDataFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDictDataForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.dict-data-list {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.dict-search-form {
  margin-bottom: 20px;
}

.dict-data-card {
  height: calc(100vh - 150px);
  display: flex;
  flex-direction: column;
}

.el-table {
  flex: 1;
  overflow: auto;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
