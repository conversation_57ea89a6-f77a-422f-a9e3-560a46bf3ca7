<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Search, Plus, Edit, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { IDictType, IDictTypeQueryParams, IDictTypeForm } from '@/api/dict/types'
import { DictStatus } from '@/api/dict/types'
import { getDictTypeService } from '@/api/dict'
import { createLogger } from '@/utils/logger'

// 创建日志记录器
const logger = createLogger('DictTypeList')

// 获取字典类型服务
const dictTypeService = getDictTypeService()

const emit = defineEmits(['select-dict-type', 'refresh'])

// 字典类型表格数据
const dictTypeList = ref<IDictType[]>([])
const dictTypeTotal = ref(0)
const dictTypeLoading = ref(false)
const dictTypeParams = reactive<IDictTypeQueryParams>({
  pageNo: 1,
  pageSize: 10,
  dictCode: '',
  dictName: '',
  status: null
})

// 状态选项
const statusOptions = [
  { value: DictStatus.OPEN, label: '开启' },
  { value: DictStatus.CLOSE, label: '关闭' }
]

// 字典类型表单
const dictTypeFormVisible = ref(false)
const dictTypeFormTitle = ref('')
const dictTypeForm = reactive({
  cid: undefined as number | undefined,
  dictCode: '',
  dictName: '',
  status: DictStatus.OPEN,
  remark: ''
})

const dictTypeFormRules = {
  dictCode: [
    { required: true, message: '请输入字典编码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  dictName: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 字典类型搜索框引用
const dictTypeSearchFormRef = ref()
// 字典类型表单引用
const dictTypeFormRef = ref()

/**
 * 生命周期钩子
 */
onMounted(() => {
  logger.info('字典类型组件初始化')
  // 加载字典类型数据
  loadDictTypeList()
})

/**
 * 加载字典类型列表
 */
async function loadDictTypeList() {
  dictTypeLoading.value = true
  logger.info('加载字典类型列表', dictTypeParams)
  try {
    const result = await dictTypeService.getDictTypeList(dictTypeParams)
    dictTypeList.value = result.data
    dictTypeTotal.value = result.total
    logger.info('加载字典类型列表成功', { count: result.total })
  } catch (error) {
    console.error('获取字典类型列表失败', error)
    logger.error('获取字典类型列表失败', { error })
    ElMessage.error('获取字典类型列表失败')
  } finally {
    dictTypeLoading.value = false
  }
}

/**
 * 字典类型搜索
 */
function handleDictTypeSearch() {
  logger.info('字典类型搜索', dictTypeParams)
  dictTypeParams.pageNo = 1
  loadDictTypeList()
}

/**
 * 重置字典类型搜索条件
 */
function resetDictTypeSearch() {
  logger.info('重置字典类型搜索')
  dictTypeSearchFormRef.value.resetFields()
  dictTypeParams.dictCode = ''
  dictTypeParams.dictName = ''
  dictTypeParams.status = null
  dictTypeParams.pageNo = 1
  loadDictTypeList()
}

/**
 * 打开新增字典类型表单
 */
function openAddDictTypeForm() {
  logger.info('打开新增字典类型表单')
  dictTypeFormTitle.value = '新增字典类型'
  dictTypeForm.cid = undefined
  dictTypeForm.dictCode = ''
  dictTypeForm.dictName = ''
  dictTypeForm.status = DictStatus.OPEN
  dictTypeForm.remark = ''
  dictTypeFormVisible.value = true
}

/**
 * 打开编辑字典类型表单
 */
function openEditDictTypeForm(row: IDictType) {
  logger.info('打开编辑字典类型表单', { cid: row.cid, dictCode: row.dictCode })
  dictTypeFormTitle.value = '编辑字典类型'
  dictTypeForm.cid = row.cid
  dictTypeForm.dictCode = row.dictCode
  dictTypeForm.dictName = row.dictName
  dictTypeForm.status = row.status
  dictTypeForm.remark = row.remark || ''
  dictTypeFormVisible.value = true
}

/**
 * 提交字典类型表单
 */
async function submitDictTypeForm() {
  dictTypeFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      logger.warn('字典类型表单验证失败')
      return
    }

    try {
      if (dictTypeForm.cid) {
        // 编辑
        logger.info('提交编辑字典类型表单', {
          cid: dictTypeForm.cid,
          dictCode: dictTypeForm.dictCode
        })
        await dictTypeService.updateDictType({
          cid: dictTypeForm.cid,
          dictCode: dictTypeForm.dictCode,
          dictName: dictTypeForm.dictName,
          status: dictTypeForm.status,
          remark: dictTypeForm.remark
        })
        ElMessage.success('修改字典类型成功')
        logger.info('修改字典类型成功', { cid: dictTypeForm.cid })
      } else {
        // 新增
        logger.info('提交新增字典类型表单', {
          dictCode: dictTypeForm.dictCode,
          dictName: dictTypeForm.dictName
        })
        await dictTypeService.addDictType({
          dictCode: dictTypeForm.dictCode,
          dictName: dictTypeForm.dictName,
          status: dictTypeForm.status,
          remark: dictTypeForm.remark
        })
        ElMessage.success('新增字典类型成功')
        logger.info('新增字典类型成功', { dictCode: dictTypeForm.dictCode })
      }
      // 关闭表单
      dictTypeFormVisible.value = false
      // 重新加载列表
      loadDictTypeList()
      // 触发刷新事件
      emit('refresh')
    } catch (error) {
      console.error('保存字典类型失败', error)
      logger.error('保存字典类型失败', {
        form: dictTypeForm,
        error
      })
      ElMessage.error('保存字典类型失败')
    }
  })
}

/**
 * 删除字典类型
 */
function handleDeleteDictType(row: IDictType) {
  logger.info('准备删除字典类型', { cid: row.cid, dictCode: row.dictCode })
  ElMessageBox.confirm(
    `确定要删除字典类型"${row.dictName}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  )
    .then(async () => {
      try {
        logger.info('确认删除字典类型', { cid: row.cid })
        await dictTypeService.deleteDictType(row.cid)
        ElMessage.success('删除字典类型成功')
        logger.info('删除字典类型成功', { cid: row.cid })
        // 重新加载列表
        loadDictTypeList()
        // 触发刷新事件
        emit('refresh')
      } catch (error) {
        console.error('删除字典类型失败', error)
        logger.error('删除字典类型失败', { cid: row.cid, error })
        ElMessage.error('删除字典类型失败')
      }
    })
    .catch(() => {
      // 取消删除
      logger.info('取消删除字典类型', { cid: row.cid })
    })
}

/**
 * 字典类型表格选择变更
 */
function handleDictTypeSelectionChange(row: IDictType | null) {
  logger.info('字典类型选择变更', row ? { cid: row.cid, dictCode: row.dictCode } : 'null')
  // 触发选择事件
  emit('select-dict-type', row)
}

/**
 * 字典类型分页变更
 */
function handleDictTypePageChange(pageNo: number) {
  logger.info('字典类型分页变更', { pageNo })
  dictTypeParams.pageNo = pageNo
  loadDictTypeList()
}

/**
 * 字典类型每页条数变更
 */
function handleDictTypeSizeChange(pageSize: number) {
  logger.info('字典类型每页条数变更', { pageSize })
  dictTypeParams.pageSize = pageSize
  dictTypeParams.pageNo = 1
  loadDictTypeList()
}
</script>

<template>
  <div class="dict-type-list">
    <el-card shadow="hover" class="dict-type-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">字典类型</span>
          <el-button
            type="primary"
            :icon="Plus"
            @click="openAddDictTypeForm"
          >
            新增
          </el-button>
        </div>
      </template>

      <!-- 字典类型搜索表单 -->
      <el-form
        ref="dictTypeSearchFormRef"
        :model="dictTypeParams"
        inline
        class="dict-search-form"
      >
        <div class="form-container">
          <el-form-item label="" prop="dictCode">
            <el-input
              v-model="dictTypeParams.dictCode"
              placeholder="请输入类型编码"
              clearable
            />
          </el-form-item>
          <el-form-item label="" prop="dictName">
            <el-input
              v-model="dictTypeParams.dictName"
              placeholder="请输入类型名称"
              clearable
            />
          </el-form-item>
          <el-form-item label="" prop="status">
            <el-select v-model="dictTypeParams.status" placeholder="请选择状态" clearable style="width: 120px;">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleDictTypeSearch">
              搜索
            </el-button>
          </el-form-item>
        </div>
      </el-form>

      <!-- 字典类型表格 -->
      <el-table
        v-loading="dictTypeLoading"
        :data="dictTypeList"
        highlight-current-row
        @current-change="handleDictTypeSelectionChange"
      >
        <el-table-column prop="dictCode" label="类型编码" show-overflow-tooltip min-width="180" />
        <el-table-column prop="dictName" label="类型名称" show-overflow-tooltip min-width="180" />
        <el-table-column prop="status" label="状态" width="120" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === DictStatus.OPEN ? 'success' : 'info'">
              {{ scope.row.status === DictStatus.OPEN ? '开启' : '关闭' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template #default="scope">
            <el-button
              type="primary"
              :icon="Edit"
              circle
              plain
              @click.stop="openEditDictTypeForm(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 字典类型分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="dictTypeParams.pageNo"
          v-model:page-size="dictTypeParams.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="dictTypeTotal"
          @size-change="handleDictTypeSizeChange"
          @current-change="handleDictTypePageChange"
        />
      </div>
    </el-card>

    <!-- 字典类型表单对话框 -->
    <el-dialog v-model="dictTypeFormVisible" :title="dictTypeFormTitle" width="500px">
      <el-form
        ref="dictTypeFormRef"
        :model="dictTypeForm"
        :rules="dictTypeFormRules"
        label-width="100px"
      >
        <el-form-item label="字典编码" prop="dictCode">
          <el-input
            v-model="dictTypeForm.dictCode"
            placeholder="请输入字典编码"
            :disabled="!!dictTypeForm.cid"
          />
        </el-form-item>
        <el-form-item label="字典名称" prop="dictName">
          <el-input v-model="dictTypeForm.dictName" placeholder="请输入字典名称" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="dictTypeForm.status">
            <el-radio
              v-for="item in statusOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="dictTypeForm.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dictTypeFormVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDictTypeForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.dict-type-list {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.dict-search-form {
  margin-bottom: 20px;
  width: 100%;
}

.form-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

/* 在小屏幕上表单项占更多宽度 */
@media (max-width: 992px) {
  .form-container .el-form-item {
    margin-right: 0;
    flex: 1 1 40%;
  }
}

/* 在大屏幕上表单项保持一行 */
@media (min-width: 993px) {
  .form-container .el-form-item {
    margin-right: 0;
    flex: 0 0 auto;
  }
}

.dict-type-card {
  height: calc(100vh - 150px);
  display: flex;
  flex-direction: column;
}

/* 使用深度选择器修改Element Plus组件内部样式 */
:deep(.el-card__body) {
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.el-table {
  flex: 1;
  overflow: auto;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
