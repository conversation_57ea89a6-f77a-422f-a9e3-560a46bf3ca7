<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { IDictTypeForm } from '@/api/dict/types'
import { DictStatus } from '@/api/dict/types'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  visible: boolean
  title: string
  formData?: IDictTypeForm
}>()

const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 字典类型表单
const form = reactive<IDictTypeForm>({
  dictCode: '',
  dictName: '',
  status: DictStatus.OPEN,
  remark: ''
})

// 表单校验规则
const rules = {
  dictCode: [
    { required: true, message: '请输入字典代码', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  dictName: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 状态选项
const statusOptions = [
  { label: '开启', value: DictStatus.OPEN },
  { label: '关闭', value: DictStatus.CLOSE }
]

// 表单引用
const formRef = ref()

// 监听弹窗显示状态
watch(() => props.visible, (val) => {
  if (val && props.formData) {
    // 编辑模式，填充表单数据
    Object.assign(form, props.formData)
  }
})

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      emit('submit', { ...form })
    } else {
      ElMessage.error('请填写必填项')
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
  Object.keys(form).forEach(key => {
    if (key !== 'status') {
      form[key as keyof IDictTypeForm] = ''
    } else {
      form[key] = DictStatus.OPEN
    }
  })
}

// 关闭弹窗
const handleClose = () => {
  resetForm()
  emit('update:visible', false)
}
</script>

<template>
  <el-dialog
    :title="title"
    :model-value="visible"
    width="500px"
    @update:model-value="(val) => emit('update:visible', val)"
    @closed="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      class="dict-type-form"
    >
      <el-form-item label="字典代码" prop="dictCode">
        <el-input
          v-model="form.dictCode"
          placeholder="请输入字典代码"
          :disabled="!!props.formData?.id"
        />
      </el-form-item>
      
      <el-form-item label="字典名称" prop="dictName">
        <el-input v-model="form.dictName" placeholder="请输入字典名称" />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择状态" class="w-full">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          placeholder="请输入备注"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.dict-type-form {
  margin: 10px 0;
}
.w-full {
  width: 100%;
}
</style> 