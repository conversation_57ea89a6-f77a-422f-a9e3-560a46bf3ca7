/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // 根据 02_technology_stack.md 和 01_preparation_analysis.md 中提到的 Element Plus 和自定义主题需求，
      // 未来可以在这里添加主题颜色变量、字体和断点等。
      // 例如:
      // colors: {
      //   primary: '#409EFF', // Element Plus 主题色示例
      // },
      // fontFamily: {
      //   sans: ['Noto Sans SC', 'sans-serif'],
      // },
    },
  },
  plugins: [],
}

