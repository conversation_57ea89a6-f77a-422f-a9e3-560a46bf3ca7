# MICS支付管理系统部署文档

## 目录

- [系统概述](#系统概述)
- [部署前准备](#部署前准备)
- [前端部署](#前端部署)
  - [环境配置](#环境配置)
  - [构建部署](#构建部署)
  - [Nginx配置](#nginx配置)
- [Docker部署](#docker部署)
  - [前端Docker部署](#前端docker部署)
  - [Docker Compose部署](#docker-compose部署)
- [HTTPS配置](#https配置)
  - [SSL证书获取](#ssl证书获取)
  - [Nginx HTTPS配置](#nginx-https配置)
- [安全加固](#安全加固)
- [后端部署](#后端部署)
- [系统监控](#系统监控)
  - [日志管理](#日志管理)
  - [性能监控](#性能监控)
- [系统维护](#系统维护)
  - [备份策略](#备份策略)
  - [更新流程](#更新流程)
- [系统验证](#系统验证)
- [常见问题](#常见问题)

## 系统概述

MICS支付管理系统是一个基于Vue 3和Vite构建的前端应用，配合后端API提供支付管理相关功能。本文档主要介绍如何将系统从本地开发环境部署到生产环境。

## 部署前准备

### 环境要求

- Node.js: v18.x 或更高版本
- npm: v9.x 或更高版本
- Nginx: 1.18.0 或更高版本（用于生产环境部署）
- Docker: 20.10.x 或更高版本（可选，用于容器化部署）
- Docker Compose: 2.x 或更高版本（可选，用于容器编排）
- 后端API服务已部署并可访问

### 获取源代码

```bash
# 克隆代码仓库
git clone <项目仓库地址>
cd mics-vue
```

## 前端部署

### 环境配置

1. 创建环境配置文件

根据部署环境，从`env-templates`目录复制适当的模板并创建环境文件：

```bash
# 开发环境
cp env-templates/.env.development .env.development

# 生产环境
cp env-templates/.env.production .env.production
```

2. 修改环境配置

编辑`.env.production`文件，设置正确的API地址和其他配置：

```
# 生产环境配置
VITE_API_BASE_URL=https://your-api-domain.com
VITE_USE_MOCK=false
VITE_APP_TITLE=MICS支付管理系统
```

### 构建部署

1. 安装依赖

```bash
npm install
```

2. 构建生产版本

```bash
npm run build
```

构建完成后，将在`dist`目录生成可部署的静态文件。

### Nginx配置

1. 将构建好的静态文件复制到Nginx服务器的网站目录：

```bash
sudo mkdir -p /var/www/mics-vue
sudo cp -r dist/* /var/www/mics-vue/
```

2. 创建Nginx配置文件：

```bash
sudo nano /etc/nginx/sites-available/mics-vue
```

3. 添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名或服务器IP

    root /var/www/mics-vue;
    index index.html;

    # 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理配置
    location /api/ {
        proxy_pass http://your-backend-api:9032/;  # 替换为您的后端API地址
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存设置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
}
```

4. 启用站点并重启Nginx：

```bash
sudo ln -s /etc/nginx/sites-available/mics-vue /etc/nginx/sites-enabled/
sudo nginx -t  # 检查配置是否有误
sudo systemctl restart nginx
```

## Docker部署

### 前端Docker部署

1. 创建Dockerfile

在项目根目录创建`Dockerfile`文件：

```bash
nano Dockerfile
```

添加以下内容：

```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

2. 创建Nginx配置文件

```bash
nano nginx.conf
```

添加以下内容：

```nginx
server {
    listen 80;
    server_name localhost;
    
    root /usr/share/nginx/html;
    index index.html;
    
    # 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理配置
    location /api/ {
        proxy_pass http://backend-api:9032/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

3. 构建并运行Docker镜像

```bash
# 构建镜像
docker build -t mics-vue:latest .

# 运行容器
docker run -d -p 80:80 --name mics-frontend mics-vue:latest
```

### Docker Compose部署

1. 创建docker-compose.yml文件

```bash
nano docker-compose.yml
```

添加以下内容：

```yaml
version: '3'

services:
  frontend:
    build: ./mics-vue
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - mics-network
    restart: always

  backend:
    image: your-backend-image:latest  # 替换为实际的后端镜像
    ports:
      - "9032:9032"
    environment:
      - DB_HOST=db
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=your-password
      - DB_NAME=mics_db
    depends_on:
      - db
    networks:
      - mics-network
    restart: always

  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=your-password
      - MYSQL_DATABASE=mics_db
    volumes:
      - db-data:/var/lib/mysql
    networks:
      - mics-network
    restart: always

networks:
  mics-network:
    driver: bridge

volumes:
  db-data:
```

2. 启动服务

```bash
docker-compose up -d
```

## HTTPS配置

为了保障系统安全，生产环境应当配置HTTPS。

### SSL证书获取

1. 使用Let's Encrypt免费证书

```bash
# 安装certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# 获取并安装证书
sudo certbot --nginx -d your-domain.com
```

2. 使用商业SSL证书

如果您已购买商业SSL证书，将证书文件和私钥上传到服务器：

```bash
# 创建证书目录
sudo mkdir -p /etc/nginx/ssl/mics-vue

# 复制证书文件
sudo cp your-certificate.crt /etc/nginx/ssl/mics-vue/
sudo cp your-private.key /etc/nginx/ssl/mics-vue/
```

### Nginx HTTPS配置

修改Nginx配置文件，启用HTTPS：

```bash
sudo nano /etc/nginx/sites-available/mics-vue
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 将HTTP请求重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/mics-vue/your-certificate.crt;
    ssl_certificate_key /etc/nginx/ssl/mics-vue/your-private.key;
    
    # SSL协议配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH';
    
    # HSTS配置
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # 其他安全头
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    
    root /var/www/mics-vue;
    index index.html;
    
    # 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理配置
    location /api/ {
        proxy_pass http://your-backend-api:9032/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存设置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
}
```

重启Nginx应用新配置：

```bash
sudo nginx -t
sudo systemctl restart nginx
```

## 安全加固

### 系统安全配置

1. 定期更新系统和软件包

```bash
sudo apt update
sudo apt upgrade
```

2. 配置防火墙

```bash
# 安装UFW
sudo apt install ufw

# 设置默认规则
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH连接
sudo ufw allow ssh

# 允许Web流量
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# 启用防火墙
sudo ufw enable
```

3. 加固SSH配置

编辑SSH配置文件：

```bash
sudo nano /etc/ssh/sshd_config
```

修改以下配置：

```
# 禁用密码认证，使用密钥认证
PasswordAuthentication no

# 禁用root登录
PermitRootLogin no

# 限制SSH访问用户
AllowUsers your-username

# 修改默认端口（可选）
Port 2222
```

重启SSH服务：

```bash
sudo systemctl restart sshd
```

### 应用安全配置

1. 配置内容安全策略（CSP）

在Nginx配置中添加CSP头：

```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self' https://your-api-domain.com";
```

2. 配置安全Cookie

确保前端应用设置的Cookie包含安全属性：

```javascript
document.cookie = "sessionId=abc123; Secure; HttpOnly; SameSite=Strict";
```

3. 实施速率限制

在Nginx配置中添加速率限制：

```nginx
# 定义限制区域
limit_req_zone $binary_remote_addr zone=api_limit:10m rate=5r/s;

# 在API位置应用限制
location /api/ {
    limit_req zone=api_limit burst=10 nodelay;
    proxy_pass http://your-backend-api:9032/;
    # ... 其他代理设置 ...
}
```

## 后端部署

后端API服务部署请参考后端项目文档。根据当前配置，后端API服务运行在`*************:9032`。

### 后端服务器配置

1. 确保后端服务允许跨域请求

如果前端和后端部署在不同的域名或端口下，需要在后端配置CORS（跨域资源共享）：

```
Access-Control-Allow-Origin: http://your-frontend-domain.com
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

2. 配置防火墙

确保服务器防火墙允许必要的端口通信：

```bash
# 允许HTTP流量（80端口）
sudo ufw allow 80/tcp

# 允许HTTPS流量（443端口）
sudo ufw allow 443/tcp

# 允许后端API端口（如果需要直接访问）
sudo ufw allow 9032/tcp
```

## 系统监控

### 日志管理

1. Nginx日志配置

编辑Nginx配置文件，确保正确配置日志：

```nginx
server {
    # ... 其他配置 ...
    
    # 访问日志配置
    access_log /var/log/nginx/mics-vue.access.log;
    error_log /var/log/nginx/mics-vue.error.log;
}
```

2. 日志轮转

创建日志轮转配置：

```bash
sudo nano /etc/logrotate.d/mics-vue
```

添加以下内容：

```
/var/log/nginx/mics-vue.*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data adm
    sharedscripts
    postrotate
        [ -s /run/nginx.pid ] && kill -USR1 `cat /run/nginx.pid`
    endscript
}
```

3. 使用ELK堆栈进行日志分析

安装Filebeat收集日志：

```bash
# 添加Elastic仓库
wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add -
sudo apt-get install apt-transport-https
echo "deb https://artifacts.elastic.co/packages/7.x/apt stable main" | sudo tee /etc/apt/sources.list.d/elastic-7.x.list
sudo apt-get update

# 安装Filebeat
sudo apt-get install filebeat

# 配置Filebeat
sudo nano /etc/filebeat/filebeat.yml
```

配置Filebeat收集Nginx日志：

```yaml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/nginx/mics-vue.access.log
  fields:
    type: nginx-access
  fields_under_root: true

- type: log
  enabled: true
  paths:
    - /var/log/nginx/mics-vue.error.log
  fields:
    type: nginx-error
  fields_under_root: true

output.elasticsearch:
  hosts: ["your-elasticsearch-host:9200"]
```

启动Filebeat：

```bash
sudo systemctl enable filebeat
sudo systemctl start filebeat
```

### 性能监控

1. 使用Prometheus和Grafana监控系统性能

安装Node Exporter收集系统指标：

```bash
# 下载Node Exporter
wget https://github.com/prometheus/node_exporter/releases/download/v1.3.1/node_exporter-1.3.1.linux-amd64.tar.gz
tar xvfz node_exporter-1.3.1.linux-amd64.tar.gz
cd node_exporter-1.3.1.linux-amd64

# 安装Node Exporter
sudo cp node_exporter /usr/local/bin/
sudo useradd -rs /bin/false node_exporter

# 创建systemd服务
sudo nano /etc/systemd/system/node_exporter.service
```

添加以下内容：

```ini
[Unit]
Description=Node Exporter
After=network.target

[Service]
User=node_exporter
Group=node_exporter
Type=simple
ExecStart=/usr/local/bin/node_exporter

[Install]
WantedBy=multi-user.target
```

启动Node Exporter：

```bash
sudo systemctl daemon-reload
sudo systemctl enable node_exporter
sudo systemctl start node_exporter
```

2. 配置Nginx状态监控

编辑Nginx配置：

```nginx
server {
    # ... 其他配置 ...
    
    # Nginx状态监控
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;  # 只允许本地访问
        deny all;
    }
}
```

## 系统维护

### 备份策略

1. 数据库备份

创建数据库备份脚本：

```bash
sudo nano /usr/local/bin/backup-mics-db.sh
```

添加以下内容：

```bash
#!/bin/bash
DATE=$(date +%Y%m%d)
BACKUP_DIR="/var/backups/mics"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u root -p'your-password' mics_db > $BACKUP_DIR/mics_db_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/mics_db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "mics_db_*.sql.gz" -type f -mtime +7 -delete
```

设置执行权限并添加到crontab：

```bash
sudo chmod +x /usr/local/bin/backup-mics-db.sh
sudo crontab -e
```

添加以下内容：

```
0 1 * * * /usr/local/bin/backup-mics-db.sh > /var/log/mics-db-backup.log 2>&1
```

2. 前端资源备份

创建前端资源备份脚本：

```bash
sudo nano /usr/local/bin/backup-mics-frontend.sh
```

添加以下内容：

```bash
#!/bin/bash
DATE=$(date +%Y%m%d)
BACKUP_DIR="/var/backups/mics"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份前端资源
tar -czf $BACKUP_DIR/mics_frontend_$DATE.tar.gz -C /var/www mics-vue

# 删除7天前的备份
find $BACKUP_DIR -name "mics_frontend_*.tar.gz" -type f -mtime +7 -delete
```

设置执行权限并添加到crontab：

```bash
sudo chmod +x /usr/local/bin/backup-mics-frontend.sh
sudo crontab -e
```

添加以下内容：

```
0 2 * * * /usr/local/bin/backup-mics-frontend.sh > /var/log/mics-frontend-backup.log 2>&1
```

### 更新流程

1. 前端更新流程

```bash
# 进入项目目录
cd /path/to/mics-vue

# 拉取最新代码
git pull

# 安装依赖
npm install

# 构建生产版本
npm run build

# 备份当前版本
sudo tar -czf /var/backups/mics/mics_frontend_$(date +%Y%m%d_%H%M%S).tar.gz -C /var/www mics-vue

# 部署新版本
sudo cp -r dist/* /var/www/mics-vue/
```

2. Docker环境更新流程

```bash
# 进入项目目录
cd /path/to/mics-vue

# 拉取最新代码
git pull

# 重新构建并启动容器
docker-compose build
docker-compose up -d
```

3. 回滚流程

如需回滚到之前版本：

```bash
# 恢复前端资源
sudo tar -xzf /var/backups/mics/mics_frontend_YYYYMMDD.tar.gz -C /var/www/
```

## 系统验证

1. 访问部署后的系统地址：`http://your-domain.com`
2. 验证以下功能是否正常：
   - 登录认证
   - 菜单导航
   - 数据加载和显示
   - 表单提交和操作
   - API请求响应时间

### 性能验证

使用以下工具验证系统性能：

- Lighthouse: 检查页面加载性能、可访问性和SEO
- Chrome DevTools: 分析网络请求和资源加载
- WebPageTest: 从不同地理位置测试访问性能

## 常见问题

### 1. 页面显示空白或资源加载失败

**可能原因**：
- Nginx配置中的路径不正确
- 构建时的BASE_URL配置不正确
- 静态资源路径问题

**解决方案**：
- 检查Nginx配置中的root路径是否正确
- 确认`.env.production`中的`VITE_API_BASE_URL`配置是否正确
- 检查浏览器控制台是否有404错误

### 2. API请求失败

**可能原因**：
- 后端API服务未启动或不可访问
- Nginx代理配置不正确
- CORS配置问题
- 网络连接问题

**解决方案**：
- 确认后端API服务是否正常运行
- 检查Nginx中的proxy_pass配置
- 确保后端API允许前端域名的跨域请求
- 使用curl或Postman测试API端点

### 3. 路由访问404错误

**可能原因**：
- Nginx未正确配置处理前端路由
- Vue Router配置问题

**解决方案**：
- 确保Nginx配置中包含`try_files $uri $uri/ /index.html;`
- 检查Vue Router的配置是否正确

### 4. 系统性能问题

**可能原因**：
- 静态资源未正确缓存
- 图片和其他资源未优化
- 代码分割不合理

**解决方案**：
- 检查Nginx缓存配置
- 优化图片和资源大小
- 使用工具如Lighthouse分析并优化性能
- 检查Vite构建配置，确保正确的代码分割 