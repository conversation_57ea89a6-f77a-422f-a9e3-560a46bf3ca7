<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑应用 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 添加 Select2 CSS -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
         .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden; /* 确保子元素圆角 */
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        .btn-secondary:hover {
            background-color: #5446b7; /* Slightly darker secondary */
        }

        /* Select2 样式调整 */
        .select2-container .select2-selection--single {
            height: calc(2.25rem + 2px) !important;
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5rem !important;
            padding-left: 0 !important;
             padding-right: 2rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(2.25rem + 2px) !important;
            right: 0.5rem !important;
        }
        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: white !important;
        }
         .select2-dropdown {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
        }
        .select2-container--default .select2-search--dropdown .select2-search__field {
            border-radius: 0.375rem !important;
             border: 1px solid #e0e6ed !important;
             padding: 0.5rem 1rem !important;
        }
         .select2-container .select2-selection--multiple {
            min-height: calc(2.25rem + 2px) !important;
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.25rem 0.5rem !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: var(--primary-light) !important;
            border: 1px solid var(--primary-color) !important;
            color: var(--primary-dark) !important;
            border-radius: 0.375rem !important;
            padding: 0.2rem 0.5rem !important;
            margin-top: 0.2rem !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: var(--primary-dark) !important;
            margin-right: 0.3rem !important;
        }
         .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
             color: var(--danger-color) !important;
        }

        /* Required CSS for Tabbed Interface */
        .tab-nav {
            border-bottom: 1px solid var(--border-color); /* Assumes --border-color is defined */
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch; /* For smooth scrolling on iOS */
        }

        .tab-link {
            padding: 1rem 1.5rem;
            color: #64748b; /* Default text color */
            font-weight: 500;
            position: relative;
            white-space: nowrap;
            text-decoration: none;
            transition: color 0.3s; /* Smooth color transition */
        }

        .tab-link:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color); /* Assumes --primary-color is defined */
            transform: scaleX(0);
            transition: transform 0.3s;
        }

        .tab-link.active {
            color: var(--primary-color); /* Active tab text color */
        }

        .tab-link.active:after {
            transform: scaleX(1); /* Show underline for active tab */
        }

        /* Ensure tab content visibility is controlled correctly (Tailwind's 'hidden' class preferred) */
        .tab-content {
            /* Add any base styling for tab content panels here if needed */
        }
        /* Tailwind's 'hidden' class (display: none !important;) controls visibility */
        /* Tailwind's 'active' class can be used for styling active content if desired, but not visibility */

    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
     <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 (当前页面父级，需要高亮) -->
                <li class="nav-item active">
                    <a href="application.html" class="flex items-center px-4 py-3 text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="application.html" class="hover:text-primary">应用管理</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">编辑应用</span> <!-- 或 "创建应用" -->
        </div>

        <h2 class="text-2xl font-bold text-gray-800 mb-6">编辑应用</h2> <!-- 或 "创建应用" -->

        <div class="card">
            <form action="#" method="POST">
                 <!-- Tab Navigation -->
                <div class="tab-nav">
                    <a href="#basic-info" class="tab-link active" data-tab="tab-content-basic">基本信息</a>
                    <a href="#linked-info" class="tab-link" data-tab="tab-content-linked">关联信息</a>
                    <a href="#config-info" class="tab-link" data-tab="tab-content-config">配置信息</a>
                </div>

                <!-- Tab Content Wrapper (Apply padding here) -->
                <div class="p-8">
                    <!-- Tab Content Panel 1: 基本信息 -->
                    <div id="tab-content-basic" class="tab-content active">
                         <!-- 基本信息内容 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 应用名称 -->
                            <div>
                                <label for="app-name" class="block text-sm font-medium text-gray-700 mb-1">应用名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="app-name" name="app-name" value="电商平台App" required class="w-full" placeholder="请输入应用的名称">
                            </div>

                            <!-- AppID -->
                            <div>
                                <label for="app-id" class="block text-sm font-medium text-gray-700 mb-1">AppID <span class="text-red-500">*</span></label>
                                <input type="text" id="app-id" name="app-id" value="ecp_a1b2c3d4" required class="w-full bg-gray-100 font-mono" placeholder="系统自动生成" readonly>
                                 <p class="text-xs text-gray-500 mt-1">应用唯一标识，创建后不可修改。</p>
                            </div>

                            <!-- 渠道名称 -->
                            <div>
                                <label for="channel-name" class="block text-sm font-medium text-gray-700 mb-1">渠道名称 <span class="text-red-500">*</span></label>
                                <select id="channel-name" name="channel-name" required class="w-full">
                                    <option value="wechat" selected>微信</option>
                                    <option value="alipay">支付宝</option>
                                    <option value="unionpay">云闪付</option>
                                </select>
                            </div>

                            <!-- 应用类型 -->
                            <div>
                                <label for="app-type" class="block text-sm font-medium text-gray-700 mb-1">应用类型 <span class="text-red-500">*</span></label>
                                <select id="app-type" name="app-type" required class="w-full">
                                    <option value="app" selected>APP</option>
                                    <option value="miniapp">小程序</option>
                                    <option value="h5">H5</option>
                                </select>
                            </div>

                            <!-- 状态 -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态 <span class="text-red-500">*</span></label>
                                <select id="status" name="status" required class="w-full">
                                    <option value="active" selected>启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                            </div>
                            
                            <!-- 描述 -->
                            <div class="md:col-span-2">
                                <label for="app-description" class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                                <textarea id="app-description" name="app-description" rows="3" class="w-full" placeholder="应用的补充说明信息">这是主要的电商平台移动客户端应用。</textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content Panel 2: 关联信息 -->
                    <div id="tab-content-linked" class="tab-content hidden">
                        <!-- 关联信息内容 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 关联账号 -->
                            <div>
                                <label for="linked-account" class="block text-sm font-medium text-gray-700 mb-1">关联账号 <span class="text-red-500">*</span></label>
                                <select id="linked-account" name="linked-account" required class="w-full select2-multiple">
                                    <option value="CHNACC001" selected>官方微信支付账号</option>
                                    <option value="CHNACC002">官方支付宝账号</option>
                                    <option value="CHNACC003">测试云闪付账号</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">选择此应用使用的渠道账号。</p>
                            </div>

                            <!-- 关联商户号 -->
                            <div>
                                <label for="linked-merchants" class="block text-sm font-medium text-gray-700 mb-1">关联商户号</label>
                                <select id="linked-merchants" name="linked-merchants[]" class="w-full select2-multiple" multiple="multiple">
                                    <option value="MCH001" selected>示例商户A (**********)</option>
                                    <option value="MCH002">示例商户B (9876543210)</option>
                                    <option value="MCH003">测试商户C (1122334455)</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">选择此应用可以使用的商户号。</p>
                            </div>
                        </div>
                    </div>
                    
                     <!-- Tab Content Panel 3: 配置信息 -->
                    <div id="tab-content-config" class="tab-content hidden">
                       <!-- 配置信息内容 -->
                       <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 支付回调地址 -->
                            <div class="md:col-span-2">
                                <label for="payment-notify-url" class="block text-sm font-medium text-gray-700 mb-1">支付结果回调地址</label>
                                <input type="url" id="payment-notify-url" name="payment-notify-url" value="https://api.example-app.com/payment/notify" class="w-full" placeholder="接收异步支付结果通知的URL">
                            </div>

                            <!-- 退款回调地址 -->
                             <div class="md:col-span-2">
                                <label for="refund-notify-url" class="block text-sm font-medium text-gray-700 mb-1">退款结果回调地址</label>
                                <input type="url" id="refund-notify-url" name="refund-notify-url" value="https://api.example-app.com/refund/notify" class="w-full" placeholder="接收异步退款结果通知的URL">
                            </div>
                            
                            <!-- AppSecret -->
                            <div class="md:col-span-2">
                                <label for="app-secret" class="block text-sm font-medium text-gray-700 mb-1">AppSecret (应用密钥)</label>
                                <div class="flex items-center space-x-2">
                                    <input type="password" id="app-secret" name="app-secret" value="**********" class="w-full font-mono">
                                    <button type="button" class="btn btn-secondary btn-sm py-1 px-3 whitespace-nowrap">
                                        <i class="fas fa-sync-alt mr-1"></i>重新生成
                                    </button>
                                    <button type="button" id="toggle-secret" class="btn btn-light btn-sm py-1 px-3">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                 <p class="text-xs text-gray-500 mt-1">用于API接口签名认证，请妥善保管。</p>
                            </div>
                       </div>
                    </div>

                    <!-- 操作按钮 (放在Tab Content Wrapper内部，但在所有tab-content之后) -->
                    <div class="mt-8 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                        <a href="application.html" class="btn btn-light">取消</a>
                        <button type="submit" class="btn btn-primary">保存</button> <!-- 或 "创建" -->
                    </div>
                </div>
            </form>
        </div>
    </main>

    <!-- 引入 jQuery 和 Select2 JS -->
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // 初始化多选 Select2
            $('.select2-multiple').select2({
                 placeholder: "选择关联的商户号",
                 allowClear: true,
                 width: '100%', 
                 closeOnSelect: false 
            });
            
            // 切换 AppSecret 可见性
            $('#toggle-secret').on('click', function() {
                const secretInput = $('#app-secret');
                const icon = $(this).find('i');
                if (secretInput.attr('type') === 'password') {
                    secretInput.attr('type', 'text');
                    icon.removeClass('fa-eye').addClass('fa-eye-slash');
                } else {
                    secretInput.attr('type', 'password');
                    icon.removeClass('fa-eye-slash').addClass('fa-eye');
                }
            });
        });

        // --- 以下是模板的通用JS --- 
        
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
             // 确保元素存在
            if(!submenu || !icon) return; 
            
            let isActive = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.href === window.location.href) {
                    isActive = true;
                    link.classList.add('font-semibold', 'text-primary'); 
                }
            });

            // 如果子菜单包含活动项 或 父菜单是active，则展开
             if (isActive || item.closest('.nav-item').classList.contains('active')) {
                 if(!item.closest('.nav-item').querySelector('a[href]') && isActive) {
                     submenu.classList.add('active');
                     icon.style.transform = 'rotate(180deg)';
                 } else if (item.closest('.nav-item').classList.contains('active')) {
                      submenu.classList.add('active');
                      icon.style.transform = 'rotate(180deg)';
                 }
            }
            
             // 添加点击事件
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 动态设置当前导航项的激活状态
        const currentPath = window.location.pathname.split('/').pop();
        // 移除所有激活状态以重新计算
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
         document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
         document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');

        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a');
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            let isCurrentParent = false;
            let isCurrentPage = false;

            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                 isCurrentPage = true;
            } else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary');
                    }
                });
            }
            
            // 检查是否当前编辑/详情页对应的列表页父菜单
            const parentLinks = ['application.html']; // 定义父菜单链接
             if (currentPath === 'application_edit.html' || currentPath === 'application_detail.html') {
                 if (link && parentLinks.includes(link.getAttribute('href'))) {
                     isCurrentParent = true; // 将父菜单标记为当前
                 }
            }
            

            if (isCurrentPage || isCurrentParent) {
                 navItem.classList.add('active');
                const submenu = navItem.querySelector('.submenu');
                const icon = navItem.querySelector('.submenu-icon');
                if (submenu && isCurrentParent) { // 只有父菜单才展开子菜单
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            }
        });

        // JavaScript for Standard Tab Switching
        function initializeStandardTabs() {
            const tabLinks = document.querySelectorAll('.tab-nav .tab-link'); // Target links within .tab-nav
            const tabContentWrapper = document.querySelector('.tab-nav + div'); // Assumes content wrapper immediately follows .tab-nav

            if (!tabLinks.length || !tabContentWrapper) return; // Exit if essential elements are missing

            const allTabContentDivs = tabContentWrapper.querySelectorAll('.tab-content'); // Find content within the wrapper

            if (!allTabContentDivs.length) return; // Exit if no content panels found

            tabLinks.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 1. Deactivate all tab links
                    tabLinks.forEach(t => t.classList.remove('active'));

                    // 2. Hide all tab content panels using Tailwind's 'hidden' class
                    allTabContentDivs.forEach(content => content.classList.add('hidden'));
                     // Remove 'active' class from content if used for styling
                    allTabContentDivs.forEach(content => content.classList.remove('active'));

                    // 3. Activate the clicked tab link
                    this.classList.add('active');

                    // 4. Show the corresponding content panel
                    const contentId = this.getAttribute('data-tab');
                    const contentToShow = document.getElementById(contentId);
                    if (contentToShow) {
                        contentToShow.classList.remove('hidden');
                        contentToShow.classList.add('active'); // Add 'active' back for potential styling
                    }
                });
            });

            // Ensure initial state: Show content corresponding to the initially active tab link
            const initialActiveTabLink = document.querySelector('.tab-nav .tab-link.active');
            let initialContentId = null;
            if (initialActiveTabLink) {
                 initialContentId = initialActiveTabLink.getAttribute('data-tab');
            } else if (tabLinks.length > 0) {
                // Fallback: If no link is active, make the first one active
                tabLinks[0].classList.add('active');
                initialContentId = tabLinks[0].getAttribute('data-tab');
            }

            allTabContentDivs.forEach(content => {
                if (content.id === initialContentId) {
                    content.classList.remove('hidden');
                    content.classList.add('active'); // Ensure initial content is active
                } else {
                    content.classList.add('hidden');
                    content.classList.remove('active'); // Ensure others are not active
                }
            });
        }

        // Initialize the tabs (call this after the DOM is ready)
        document.addEventListener('DOMContentLoaded', initializeStandardTabs);

    </script>
</body>
</html> 
