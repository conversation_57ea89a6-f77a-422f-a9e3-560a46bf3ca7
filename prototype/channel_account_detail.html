<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道账号详情 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary: #3b82f6;
            --primary-hover: #2563eb;
            --primary-light: #eff6ff;
            --primary-color: #3b82f6;
            --secondary-color: #2563eb;
            --secondary: #6b7280;
            --light: #f3f4f6;
            --dark: #1f2937;
            --danger: #ef4444;
            --success: #10b981;
            --warning: #f59e0b;
            --info: #3b82f6;
        }
        
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f9fafb;
            color: #111827;
            min-height: 100vh;
        }
        
        /* 自定义CSS类 */
        .bg-primary-light {
            background-color: var(--primary-light);
        }
        
        /* 常用工具类 */
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-hover);
        }
        
        .btn-light {
            background-color: var(--light);
            color: var(--dark);
        }
        
        .btn-light:hover {
            background-color: #e5e7eb;
        }
        
        .tag {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .tag-green {
            background-color: #d1fae5;
            color: #059669;
        }
        
        .tag-yellow {
            background-color: #fef3c7;
            color: #d97706;
        }
        
        .tag-red {
            background-color: #fee2e2;
            color: #dc2626;
        }
        
        .tag-gray {
            background-color: #f3f4f6;
            color: #6b7280;
        }
        
        .tag-blue {
            background-color: #dbeafe;
            color: #2563eb;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
        .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }


        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px; 
        }

        /* 详情页面样式 */
        .detail-section {
            margin-bottom: 2rem;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
        }
        
        .detail-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .detail-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .detail-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .detail-label {
            width: 100px;
            flex-shrink: 0;
            font-weight: 500;
            color: #4b5563;
        }
        
        .detail-value {
            color: #111827;
            flex-grow: 1;
        }
        
        .password-mask {
            font-family: monospace;
            letter-spacing: 1px;
        }
        
        .collapsible-section {
            margin-bottom: 2rem;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
        }
        
        .collapsible-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            cursor: pointer;
        }
        
        .collapsible-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
        }
        
        .collapsible-content {
            display: none;
            padding-top: 1rem;
        }
        
        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th {
            background-color: #f9fafb;
            font-weight: 500;
            text-align: left;
            padding: 0.75rem 1rem;
            color: #4b5563;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .data-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e5e7eb;
            color: #111827;
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        .data-table tr:hover {
            background-color: #f9fafb;
        }
        
        /* 页面布局修复 */
        .content-container {
            padding: 1.5rem;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.875rem;
        }
        
        .breadcrumb a {
            color: #6b7280;
            text-decoration: none;
        }
        
        .breadcrumb a:hover {
            color: var(--primary);
        }
        
        .breadcrumb .breadcrumb-separator {
            margin: 0 0.5rem;
            color: #9ca3af;
        }
        
        .title-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .title-section h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.75rem;
        }
        
        @media (max-width: 768px) {
            .title-section {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .detail-row {
                flex-direction: column;
            }
            
            .detail-label {
                width: 100%;
                margin-bottom: 0.25rem;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 (当前页面父级，需要高亮和展开) -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300 transform rotate-180"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4 active" style="max-height: 500px;">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-primary font-semibold">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>

                
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="channel_account.html" class="hover:text-primary">渠道账号管理</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">渠道账号详情</span>
        </div>

        <!-- 标题和操作区域 -->
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">渠道账号详情</h2>
            <div class="space-x-3">
                <a href="channel_account_edit.html?id=CHNACC001" class="btn btn-primary">
                    <i class="fas fa-edit mr-1"></i> 编辑
                </a>
                <a href="channel_account.html" class="btn btn-light">
                    <i class="fas fa-arrow-left mr-1"></i> 返回
                </a>
            </div>
        </div>

        <!-- 渠道账号详情卡片 -->
        <div class="card p-8 mb-6">
            <!-- 基本信息 -->
            <div class="detail-section">
                <div class="detail-header">
                    <h3 class="detail-title">基本信息</h3>
                    <span class="tag tag-green">已启用</span>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <div class="detail-row">
                            <span class="detail-label">账号ID:</span>
                            <span class="detail-value">CHNACC001</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">账号名称:</span>
                            <span class="detail-value">官方微信支付账号</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">渠道类型:</span>
                            <span class="detail-value">微信支付</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">平台类型:</span>
                            <span class="detail-value">服务商</span>
                        </div>
                    </div>
                    <div>
                        <div class="detail-row">
                            <span class="detail-label">申请主体:</span>
                            <span class="detail-value">北京科技有限公司</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">是否超管:</span>
                            <span class="detail-value">是</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">渠道地址:</span>
                            <span class="detail-value">https://api.mch.weixin.qq.com</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">创建时间:</span>
                            <span class="detail-value">2023-08-15 14:30:22</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 凭证信息 -->
            <div class="collapsible-section">
                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                    <h3 class="collapsible-title">凭证信息</h3>
                    <i class="fas fa-chevron-down text-gray-500 transition-transform duration-300"></i>
                </div>
                <div class="collapsible-content" style="display: none;">
                    <!-- 微信支付凭证信息 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="detail-row">
                                <span class="detail-label">AppID:</span>
                                <span class="detail-value">wx1234567890abcdef</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">AppSecret:</span>
                                <div class="flex items-center">
                                    <span class="detail-value password-mask" data-value="secretAppKey_2023">••••••••••</span>
                                    <button class="ml-2 text-gray-500 hover:text-primary" onclick="togglePasswordVisibility(this)">
                                        <i class="far fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">APIv3密钥:</span>
                                <div class="flex items-center">
                                    <span class="detail-value password-mask" data-value="api3Key_wxpay_secure_20230815">••••••••••••••••••••••••••••••</span>
                                    <button class="ml-2 text-gray-500 hover:text-primary" onclick="togglePasswordVisibility(this)">
                                        <i class="far fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="detail-row">
                                <span class="detail-label">API证书路径:</span>
                                <span class="detail-value">/path/to/apiclient_cert.pem</span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">API密钥路径:</span>
                                <span class="detail-value">/path/to/apiclient_key.pem</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 描述信息 -->
            <div class="detail-section">
                <div class="detail-header">
                    <h3 class="detail-title">描述信息</h3>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <p class="text-gray-700">官方接入的微信支付账号，用于收银台主要支付场景。该账号具有超级管理员权限，可以管理所有商户下的交易。</p>
                </div>
            </div>
        </div>
        
        <!-- 渠道员工列表 -->
        <div class="card p-8">
            <div class="detail-header">
                <h3 class="detail-title">关联渠道员工</h3>
                <a href="channel_employee.html?channel_account=CHNACC001" class="text-primary hover:underline">
                    <i class="fas fa-external-link-alt mr-1"></i> 查看全部
                </a>
            </div>
            
            <!-- 员工列表 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">在职</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13900139000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">在职</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王五</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13700137000</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-gray">离职</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            // 确保元素存在
            if(!submenu || !icon) return; 
            
            let isParentActive = item.closest('.nav-item').classList.contains('active');
            let hasActiveChild = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.classList.contains('font-semibold')) { // 检查是否有子项是高亮的
                     hasActiveChild = true;
                }
            });
            
            // 如果父菜单是active 或 包含active子项，则展开
            if (isParentActive || hasActiveChild) {
                 submenu.classList.add('active');
                 icon.style.transform = 'rotate(180deg)';
            } else {
                 submenu.classList.remove('active');
                 icon.style.transform = 'rotate(0deg)';
            }
            
            // 添加点击事件
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 切换折叠区域显示
        function toggleCollapsible(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('i');
            
            if (content.style.display === 'block') {
                content.style.display = 'none';
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
            }
        }
        
        // 切换密码可见性
        function togglePasswordVisibility(button) {
            const valueElement = button.previousElementSibling;
            const icon = button.querySelector('i');
            
            // 获取密码真实值
            const realValue = valueElement.dataset.value;
            
            if (valueElement.classList.contains('password-mask')) {
                valueElement.classList.remove('password-mask');
                valueElement.textContent = realValue;
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                valueElement.classList.add('password-mask');
                valueElement.textContent = '••••••••••';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html> 
