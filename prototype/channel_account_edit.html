<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑渠道账号 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
     <!-- Select2 CSS -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
        .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }


        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px; 
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden; /* Ensure card contains child elements properly */
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
            color: #64748b; 
            font-weight: 500; 
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

         .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        
        /* Select2 适配 */
         .select2-container--default .select2-selection--single {
            height: calc(2.25rem + 2px) !important; /* 适应 Tailwind 表单高度 */
            border: 1px solid #e0e6ed !important;
            border-radius: 0.5rem !important;
            padding: 0.375rem 0.75rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5rem !important;
            padding-left: 0 !important;
            color: #495057;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(2.25rem + 2px) !important;
            top: 0.1rem !important;
        }
        .select2-container--default.select2-container--open .select2-selection--single {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
        }
         .select2-dropdown {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        /* Required CSS for Tabbed Interface */
        .tab-nav {
            border-bottom: 1px solid var(--border-color); /* Assumes --border-color is defined */
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch; /* For smooth scrolling on iOS */
            padding-left: 1.5rem; /* Align with card padding */
        }

        .tab-link {
            padding: 1rem 1.5rem;
            color: #64748b; /* Default text color */
            font-weight: 500;
            position: relative;
            white-space: nowrap;
            text-decoration: none;
            transition: color 0.3s; /* Smooth color transition */
        }

        .tab-link:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color); /* Assumes --primary-color is defined */
            transform: scaleX(0);
            transition: transform 0.3s;
        }

        .tab-link.active {
            color: var(--primary-color); /* Active tab text color */
        }

        .tab-link.active:after {
            transform: scaleX(1); /* Show underline for active tab */
        }

        /* Ensure tab content visibility is controlled correctly (Tailwind's 'hidden' class preferred) */
        .tab-content {
            /* Add any base styling for tab content panels here if needed */
        }
        /* Tailwind's 'hidden' class (display: none !important;) controls visibility */
        /* Tailwind's 'active' class can be used for styling active content if desired, but not visibility */

    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
         <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">个人信息</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">系统设置</a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
             <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                  
                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 (当前页面父级，需要高亮和展开) -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4 active">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-primary font-semibold">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
         <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="channel_account.html" class="hover:text-primary">渠道账号管理</a>
            <i class="fas fa-angle-right mx-2"></i>
             <a href="channel_account.html" class="hover:text-primary">渠道账号</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800" id="breadcrumb-action">编辑渠道账号</span>
        </div>

        <h2 class="text-2xl font-bold text-gray-800 mb-6" id="main-title">编辑渠道账号</h2>

        <!-- 编辑表单 -->
        <div class="card">
            <!-- Tab Navigation -->
            <div class="tab-nav">
                <a href="#tab-basic-info" class="tab-link active" data-tab="content-basic-info">基本信息</a>
                <a href="#tab-credentials" class="tab-link" data-tab="content-credentials">凭证信息</a>
                <a href="#tab-employees" class="tab-link" data-tab="content-employees">关联员工</a>
            </div>

            <!-- Tab Content Wrapper -->
            <div class="p-8">
                 <form id="channel-account-form" class="space-y-6">
                    <!-- 隐藏域，用于存储ID -->
                    <input type="hidden" id="channel_account_id" name="id" value="CHNACC001">

                    <!-- Tab Content Panel 1: 基本信息 -->
                    <div id="content-basic-info" class="tab-content active space-y-6">
                        <div>
                            <label for="account_name" class="block text-sm font-medium text-gray-700 mb-1">账号名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="account_name" name="account_name" value="官方微信支付账号" required class="w-full">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="channel_type" class="block text-sm font-medium text-gray-700 mb-1">渠道类型 <span class="text-red-500">*</span></label>
                                <select id="channel_type" name="channel_type" required class="w-full select2-basic">
                                    <option value="wechat" selected>微信支付</option>
                                    <option value="alipay">支付宝</option>
                                    <option value="unionpay">云闪付</option>
                                    <!-- 更多渠道类型 -->
                                </select>
                            </div>

                            <div>
                                <label for="platform_type" class="block text-sm font-medium text-gray-700 mb-1">平台类型 <span class="text-red-500">*</span></label>
                                <select id="platform_type" name="platform_type" required class="w-full select2-basic">
                                    <option value="service_provider" selected>服务商</option>
                                    <option value="direct_merchant">直连商户</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="entity_name" class="block text-sm font-medium text-gray-700 mb-1">申请主体 <span class="text-red-500">*</span></label>
                            <input type="text" id="entity_name" name="entity_name" value="北京科技有限公司" required class="w-full">
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">是否超管 <span class="text-red-500">*</span></label>
                                <div class="flex items-center space-x-6">
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="is_super_admin" value="yes" class="form-radio h-4 w-4 text-primary focus:ring-primary" checked>
                                        <span class="ml-2 text-gray-700">是</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="is_super_admin" value="no" class="form-radio h-4 w-4 text-primary focus:ring-primary">
                                        <span class="ml-2 text-gray-700">否</span>
                                    </label>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">状态 <span class="text-red-500">*</span></label>
                                <div class="flex items-center space-x-6">
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="status" value="active" class="form-radio h-4 w-4 text-primary focus:ring-primary" checked>
                                        <span class="ml-2 text-gray-700">启用</span>
                                    </label>
                                    <label class="inline-flex items-center">
                                        <input type="radio" name="status" value="inactive" class="form-radio h-4 w-4 text-primary focus:ring-primary">
                                        <span class="ml-2 text-gray-700">禁用</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label for="channel_address" class="block text-sm font-medium text-gray-700 mb-1">渠道地址 <span class="text-red-500">*</span></label>
                            <input type="text" id="channel_address" name="channel_address" value="https://api.mch.weixin.qq.com" required class="w-full">
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                            <textarea id="description" name="description" rows="4" class="w-full">官方接入的微信支付账号</textarea>
                        </div>
                    </div>

                    <!-- Tab Content Panel 2: 凭证信息 -->
                    <div id="content-credentials" class="tab-content hidden space-y-6">
                        <!-- 凭证信息 (根据渠道类型动态显示) -->
                        <h3 class="text-lg font-medium text-gray-900">凭证信息</h3>
                        <div id="credential-fields" class="space-y-6">
                            <!-- 微信支付凭证 -->
                            <div id="wechat-credentials" class="space-y-4">
                                <div>
                                    <label for="wx_appid" class="block text-sm font-medium text-gray-700 mb-1">AppID</label>
                                    <input type="text" id="wx_appid" name="credentials[wx_appid]" value="wx1234567890abcdef" class="w-full">
                                </div>
                                <div>
                                    <label for="wx_appsecret" class="block text-sm font-medium text-gray-700 mb-1">AppSecret</label>
                                    <div class="relative">
                                        <input type="password" id="wx_appsecret" name="credentials[wx_appsecret]" value="**********" class="w-full pr-10">
                                        <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-primary focus:outline-none" onclick="togglePasswordVisibility('wx_appsecret')">
                                            <i class="far fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label for="wx_apikey_v3" class="block text-sm font-medium text-gray-700 mb-1">APIv3密钥</label>
                                    <div class="relative">
                                        <input type="password" id="wx_apikey_v3" name="credentials[wx_apikey_v3]" value="********************************" class="w-full pr-10">
                                        <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-primary focus:outline-none" onclick="togglePasswordVisibility('wx_apikey_v3')">
                                            <i class="far fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label for="wx_cert_path" class="block text-sm font-medium text-gray-700 mb-1">API证书路径 (apiclient_cert.pem)</label>
                                    <input type="text" id="wx_cert_path" name="credentials[wx_cert_path]" value="/path/to/apiclient_cert.pem" class="w-full">
                                </div>
                                <div>
                                    <label for="wx_key_path" class="block text-sm font-medium text-gray-700 mb-1">API密钥路径 (apiclient_key.pem)</label>
                                    <input type="text" id="wx_key_path" name="credentials[wx_key_path]" value="/path/to/apiclient_key.pem" class="w-full">
                                </div>
                            </div>
                            <!-- 支付宝凭证 -->
                            <div id="alipay-credentials" class="space-y-4 hidden">
                                <div>
                                    <label for="alipay_appid" class="block text-sm font-medium text-gray-700 mb-1">AppID</label>
                                    <input type="text" id="alipay_appid" name="credentials[alipay_appid]" class="w-full">
                                </div>
                                <div>
                                    <label for="alipay_private_key" class="block text-sm font-medium text-gray-700 mb-1">应用私钥 (RSA)</label>
                                    <textarea id="alipay_private_key" name="credentials[alipay_private_key]" rows="4" class="w-full font-mono text-xs"></textarea>
                                </div>
                                <div>
                                    <label for="alipay_public_key" class="block text-sm font-medium text-gray-700 mb-1">支付宝公钥 (RSA)</label>
                                    <textarea id="alipay_public_key" name="credentials[alipay_public_key]" rows="4" class="w-full font-mono text-xs"></textarea>
                                </div>
                            </div>
                            <!-- 云闪付凭证 -->
                            <div id="unionpay-credentials" class="space-y-4 hidden">
                                <div>
                                    <label for="unionpay_sign_cert_path" class="block text-sm font-medium text-gray-700 mb-1">签名证书路径 (.pfx)</label>
                                    <input type="text" id="unionpay_sign_cert_path" name="credentials[unionpay_sign_cert_path]" class="w-full">
                                </div>
                                <div>
                                    <label for="unionpay_sign_cert_pwd" class="block text-sm font-medium text-gray-700 mb-1">签名证书密码</label>
                                    <div class="relative">
                                        <input type="password" id="unionpay_sign_cert_pwd" name="credentials[unionpay_sign_cert_pwd]" class="w-full pr-10">
                                        <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-primary focus:outline-none" onclick="togglePasswordVisibility('unionpay_sign_cert_pwd')">
                                            <i class="far fa-eye"></i>
                                        </button>
                                    </div>
                                </div>
                                <div>
                                    <label for="unionpay_encrypt_cert_path" class="block text-sm font-medium text-gray-700 mb-1">加密证书路径 (.cer)</label>
                                    <input type="text" id="unionpay_encrypt_cert_path" name="credentials[unionpay_encrypt_cert_path]" class="w-full">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content Panel 3: 关联员工 -->
                    <div id="content-employees" class="tab-content hidden">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">关联渠道员工</h3>
                        <div id="employee-fields">
                            <div class="flex justify-between items-center mb-4">
                                <div class="text-sm text-gray-600">选择需要关联的渠道员工</div>
                                <button type="button" class="btn btn-primary py-1 px-3 text-sm" id="add-employee">
                                    <i class="fas fa-plus mr-1"></i> 添加员工
                                </button>
                            </div>

                            <!-- 员工列表 -->
                            <div class="overflow-x-auto border rounded-lg">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">选择</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工姓名</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" name="employees[]" value="EMP001" class="form-checkbox h-5 w-5 text-primary focus:ring-primary" checked>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP001</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138000</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="tag tag-green">在职</span>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" name="employees[]" value="EMP002" class="form-checkbox h-5 w-5 text-primary focus:ring-primary" checked>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP002</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13900139000</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="tag tag-green">在职</span>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" name="employees[]" value="EMP003" class="form-checkbox h-5 w-5 text-primary focus:ring-primary" checked>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP003</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王五</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13700137000</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="tag tag-gray">离职</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 (Moved outside tab content, inside form) -->
                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-8">
                        <a href="channel_account.html" class="btn btn-light">取消</a>
                        <button type="submit" class="btn btn-primary" id="submit-button">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- 使用国内CDN的jQuery和Select2 -->
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/js/select2.min.js"></script>

    <script>
         // 页面加载时初始化
        $(document).ready(function() {
            const urlParams = new URLSearchParams(window.location.search);
            const accountId = urlParams.get('id');
            
            // 初始化Select2
            $('.select2-basic').select2({
                width: '100%'
            });

            // 初始化 Tab 切换
            initializeStandardTabs();

            // 根据是否有ID判断是创建还是编辑
            if (accountId) {
                $('#main-title').text('编辑渠道账号');
                $('#breadcrumb-action').text('编辑渠道账号');
                $('#submit-button').text('保存');
                // 实际应用中，这里会根据ID加载数据填充表单
                 $('#channel_account_id').val(accountId); 
                 // 假设加载的数据是微信支付
                updateCredentialFields('wechat'); 
            } else {
                $('#main-title').text('创建渠道账号');
                $('#breadcrumb-action').text('创建渠道账号');
                $('#submit-button').text('创建');
                 $('#channel_account_id').val(''); // 清空ID
                 updateCredentialFields($('#channel_type').val()); // 根据默认选择更新
                 // 清空表单内容，需要遍历所有tab
                 $('#content-basic-info input:not([type=radio]):not([type=hidden]), #content-basic-info select, #content-basic-info textarea').val('');
                 $('#content-basic-info input[type=radio]').prop('checked', false); // Clear radios
                 $('#content-credentials input, #content-credentials textarea').val(''); 
                 $('#content-employees table tbody').empty(); // Clear employee table
                 $('.select2-basic').val(null).trigger('change'); // 重置Select2
                 // Reset tabs to default state
                 $('.tab-link').removeClass('active');
                 $('.tab-link[data-tab="content-basic-info"]').addClass('active');
                 $('.tab-content').addClass('hidden').removeClass('active');
                 $('#content-basic-info').removeClass('hidden').addClass('active');
            }
            
            // 渠道类型选择变化时，更新凭证字段
            $('#channel_type').on('change', function() {
                const selectedType = $(this).val();
                updateCredentialFields(selectedType);
            });

            // 初始化时也调用一次，确保凭证字段正确显示
            updateCredentialFields($('#channel_type').val());
            
            // 添加员工按钮点击事件
            $('#add-employee').on('click', function() {
                openEmployeeModal();
            });
        });

        // 更新显示的凭证字段
        function updateCredentialFields(channelType) {
            // 先隐藏所有凭证字段区域
            $('#credential-fields > div[id$="-credentials"]').hide();
            // 显示对应渠道的凭证字段区域
            $('#' + channelType + '-credentials').show();
        }
        
        // 切换密码可见性
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            if (input.type === "password") {
                input.type = "text";
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = "password";
                 icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        // 打开添加员工模态框
        function openEmployeeModal() {
            // 创建模态框
            if (!document.getElementById('employee-modal')) {
                const modalHTML = `
                <div id="employee-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
                    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl">
                        <div class="flex justify-between items-center border-b px-6 py-4">
                            <h3 class="text-lg font-medium text-gray-900">选择员工</h3>
                            <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="p-6">
                            <div class="mb-4">
                                <input type="text" placeholder="搜索员工..." class="w-full border rounded-lg px-4 py-2">
                            </div>
                            <div class="overflow-x-auto border rounded-lg max-h-96">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">选择</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工姓名</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" class="form-checkbox h-5 w-5 text-primary focus:ring-primary">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP004</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵六</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13600136000</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="tag tag-green">在职</span>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" class="form-checkbox h-5 w-5 text-primary focus:ring-primary">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP005</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钱七</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13500135000</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="tag tag-green">在职</span>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" class="form-checkbox h-5 w-5 text-primary focus:ring-primary">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP006</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">周八</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13400134000</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="tag tag-green">在职</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="px-6 py-4 border-t flex justify-end space-x-3">
                            <button type="button" class="btn btn-light" onclick="closeEmployeeModal()">取消</button>
                            <button type="button" class="btn btn-primary" onclick="addSelectedEmployees()">添加选中员工</button>
                        </div>
                    </div>
                </div>`;
                
                document.body.insertAdjacentHTML('beforeend', modalHTML);
            }
            
            // 显示模态框
            document.getElementById('employee-modal').style.display = 'flex';
        }
        
        // 关闭添加员工模态框
        function closeEmployeeModal() {
            document.getElementById('employee-modal').style.display = 'none';
        }
        
        // 添加选中的员工
        function addSelectedEmployees() {
            // 模拟添加选中的员工（实际应用中应该获取选中的复选框并添加相应的员工）
            // 这里只是一个示例，添加一个新员工
            const tbody = document.querySelector('#employee-fields table tbody');
            const newRow = `
            <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                    <input type="checkbox" name="employees[]" value="EMP004" class="form-checkbox h-5 w-5 text-primary focus:ring-primary" checked>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EMP004</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵六</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13600136000</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="tag tag-green">在职</span>
                </td>
            </tr>`;
            
            tbody.insertAdjacentHTML('beforeend', newRow);
            closeEmployeeModal();
        }

        // JavaScript for Standard Tab Switching
        function initializeStandardTabs() {
            const tabLinks = document.querySelectorAll('.tab-nav .tab-link'); // Target links within .tab-nav
            // Updated selector: Find the parent .card, then find the content wrapper inside it
             let contentWrapperParent = document.querySelector('.tab-nav');
             let tabContentWrapper = null;
             if (contentWrapperParent) {
                 // Assuming the wrapper is the next sibling div
                 tabContentWrapper = contentWrapperParent.nextElementSibling; 
                 // More robust: find sibling div with padding
                 // tabContentWrapper = $(contentWrapperParent).next('div[class*="p-"]').get(0); 
             }

            if (!tabLinks.length || !tabContentWrapper) {
                 console.error("Tab elements not found or wrapper is not the immediate sibling.");
                return; // Exit if essential elements are missing
            }

            const allTabContentDivs = tabContentWrapper.querySelectorAll('.tab-content'); // Find content within the wrapper

            if (!allTabContentDivs.length) return; // Exit if no content panels found

            tabLinks.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 1. Deactivate all tab links
                    tabLinks.forEach(t => t.classList.remove('active'));

                    // 2. Hide all tab content panels using Tailwind's 'hidden' class
                    allTabContentDivs.forEach(content => content.classList.add('hidden'));
                    // Remove 'active' class from content if used for styling
                    allTabContentDivs.forEach(content => content.classList.remove('active'));

                    // 3. Activate the clicked tab link
                    this.classList.add('active');

                    // 4. Show the corresponding content panel
                    const contentId = this.getAttribute('data-tab');
                    const contentToShow = document.getElementById(contentId);
                    if (contentToShow) {
                        contentToShow.classList.remove('hidden');
                        contentToShow.classList.add('active'); // Add 'active' back for potential styling
                    }
                });
            });

            // Ensure initial state: Show content corresponding to the initially active tab link
            const initialActiveTabLink = document.querySelector('.tab-nav .tab-link.active');
            let initialContentId = null;
            if (initialActiveTabLink) {
                initialContentId = initialActiveTabLink.getAttribute('data-tab');
            } else if (tabLinks.length > 0) {
                // Fallback: If no link is active, make the first one active
                tabLinks[0].classList.add('active');
                initialContentId = tabLinks[0].getAttribute('data-tab');
            }

            allTabContentDivs.forEach(content => {
                if (content.id === initialContentId) {
                    content.classList.remove('hidden');
                    content.classList.add('active'); // Ensure initial content is active
                } else {
                    content.classList.add('hidden');
                    content.classList.remove('active'); // Ensure others are not active
                }
            });
        }

        // 侧边栏子菜单折叠展开 (与列表页相同逻辑)
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            if(!submenu || !icon) return;
             let isParentActive = item.closest('.nav-item').classList.contains('active');
             let hasActiveChild = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.classList.contains('font-semibold')) {
                     hasActiveChild = true;
                }
            });
            if (isParentActive || hasActiveChild) {
                 submenu.classList.add('active');
                 icon.style.transform = 'rotate(180deg)';
            } else {
                 submenu.classList.remove('active');
                 icon.style.transform = 'rotate(0deg)';
            }
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单 (与列表页相同逻辑)
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 动态设置当前导航项的激活状态 (与列表页相同逻辑)
        const currentPath = window.location.pathname.split('/').pop();
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
        document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');
        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a');
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            let isCurrentParent = false;
            let isCurrentPage = false;
            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                 isCurrentPage = true;
            } 
            else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary');
                    }
                });
            }
            const parentMap = {
                 'channel_account_edit.html': 'channel_account.html',
                 'channel_employee.html': 'channel_account.html', 
                 'channel_employee_edit.html': 'channel_account.html'
            };
            const parentPath = parentMap[currentPath];
             if (parentPath) {
                 submenuLinks.forEach(subLink => {
                     if (subLink.getAttribute('href') === parentPath) {
                         subLink.classList.add('font-semibold', 'text-primary');
                         isCurrentParent = true;
                     }
                 });
                  // Check if the top level nav item itself is the parent (when no submenu involved or direct link)
                 if (link && link.getAttribute('href') === parentPath) {
                     isCurrentParent = true;
                 }
            }
            if (isCurrentPage || isCurrentParent) {
                 navItem.classList.add('active');
                const submenu = navItem.querySelector('.submenu');
                const icon = navItem.querySelector('.submenu-icon');
                if (submenu && isCurrentParent) {
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            }
        });
    </script>
</body>
</html> 
