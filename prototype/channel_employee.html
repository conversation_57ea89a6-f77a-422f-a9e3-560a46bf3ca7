<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道员工管理 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
     <!-- Select2 CSS -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
        .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }


        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px; 
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
            color: #64748b; 
            font-weight: 500; 
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

         .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        
        .btn-outline-primary {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background-color: transparent;
        }
        .btn-outline-primary:hover {
             background-color: var(--primary-light);
        }
        
        .btn-outline-danger {
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
            background-color: transparent;
        }
        .btn-outline-danger:hover {
             background-color: rgba(225, 112, 85, 0.1);
        }
        .btn-outline-success {
            border: 1px solid var(--success-color);
            color: var(--success-color);
            background-color: transparent;
        }
        .btn-outline-success:hover {
            background-color: rgba(0, 184, 148, 0.1);
        }
         .btn-outline-warning {
            border: 1px solid var(--warning-color);
            color: #d6a100;
            background-color: transparent;
        }
        .btn-outline-warning:hover {
             background-color: rgba(253, 203, 110, 0.15);
        }


        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 分页样式 */
        .pagination a, .pagination span {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border-radius: 0.375rem;
            color: #64748b;
            transition: all 0.2s;
        }
        .pagination a:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        .pagination .active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 5px rgba(59, 124, 254, 0.3);
        }
        .pagination .disabled {
            color: #cbd5e1;
            cursor: not-allowed;
        }
        
        /* Select2 适配 */
         .select2-container--default .select2-selection--single {
            height: calc(2.25rem + 2px) !important; /* 适应 Tailwind 表单高度 */
            border: 1px solid #e0e6ed !important;
            border-radius: 0.5rem !important;
            padding: 0.375rem 0.75rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5rem !important;
            padding-left: 0 !important;
            color: #495057;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(2.25rem + 2px) !important;
            top: 0.1rem !important;
        }
        .select2-container--default.select2-container--open .select2-selection--single {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
        }
         .select2-dropdown {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
     <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
             <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                   
                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="#" class="block py-2 text-gray-400 hover:text-primary cursor-not-allowed">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4 active">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-primary font-semibold">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-500">渠道账号管理</span>
             <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">渠道员工</span>
        </div>

        <h2 class="text-2xl font-bold text-gray-800 mb-6">渠道员工管理</h2>

        <!-- 筛选和操作区域 -->
        <div class="card mb-6 p-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex flex-wrap items-center gap-4">
                    <input type="text" placeholder="搜索员工姓名或登录账号" class="w-full sm:w-auto">
                     <select class="w-full sm:w-64 select2-basic">
                        <option value="">所有渠道账号</option>
                        <option value="CHNACC001">官方微信支付账号 (CHNACC001)</option>
                        <option value="CHNACC002">官方支付宝账号 (CHNACC002)</option>
                        <option value="CHNACC003">测试云闪付账号 (CHNACC003)</option>
                        <!-- 更多渠道账号 -->
                    </select>
                    <select class="w-full sm:w-auto">
                        <option value="">所有状态</option>
                        <option value="active">启用</option>
                        <option value="inactive">禁用</option>
                    </select>
                    <button class="btn btn-primary">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                    <button class="btn btn-light">
                        <i class="fas fa-undo mr-2"></i>重置
                    </button>
                </div>
                <a href="channel_employee_edit.html" class="btn btn-primary">
                    <i class="fas fa-user-plus mr-2"></i>创建渠道员工
                </a>
            </div>
        </div>

        <!-- 渠道员工列表 -->
        <div class="card">
            <div class="table-container">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="table-header">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                                ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                                员工姓名
                            </th>
                             <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                                登录账号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                                所属渠道账号
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                                状态
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">
                                创建时间
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 示例数据行 1 -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                EMP001
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                张三
                            </td>
                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                zhangsan_wx
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="#" class="text-primary hover:underline">官方微信支付账号 (CHNACC001)</a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2023-10-02 09:00:00
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                <a href="channel_employee_edit.html?id=EMP001" class="btn btn-outline-primary btn-sm py-1 px-2">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </a>
                                 <button class="btn btn-outline-warning btn-sm py-1 px-2" onclick="confirmResetPassword('EMP001', '张三')">
                                    <i class="fas fa-key mr-1"></i>重置密码
                                </button>
                                <button class="btn btn-outline-danger btn-sm py-1 px-2">
                                    <i class="fas fa-toggle-off mr-1"></i>禁用
                                </button>
                            </td>
                        </tr>
                         <!-- 示例数据行 2 -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                EMP002
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                李四
                            </td>
                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                lisi_alipay
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <a href="#" class="text-primary hover:underline">官方支付宝账号 (CHNACC002)</a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-gray">禁用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                2023-09-20 14:30:00
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                <a href="channel_employee_edit.html?id=EMP002" class="btn btn-outline-primary btn-sm py-1 px-2">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </a>
                                 <button class="btn btn-outline-warning btn-sm py-1 px-2" onclick="confirmResetPassword('EMP002', '李四')">
                                    <i class="fas fa-key mr-1"></i>重置密码
                                </button>
                                <button class="btn btn-outline-success btn-sm py-1 px-2">
                                    <i class="fas fa-toggle-on mr-1"></i>启用
                                </button>
                            </td>
                        </tr>
                        <!-- 更多数据行... -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                </div>
                <nav class="pagination" aria-label="Pagination">
                    <span class="disabled">&laquo;</span>
                    <span class="active">1</span>
                    <!-- <a href="#">2</a> -->
                    <span class="disabled">&raquo;</span>
                </nav>
            </div>
        </div>
    </main>

    <!-- 使用国内CDN的jQuery和Select2 -->
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/js/select2.min.js"></script>

    <script>
        $(document).ready(function() {
            // 初始化Select2
            $('.select2-basic').select2();
        });
        
         // 确认重置密码
        function confirmResetPassword(employeeId, employeeName) {
            if (confirm(`确定要重置员工 "${employeeName}" (ID: ${employeeId}) 的密码吗？`)) {
                // 在实际应用中，这里会调用后端API来重置密码
                alert(`员工 "${employeeName}" 的密码已重置（模拟操作）。`);
                // 可以添加进一步的操作，例如显示新密码或发送通知
            }
        }

        // 侧边栏子菜单折叠展开 (与列表页相同逻辑)
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            if(!submenu || !icon) return;
             let isParentActive = item.closest('.nav-item').classList.contains('active');
             let hasActiveChild = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.classList.contains('font-semibold')) {
                     hasActiveChild = true;
                }
            });
            if (isParentActive || hasActiveChild) {
                 submenu.classList.add('active');
                 icon.style.transform = 'rotate(180deg)';
            } else {
                 submenu.classList.remove('active');
                 icon.style.transform = 'rotate(0deg)';
            }
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单 (与列表页相同逻辑)
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

       // 动态设置当前导航项的激活状态 (与列表页相同逻辑，适配当前页面)
        const currentPath = window.location.pathname.split('/').pop();
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
        document.querySelectorAll('.menu-toggle.text-primary').forEach(item => item.classList.remove('text-primary')); // Remove text-primary from toggles initially
        document.querySelectorAll('a.bg-primary-light.text-primary').forEach(item => item.classList.remove('bg-primary-light', 'text-primary')); // Remove highlight from direct links
        document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');

        // Parent map for related pages
        const parentMap = {
            'channel_account_edit.html': 'channel_account.html',
            'channel_employee.html': 'channel_account.html',
            'channel_employee_edit.html': 'channel_account.html'
        };
        const parentPathForMenuHighlight = parentMap[currentPath];

        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a:not(.submenu a)'); // Get top-level link
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            const menuToggle = navItem.querySelector('.menu-toggle');
            const submenu = navItem.querySelector('.submenu');
            const icon = navItem.querySelector('.submenu-icon');

            let isCurrentPage = false;
            let isCurrentParent = false;
            let activeSubLink = null;

            // Check direct link match
            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                isCurrentPage = true;
            }
            // Check submenu link match
            else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        activeSubLink = subLink; // Store the active sublink
                    }
                });
            }

            // Check if the current page belongs to a parent via parentMap
            if (!isCurrentPage && !isCurrentParent && parentPathForMenuHighlight) {
                 let potentialParentLinkHref = null;
                 // Check if this navItem contains the target parent link (either direct or submenu)
                 if (link && link.getAttribute('href') === parentPathForMenuHighlight) {
                      potentialParentLinkHref = link.getAttribute('href');
                 } else {
                     submenuLinks.forEach(subLink => {
                          if(subLink.getAttribute('href') === parentPathForMenuHighlight) {
                               potentialParentLinkHref = subLink.getAttribute('href');
                          }
                     });
                 }

                 // If this navItem corresponds to the mapped parent
                 if(potentialParentLinkHref) {
                     isCurrentParent = true; // Treat as parent active
                     // Find the actual sublink corresponding to the current page to highlight it
                     submenuLinks.forEach(subLink => {
                          if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                               activeSubLink = subLink;
                          }
                     });
                 }
            }


            // Apply styles based on findings
            if (isCurrentPage) {
                 navItem.classList.add('active');
                 if(link) link.classList.add('bg-primary-light', 'text-primary'); // Highlight direct link
            } else if (isCurrentParent) {
                 navItem.classList.add('active');
                 if (menuToggle) menuToggle.classList.add('text-primary'); // Highlight parent toggle text
                 if (activeSubLink) activeSubLink.classList.add('font-semibold', 'text-primary'); // Highlight specific sublink
                 if (submenu) {
                     submenu.classList.add('active');
                     if(icon) icon.style.transform = 'rotate(180deg)';
                 }
            }
        });
        
        // 示例：表格行悬停效果 (可选)
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseover', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseout', function() {
                this.classList.remove('bg-gray-50');
            });
        });
    </script>
</body>
</html> 
