<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑渠道员工 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
     <!-- Select2 CSS -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
        .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }


        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px; 
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }


        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }
         input.error, textarea.error, select.error + .select2-container--default .select2-selection--single {
            border-color: var(--danger-color) !important;
         }
         .error-message {
             color: var(--danger-color);
             font-size: 0.875rem;
             margin-top: 0.25rem;
         }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

         .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        
        /* Select2 适配 */
         .select2-container--default .select2-selection--single {
            height: calc(2.25rem + 2px) !important; /* 适应 Tailwind 表单高度 */
            border: 1px solid #e0e6ed !important;
            border-radius: 0.5rem !important;
            padding: 0.375rem 0.75rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5rem !important;
            padding-left: 0 !important;
            color: #495057;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(2.25rem + 2px) !important;
            top: 0.1rem !important;
        }
        .select2-container--default.select2-container--open .select2-selection--single {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
        }
         .select2-dropdown {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
         .select2-container.error + .select2-container--default .select2-selection--single {
             border-color: var(--danger-color) !important;
         }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
         <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">个人信息</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">系统设置</a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
             <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
           
                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 (当前页面父级，需要高亮和展开) -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4 active">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-primary font-semibold">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
         <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="channel_account.html" class="hover:text-primary">渠道账号管理</a>
            <i class="fas fa-angle-right mx-2"></i>
             <a href="channel_employee.html" class="hover:text-primary">渠道员工</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800" id="breadcrumb-action">编辑渠道员工</span>
        </div>

        <h2 class="text-2xl font-bold text-gray-800 mb-6" id="main-title">编辑渠道员工</h2>

        <!-- 编辑表单 -->
        <div class="card p-8">
            <form id="channel-employee-form" class="space-y-6" onsubmit="return validateForm();">
                 <!-- 隐藏域，用于存储ID -->
                <input type="hidden" id="employee_id" name="id" value="EMP001">
                
                <div>
                    <label for="employee_name" class="block text-sm font-medium text-gray-700 mb-1">员工姓名 <span class="text-red-500">*</span></label>
                    <input type="text" id="employee_name" name="employee_name" value="张三" required class="w-full">
                    <p class="error-message hidden" id="employee_name-error"></p>
                </div>

                 <div>
                    <label for="login_account" class="block text-sm font-medium text-gray-700 mb-1">登录账号 <span class="text-red-500">*</span></label>
                    <input type="text" id="login_account" name="login_account" value="zhangsan_wx" required class="w-full bg-gray-100" readonly>
                    <p class="error-message hidden" id="login_account-error"></p>
                    <p class="text-xs text-gray-500 mt-1">登录账号创建后不可修改。</p>
                </div>

                 <div>
                    <label for="channel_account_id" class="block text-sm font-medium text-gray-700 mb-1">所属渠道账号 <span class="text-red-500">*</span></label>
                    <select id="channel_account_id" name="channel_account_id" required class="w-full select2-basic">
                         <option value="">请选择</option>
                         <option value="CHNACC001" selected>官方微信支付账号 (CHNACC001)</option>
                         <option value="CHNACC002">官方支付宝账号 (CHNACC002)</option>
                         <option value="CHNACC003">测试云闪付账号 (CHNACC003)</option>
                         <!-- 更多渠道账号 -->
                    </select>
                     <p class="error-message hidden" id="channel_account_id-error"></p>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态 <span class="text-red-500">*</span></label>
                    <div class="flex items-center space-x-6">
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" value="active" class="form-radio h-4 w-4 text-primary focus:ring-primary" checked>
                            <span class="ml-2 text-gray-700">启用</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" value="inactive" class="form-radio h-4 w-4 text-primary focus:ring-primary">
                            <span class="ml-2 text-gray-700">禁用</span>
                        </label>
                    </div>
                </div>
                
                 <div class="border-t border-gray-200 pt-6 mt-6 space-y-6">
                     <h3 class="text-lg font-medium text-gray-900 mb-2">密码设置</h3>
                     <p class="text-sm text-gray-500" id="password-info">编辑模式下，如需修改密码请填写，否则请留空。</p>
                      <div>
                         <label for="password" class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                         <div class="relative">
                             <input type="password" id="password" name="password" class="w-full pr-10">
                             <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-primary focus:outline-none" onclick="togglePasswordVisibility('password')">
                                 <i class="far fa-eye"></i>
                             </button>
                         </div>
                          <p class="error-message hidden" id="password-error"></p>
                     </div>
                     <div>
                         <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
                         <input type="password" id="password_confirmation" name="password_confirmation" class="w-full">
                         <p class="error-message hidden" id="password_confirmation-error"></p>
                     </div>
                </div>
                
                <div>
                    <label for="remarks" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                    <textarea id="remarks" name="remarks" rows="4" class="w-full">微信渠道操作员</textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 mt-6">
                    <a href="channel_employee.html" class="btn btn-light">取消</a>
                    <button type="submit" class="btn btn-primary" id="submit-button">保存</button>
                </div>
            </form>
        </div>
    </main>

    <!-- 使用国内CDN的jQuery和Select2 -->
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/js/select2.min.js"></script>

    <script>
         let isEditMode = false;
         
         // 页面加载时初始化
        $(document).ready(function() {
            const urlParams = new URLSearchParams(window.location.search);
            const employeeId = urlParams.get('id');
            
            // 初始化Select2
            $('.select2-basic').select2();

            // 根据是否有ID判断是创建还是编辑
            if (employeeId) {
                 isEditMode = true;
                $('#main-title').text('编辑渠道员工');
                $('#breadcrumb-action').text('编辑渠道员工');
                $('#submit-button').text('保存');
                $('#login_account').prop('readonly', true).addClass('bg-gray-100'); // 编辑时登录账号只读
                $('#password-info').show();
                // 实际应用中，这里会根据ID加载数据填充表单
                 $('#employee_id').val(employeeId); 
                 // 假设加载的数据
                $('#employee_name').val('张三');
                $('#login_account').val('zhangsan_wx');
                $('#channel_account_id').val('CHNACC001').trigger('change');
                $('input[name="status"][value="active"]').prop('checked', true);
                 $('#remarks').val('微信渠道操作员');
            } else {
                 isEditMode = false;
                $('#main-title').text('创建渠道员工');
                $('#breadcrumb-action').text('创建渠道员工');
                $('#submit-button').text('创建');
                 $('#employee_id').val(''); // 清空ID
                 $('#login_account').prop('readonly', false).removeClass('bg-gray-100');
                 $('#password-info').hide();
                 $('#password').prop('required', true); // 创建时密码必填
                 $('#password_confirmation').prop('required', true);
                 $('#channel-employee-form')[0].reset(); // 清空表单
                 $('.select2-basic').val(null).trigger('change'); // 重置Select2
                 $('input[name="status"][value="active"]').prop('checked', true); // 默认启用
            }
        });
        
        // 表单验证
        function validateForm() {
            let isValid = true;
             // 清除之前的错误信息
            $('.error-message').addClass('hidden').text('');
            $('input, select, textarea').removeClass('error');
            $('.select2-container').removeClass('error');

            // 检查必填项
            $('#channel-employee-form [required]').each(function() {
                 const $this = $(this);
                 let value = $this.val();
                 const fieldName = $this.attr('name');
                 const $errorElement = $('#' + fieldName + '-error');
                 
                 if (!value) {
                     isValid = false;
                     $this.addClass('error');
                     // 特殊处理Select2
                     if ($this.hasClass('select2-basic')) {
                         $this.next('.select2-container').addClass('error');
                     }
                     if ($errorElement.length) {
                         $errorElement.text('此字段不能为空').removeClass('hidden');
                     }
                 }
            });

             // 密码验证
            const password = $('#password').val();
            const passwordConfirmation = $('#password_confirmation').val();
            const $passwordError = $('#password-error');
            const $passwordConfirmationError = $('#password_confirmation-error');

             if (!isEditMode && !password) { // 创建模式下密码必填
                 isValid = false;
                 $('#password').addClass('error');
                 $passwordError.text('密码不能为空').removeClass('hidden');
            }
            
            if (password || passwordConfirmation) { // 只有在输入密码时才校验确认密码
                 if (password !== passwordConfirmation) {
                    isValid = false;
                    $('#password').addClass('error');
                    $('#password_confirmation').addClass('error');
                    $passwordConfirmationError.text('两次输入的密码不一致').removeClass('hidden');
                 } else if (!isEditMode && password.length < 6) { // 创建模式或修改密码时检查长度 (示例最少6位)
                     isValid = false;
                      $('#password').addClass('error');
                      $passwordError.text('密码长度不能少于6位').removeClass('hidden');
                 } else if (isEditMode && password && password.length < 6) { // 编辑模式修改密码时检查长度
                      isValid = false;
                      $('#password').addClass('error');
                      $passwordError.text('密码长度不能少于6位').removeClass('hidden');
                 }
                
                 // 如果填写了密码，确认密码也必须填写
                 if (!passwordConfirmation) {
                    isValid = false;
                     $('#password_confirmation').addClass('error');
                    $passwordConfirmationError.text('请再次输入新密码').removeClass('hidden');
                 }
            }

            return isValid; // 如果验证通过，返回true，表单将提交；否则返回false，阻止提交
        }
        
        // 切换密码可见性
        function togglePasswordVisibility(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            if (input.type === "password") {
                input.type = "text";
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = "password";
                 icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        

        // 侧边栏子菜单折叠展开 (与列表页相同逻辑)
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            if(!submenu || !icon) return;
             let isParentActive = item.closest('.nav-item').classList.contains('active');
             let hasActiveChild = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.classList.contains('font-semibold')) {
                     hasActiveChild = true;
                }
            });
            if (isParentActive || hasActiveChild) {
                 submenu.classList.add('active');
                 icon.style.transform = 'rotate(180deg)';
            } else {
                 submenu.classList.remove('active');
                 icon.style.transform = 'rotate(0deg)';
            }
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单 (与列表页相同逻辑)
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 动态设置当前导航项的激活状态 (与列表页相同逻辑)
        const currentPath = window.location.pathname.split('/').pop();
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
        document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');
        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a');
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            let isCurrentParent = false;
            let isCurrentPage = false;
            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                 isCurrentPage = true;
            } 
            else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary');
                    }
                });
            }
             const parentMap = {
                 'channel_account_edit.html': 'channel_account.html',
                 'channel_employee.html': 'channel_account.html', 
                 'channel_employee_edit.html': 'channel_account.html' // 当前页面
            };
            const parentPathForMenuHighlight = 'channel_employee.html'; // 编辑员工页，应该高亮员工列表页
            const topLevelParentPath = parentMap[currentPath]; // 父菜单是 渠道账号管理

            // 高亮父菜单（渠道账号管理）
            if (topLevelParentPath) {
                 let parentNavItem = null;
                 // 查找顶级父菜单项
                 document.querySelectorAll('.nav-item').forEach(item => {
                    const menuToggle = item.querySelector('.menu-toggle');
                    if(menuToggle && item.querySelector('a[href="channel_account.html"]') && item.querySelector('a[href="channel_employee.html"]')){
                       parentNavItem = item; 
                    }
                 });
                 
                 if(parentNavItem && parentNavItem === navItem) {
                    isCurrentParent = true; // 标记当前navItem是父菜单
                 }
            }
            // 高亮子菜单（渠道员工）
            submenuLinks.forEach(subLink => {
                if (subLink.getAttribute('href') === parentPathForMenuHighlight) {
                    subLink.classList.add('font-semibold', 'text-primary');
                    isCurrentParent = true; // 确保父菜单也高亮
                }
            });

            if (isCurrentPage || isCurrentParent) {
                 navItem.classList.add('active');
                const submenu = navItem.querySelector('.submenu');
                const icon = navItem.querySelector('.submenu-icon');
                if (submenu && isCurrentParent) {
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            }
        });
    </script>
</body>
</html> 
