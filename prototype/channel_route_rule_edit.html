<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增/编辑渠道路由规则 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
            padding-top: 4rem; /* 确保内容不被头部遮挡 */
            min-height: calc(100vh - 4rem); /* 保证内容区高度至少为视口高度减去头部高度 */
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }

        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }

        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .submenu.active {
            max-height: 500px; /* 适当增加，确保子菜单能完全展开 */
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }

        /* 不添加悬停效果的卡片 */
        .card-static {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
            color: #6b7280; /* 表头文字颜色 */
            font-weight: 500;
        }
        
        .table-body tr:hover {
             background-color: #f9fafb; /* 行悬停背景色 */
        }
        
        .table-body td {
            padding: 0.75rem 1rem; /* 单元格内边距 */
            border-bottom: 1px solid #e5e7eb; /* 行分隔线 */
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem; /* Add bottom margin for wrapping */
            white-space: nowrap;
        }

        .tag-blue { background-color: rgba(59, 124, 254, 0.15); color: var(--primary-color); }
        .tag-green { background-color: rgba(0, 184, 148, 0.15); color: var(--success-color); }
        .tag-purple { background-color: rgba(108, 92, 231, 0.15); color: var(--secondary-color); }
        .tag-yellow { background-color: rgba(253, 203, 110, 0.15); color: #d6a100; }
        .tag-red { background-color: rgba(225, 112, 85, 0.15); color: var(--danger-color); }
        .tag-gray { background-color: rgba(45, 52, 54, 0.1); color: #636e72; }

        /* 表单元素样式 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
            width: 100%; /* 默认占满父容器宽度 */
        }

        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #4b5563; /* 标签颜色 */
        }

        /* 按钮样式 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
            display: inline-flex; /* 让图标和文字能垂直居中 */
            align-items: center;
            justify-content: center;
        }

        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-primary:hover { background-color: var(--primary-dark); transform: translateY(-1px); box-shadow: 0 3px 10px rgba(59, 124, 254, 0.3); }
        .btn-secondary { background-color: var(--secondary-color); color: white; }
        .btn-secondary:hover { background-color: #584bbd; transform: translateY(-1px); box-shadow: 0 3px 10px rgba(108, 92, 231, 0.3); }
        .btn-success { background-color: var(--success-color); color: white; }
        .btn-success:hover { background-color: #00a37d; transform: translateY(-1px); box-shadow: 0 3px 10px rgba(0, 184, 148, 0.3); }
        .btn-danger { background-color: var(--danger-color); color: white; }
        .btn-danger:hover { background-color: #c95a41; transform: translateY(-1px); box-shadow: 0 3px 10px rgba(225, 112, 85, 0.3); }
        .btn-warning { background-color: var(--warning-color); color: #5a4f2f; }
        .btn-warning:hover { background-color: #e4b750; transform: translateY(-1px); box-shadow: 0 3px 10px rgba(253, 203, 110, 0.3); }
        .btn-light { background-color: #f1f5f9; color: #64748b; border: 1px solid #e2e8f0; }
        .btn-light:hover { background-color: #e2e8f0; }
        
        .btn i {
            margin-right: 0.5rem; /* 图标和文字间距 */
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .dashed-box {
            border: 1px dashed #d1d5db;
            border-radius: 0.5rem;
            padding: 1rem;
            min-height: 60px;
            background-color: #f9fafb;
        }

        /* New styles for channel item list */
        .channel-item {
            border: 1px solid var(--border-color);
            border-radius: 0.75rem; /* Slightly larger radius */
            transition: all 0.3s;
            background-color: #fff; 
        }
        
        .channel-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        
        .channel-icon {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            flex-shrink: 0; /* Prevent icon shrinking */
        }
        
        .channel-condition-tags {
            display: inline-flex;
            flex-wrap: wrap;
            margin-left: 1rem;
            align-items: center;
        }
        /* Style for the default channel radio button label */
        /* Remove radio button specific styles */
        /* .default-channel-label {
            ...
        } */

        /* Ensure switch styles are present (from layout template) */
        .switch {
            position: relative;
            display: inline-block;
            width: 40px; /* Adjust width if needed */
            height: 20px; /* Adjust height if needed */
            vertical-align: middle; /* Align switch nicely with text */
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 20px; /* Match height */
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px; /* Smaller than height */
            width: 16px; /* Smaller than height */
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(20px); /* width - height */
        }

    </style>
</head>
<body class="flex">
    <!-- 侧边栏 (Updated structure and links to match channel_route_rules.html) -->
    <aside class="sidebar fixed top-0 left-0 h-screen overflow-y-auto pt-16"> <!-- pt-16 避开头部 -->
        <nav class="mt-6 px-4">
            <ul>
                 <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                 <li class="nav-item">
                     <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                 <!-- 条件筛选管理 -->
                 <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                     <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>

                 <!-- 支付方式管理 -->
                 <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                         <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 (Active state will be handled by JS, structure matched) -->
                <li class="nav-item"> <!-- Removed active class, JS will handle this based on page -->
                     <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg"> <!-- Reset default style -->
                         <div class="flex items-center">
                              <i class="fas fa-random w-5 h-5 text-center"></i> <!-- Changed icon -->
                              <span class="ml-3">渠道路由管理</span>
                         </div>
                         <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i> <!-- Reset icon rotation -->
                     </div>
                     <ul class="submenu pl-12 pr-4"> <!-- Removed active class, JS will handle this -->
                         <li class="my-2">
                             <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                 路由规则
                             </a>
                         </li>
                         <li class="my-2">
                             <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                 版本管理
                             </a>
                         </li>
                     </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                 <!-- 商户号管理 -->
                 <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg"> <!-- Assuming merchant_management.html is correct, else change to merchant.html -->
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                 <!-- 应用管理 -->
                 <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                 <!-- 渠道账号管理 (Added from dashboard.html) -->
                 <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary"> <!-- Added link -->
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary"> <!-- Added link -->
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>

                <!-- 系统设置 REMOVED - Already removed in channel_route_rule_edit.html -->
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="content flex-1">
        <!-- 页面头部 -->
        <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
            <div class="flex items-center space-x-4">
                <!-- 侧边栏折叠按钮 -->
                <button id="sidebarToggle" class="text-white focus:outline-none lg:hidden">
                     <i class="fas fa-bars"></i>
                </button>
                <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8 hidden sm:block">
                <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
            </div>
            <div class="flex items-center space-x-5">
                <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                    <i class="fas fa-bell text-white"></i>
                    <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                </button>
                <div class="relative">
                    <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                        <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                        <span class="text-white">管理员</span>
                        <i class="fas fa-chevron-down text-white text-xs"></i>
                    </button>
                    <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                        <div class="py-1">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                                <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                                <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                            </a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                                <i class="fas fa-sign-out-alt mr-2 text-gray-500"></i>退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- 实际页面内容 -->
        <main class="p-6">
            <!-- Breadcrumb Navigation -->
            <div class="mb-4 flex items-center text-sm text-gray-500"> <!-- Reduced bottom margin slightly -->
                <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
                <i class="fas fa-angle-right mx-2"></i>
                <a href="channel_route_rules.html" class="hover:text-primary">渠道路由管理</a>
                <i class="fas fa-angle-right mx-2"></i>
                <span class="text-gray-800">新增/编辑规则</span>
            </div>

            <h2 class="text-2xl font-semibold mb-6 text-gray-700">新增/编辑渠道路由规则</h2>

            <form action="#" method="POST">
                <!-- Section 1: 基本信息 -->
                <div class="card mb-6"> 
                    <div class="p-6"> 
                        <section class="mb-0"> 
                            <h3 class="section-title">基本信息</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="rule_name">规则名称 <span class="text-red-500">*</span></label>
                                    <input type="text" name="rule_name" id="rule_name" required placeholder="请输入规则名称">
                                </div>
                                <div>
                                    <label for="priority">规则优先级 <span class="text-red-500">*</span></label>
                                    <input type="number" name="priority" id="priority" required placeholder="数字越小，优先级越高" min="0">
                                </div>
                                <div class="md:col-span-2">
                                    <label for="description">规则描述</label>
                                    <textarea id="description" name="description" rows="3" placeholder="请输入规则描述信息"></textarea>
                                </div>
                                <div>
                                    <label for="status">状态 <span class="text-red-500">*</span></label>
                                    <select id="status" name="status" required>
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>

                <!-- Section 2: 适用支付方式 (Changed to Single Select) -->
                <div class="card mb-6">
                    <div class="p-6">
                        <section class="mb-0">
                            <h3 class="section-title">适用支付方式 <span class="text-red-500">*</span></h3>
                            <!-- Removed multi-select tags and button -->
                            <div>
                                <label for="payment_method" class="sr-only">选择支付方式</label> <!-- Screen reader label -->
                                <select id="payment_method" name="payment_method" required class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    <option value="">-- 请选择一个支付方式 --</option>
                                    <option value="WECHAT_PAY">微信支付</option>
                                    <option value="ALIPAY">支付宝</option>
                                    <option value="UNIONPAY">银联支付</option>
                                    <option value="JD_PAY">京东支付</option>
                                    <!-- Add other payment methods as needed -->
                                </select>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">指定此路由规则适用于哪一种支付方式。</p>
                        </section>
                    </div>
                </div>

                <!-- Section 3: 触发条件规则 (Removed) -->
                <!-- The entire card for Section 3 has been removed -->

                <!-- Section 4: 路由目标渠道 (按优先级) -->
                <div class="card mb-6">
                    <div class="p-6">
                        <section class="mb-0">
                            <div class="flex justify-between items-center mb-3">
                                <h3 class="section-title mb-0 pb-0 border-none">路由目标渠道 (按优先级) <span class="text-red-500">*</span></h3>
                                <button type="button" id="add-target-channel-btn" class="btn btn-success btn-sm">
                                    <i class="fas fa-plus"></i> 添加目标渠道
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 mb-3">规则触发后，系统将按优先级顺序尝试渠道。可指定一个渠道作为默认备选，当前面渠道均不满足条件时使用。</p>
                            <div class="space-y-4 mb-6" id="target-channel-list">
                                <!-- Example Channel Item 1: WeChat -->
                                <div class="channel-item p-4" data-channel-id="WECHAT_OFFICIAL">
                                    <div class="flex items-center justify-between flex-wrap">
                                        <!-- Left side: Icon, Name, Conditions -->
                                        <div class="flex items-center flex-grow mb-2 md:mb-0">
                                            <div class="channel-icon bg-green-100 text-green-600 mr-3">
                                                <i class="fab fa-weixin text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">微信支付官方通道</h4>
                                                <p class="text-xs text-gray-500">WECHAT_OFFICIAL</p>
                                            </div>
                                            <div class="channel-condition-tags ml-4">
                                                <span class="tag tag-blue">用户地区=华南</span>
                                                <span class="tag tag-purple">订单金额&lt;1000</span>
                                            </div>
                                        </div>
                                        <!-- Right side: Priority, Add Condition, Default Toggle, Delete -->
                                        <div class="flex items-center space-x-3 md:space-x-4 flex-shrink-0">
                                            <div class="flex items-center">
                                                <label class="text-sm text-gray-600 mr-1 md:mr-2 whitespace-nowrap">优先级:</label>
                                                <input type="number" value="1" min="0" class="w-14 md:w-16 text-sm p-1 border border-gray-300 rounded">
                                            </div>
                                            <button type="button" class="add-condition-btn btn btn-primary btn-sm flex items-center text-xs px-2 md:px-3">
                                                <i class="fas fa-plus mr-1"></i> 添加条件
                                            </button>
                                            <label class="flex items-center cursor-pointer text-sm text-gray-600">
                                                <span class="mr-2">默认备选</span>
                                                <div class="switch">
                                                     <input type="checkbox" class="default-channel-toggle" value="WECHAT_OFFICIAL"> 
                                                     <span class="slider"></span>
                                                </div>
                                            </label>
                                            <button type="button" class="text-red-500 hover:text-red-700">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Example Channel Item 2: Bank Aggregate -->
                                <div class="channel-item p-4" data-channel-id="BANK_AGGREGATE_XYZ">
                                     <div class="flex items-center justify-between flex-wrap">
                                        <!-- Left side -->
                                        <div class="flex items-center flex-grow mb-2 md:mb-0">
                                             <div class="channel-icon bg-purple-100 text-purple-600 mr-3">
                                                <i class="fas fa-university text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">某银行聚合支付通道</h4>
                                                <p class="text-xs text-gray-500">BANK_AGGREGATE_XYZ</p>
                                            </div>
                                            <div class="channel-condition-tags ml-4">
                                                <span class="text-gray-400 italic text-sm">暂无独立条件</span>
                                            </div>
                                        </div>
                                        <!-- Right side -->
                                         <div class="flex items-center space-x-3 md:space-x-4 flex-shrink-0">
                                            <div class="flex items-center">
                                                <label class="text-sm text-gray-600 mr-1 md:mr-2 whitespace-nowrap">优先级:</label>
                                                <input type="number" value="2" min="0" class="w-14 md:w-16 text-sm p-1 border border-gray-300 rounded">
                                            </div>
                                            <button type="button" class="add-condition-btn btn btn-primary btn-sm flex items-center text-xs px-2 md:px-3">
                                                <i class="fas fa-plus mr-1"></i> 添加条件
                                            </button>
                                            <label class="flex items-center cursor-pointer text-sm text-gray-600">
                                                <span class="mr-2">默认备选</span>
                                                <div class="switch">
                                                    <input type="checkbox" class="default-channel-toggle" value="BANK_AGGREGATE_XYZ" checked>
                                                    <span class="slider"></span>
                                                </div>
                                            </label>
                                            <button type="button" class="text-red-500 hover:text-red-700">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Example Channel Item 3: Alipay -->
                                <div class="channel-item p-4" data-channel-id="ALIPAY_OFFICIAL">
                                     <div class="flex items-center justify-between flex-wrap">
                                        <!-- Left side -->
                                        <div class="flex items-center flex-grow mb-2 md:mb-0">
                                             <div class="channel-icon bg-blue-100 text-blue-600 mr-3">
                                                <i class="fab fa-alipay text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">支付宝官方通道</h4>
                                                <p class="text-xs text-gray-500">ALIPAY_OFFICIAL</p>
                                            </div>
                                             <div class="channel-condition-tags ml-4">
                                                <span class="tag tag-yellow">设备类型=iOS</span>
                                            </div>
                                        </div>
                                        <!-- Right side -->
                                         <div class="flex items-center space-x-3 md:space-x-4 flex-shrink-0">
                                            <div class="flex items-center">
                                                <label class="text-sm text-gray-600 mr-1 md:mr-2 whitespace-nowrap">优先级:</label>
                                                <input type="number" value="3" min="0" class="w-14 md:w-16 text-sm p-1 border border-gray-300 rounded">
                                            </div>
                                            <button type="button" class="add-condition-btn btn btn-primary btn-sm flex items-center text-xs px-2 md:px-3">
                                                <i class="fas fa-plus mr-1"></i> 添加条件
                                            </button>
                                            <label class="flex items-center cursor-pointer text-sm text-gray-600">
                                                <span class="mr-2">默认备选</span>
                                                <div class="switch">
                                                     <input type="checkbox" class="default-channel-toggle" value="ALIPAY_OFFICIAL">
                                                     <span class="slider"></span>
                                                </div>
                                            </label>
                                            <button type="button" class="text-red-500 hover:text-red-700">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </section>
                    </div>
                </div>

                <!-- 表单操作按钮 (Moved outside the last card, but inside the form) -->
                <div class="mt-8 flex justify-end space-x-3">
                    <button type="button" class="btn btn-light">
                        取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存
                    </button>
                    <button type="submit" name="publish" value="true" class="btn btn-secondary">
                        <i class="fas fa-paper-plane"></i> 保存并发布
                    </button>
                </div>
            </form>

        </main>
    </div>

    <script>
        // 侧边栏菜单交互
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const submenu = document.querySelector(this.dataset.target);
                const arrow = this.querySelector('.arrow-icon');
                
                document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                    if (openSubmenu !== submenu) {
                        openSubmenu.classList.remove('active');
                        const otherArrow = openSubmenu.previousElementSibling.querySelector('.arrow-icon');
                        if (otherArrow) {
                             otherArrow.classList.remove('rotate-180');
                        }
                    }
                });

                if (submenu) {
                    submenu.classList.toggle('active');
                    if (arrow) {
                        arrow.classList.toggle('rotate-180');
                    }
                }
            });
        });

        // 用户下拉菜单交互
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown && userMenu) {
            userDropdown.addEventListener('click', () => {
                userMenu.classList.toggle('hidden');
            });
            document.addEventListener('click', (event) => {
                 if (!userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                    userMenu.classList.add('hidden');
                }
            });
        }

        // 移动端侧边栏切换 (如果需要实现)
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.querySelector('.sidebar');
        const content = document.querySelector('.content');

        if (sidebarToggle && sidebar && content) {
            const toggleSidebar = () => {
                sidebar.classList.toggle('hidden'); // Simple toggle for visibility
                if (sidebar.classList.contains('hidden')) {
                    content.style.marginLeft = '0';
                } else {
                    content.style.marginLeft = '260px';
                }
            };

            sidebarToggle.addEventListener('click', toggleSidebar);

            // Adjust sidebar based on initial screen size and resize
            const adjustLayout = () => {
                if (window.innerWidth < 1024) {
                    sidebar.classList.add('hidden');
                    content.style.marginLeft = '0';
                } else {
                    sidebar.classList.remove('hidden');
                    content.style.marginLeft = '260px';
                }
            };

            window.addEventListener('resize', adjustLayout);
            adjustLayout(); // Initial check
        }
        
        // Simplified menu toggle logic (adapted from channel_route_rules.html)
         document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function(e) {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                const parentNavItem = this.closest('.nav-item');

                // Close other open submenus first
                document.querySelectorAll('.nav-item .submenu.active').forEach(openSubmenu => {
                    const otherParent = openSubmenu.closest('.nav-item');
                    if (openSubmenu !== submenu) {
                        openSubmenu.classList.remove('active');
                        // Use style for max-height transition
                        openSubmenu.style.maxHeight = '0'; 
                        const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                        if (otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                         // Deactivate other parent nav-item only if it's not the current one we are opening or already active
                        // Make sure not to remove active class from the main active section
                        if(otherParent !== parentNavItem && !otherParent.classList.contains('active')) { 
                            // Reset style of the other menu toggle
                            const otherToggle = otherParent.querySelector('.menu-toggle');
                            if (otherToggle) {
                                otherToggle.classList.remove('text-primary', 'bg-primary-light');
                                otherToggle.classList.add('text-gray-700');
                            }
                        }
                    }
                });

                // Toggle current submenu
                if (submenu && submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    submenu.style.maxHeight = '0';
                    if(icon) icon.style.transform = 'rotate(0deg)';
                     // If we close the currently active parent, keep the parent active style
                    if (!parentNavItem.classList.contains('active')) {
                        this.classList.remove('text-primary', 'bg-primary-light');
                        this.classList.add('text-gray-700');
                    }
                } else if (submenu) {
                    submenu.classList.add('active');
                     // Set max-height based on scrollHeight for smooth transition
                    submenu.style.maxHeight = submenu.scrollHeight + 'px'; 
                    if(icon) icon.style.transform = 'rotate(180deg)';
                    // Apply active style to the toggle itself
                    this.classList.remove('text-gray-700');
                    this.classList.add('text-primary', 'bg-primary-light');
                    // Ensure parent nav-item remains active (it should already be) 
                    parentNavItem.classList.add('active'); 
                }
            });
        });

        // --- Simplified JS for Default Channel Toggle --- 
        document.addEventListener('DOMContentLoaded', () => {
             console.log("DOM fully loaded."); 
             
             // --- Active Menu Item Handling (Adapted from channel_route_rules.html) ---
              console.log("Setting active menu item...");
              const currentPath = window.location.pathname.split('/').pop() || 'channel_route_rule_edit.html'; // Default to edit page
              console.log("Current Path:", currentPath);

              // Clear previous active states more robustly
              document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
              document.querySelectorAll('.submenu.active').forEach(submenu => {
                  submenu.classList.remove('active');
                  submenu.style.maxHeight = '0';
                  const icon = submenu.previousElementSibling.querySelector('.submenu-icon');
                  if(icon) icon.style.transform = 'rotate(0deg)';
              });
              document.querySelectorAll('.menu-toggle.text-primary').forEach(toggle => {
                  toggle.classList.remove('text-primary', 'bg-primary-light');
                  toggle.classList.add('text-gray-700');
              });
              document.querySelectorAll('.submenu li.active').forEach(li => li.classList.remove('active'));
              document.querySelectorAll('.submenu a.font-semibold').forEach(a => a.classList.remove('font-semibold'));

              // Find the target link for the current page or parent menu
              // Since edit page link is removed, we target the parent or the list page link
              const parentToggle = document.querySelector('.nav-item.active .menu-toggle'); // Find the active parent toggle
              const listPageLink = document.querySelector(`.sidebar a[href="channel_route_rules.html"]`);
              let targetElementForActivation = null;
              if (currentPath === 'channel_route_rule_edit.html') {
                   // On edit page, activate the parent menu toggle
                   targetElementForActivation = parentToggle;
                   console.log("On edit page, targeting parent toggle for activation:", targetElementForActivation);
              } else {
                   // For other pages, find the direct link
                   targetElementForActivation = document.querySelector(`.sidebar a[href="${currentPath}"]`);
                   console.log("Target Link Element:", targetElementForActivation);
              }

              if (targetElementForActivation) {
                  const navItem = targetElementForActivation.closest('.nav-item');
                  console.log("Parent Nav Item:", navItem);
                  
                  if (navItem) {
                      navItem.classList.add('active');
                      
                      // Check if the activated element is a menu toggle (meaning a parent menu)
                      if (targetElementForActivation.classList.contains('menu-toggle')) {
                          const submenu = targetElementForActivation.nextElementSibling;
                          const icon = targetElementForActivation.querySelector('.submenu-icon');
                          if (submenu && submenu.classList.contains('submenu')) {
                              submenu.classList.add('active');
                              submenu.style.maxHeight = submenu.scrollHeight + 'px'; // Expand submenu
                              targetElementForActivation.classList.remove('text-gray-700');
                              targetElementForActivation.classList.add('text-primary', 'bg-primary-light'); // Style active toggle
                              if(icon) icon.style.transform = 'rotate(180deg)';
                              console.log("Activated parent menu toggle and expanded submenu.");
                          }
                      } else { 
                           // If it's a direct link within a submenu (e.g., list page or version page)
                           const submenuLi = targetElementForActivation.closest('.submenu > li');
                           console.log("Parent Submenu LI:", submenuLi);
                            if (submenuLi) {
                                submenuLi.classList.add('active');
                                targetElementForActivation.classList.add('text-primary', 'font-semibold'); // Style active sublink
                                console.log("Activated submenu LI and link styles.");

                                const submenu = submenuLi.closest('.submenu');
                                const toggle = submenu.previousElementSibling; // The .menu-toggle div
                                console.log("Submenu Element:", submenu);
                                console.log("Toggle Element:", toggle);

                                if (submenu && toggle && toggle.classList.contains('menu-toggle')) {
                                    submenu.classList.add('active');
                                    submenu.style.maxHeight = submenu.scrollHeight + 'px'; // Expand submenu
                                    toggle.classList.remove('text-gray-700');
                                    toggle.classList.add('text-primary', 'bg-primary-light'); // Style active toggle
                                    const icon = toggle.querySelector('.submenu-icon');
                                    if(icon) icon.style.transform = 'rotate(180deg)';
                                    console.log("Expanded submenu and styled toggle for sublink.");
                                } else {
                                    console.warn("Could not find submenu or toggle for active sublink.");
                                }
                             } else {
                                console.log("Target link is a direct child of nav-item (no submenu).");
                                // Apply active style directly to the link itself if needed
                                // targetLink.classList.add('text-primary', 'bg-primary-light'); 
                             }
                      }
                  } else {
                     console.error("Could not find parent nav-item for the target element.");
                  }
              } else {
                console.warn(`Could not find sidebar element for activation based on path: ${currentPath}`);
              }
              console.log("Active menu item setup complete.");

             // --- Default Channel Toggle Initialization --- 
             console.log("Initializing default channel toggle logic..."); 
             const toggles = document.querySelectorAll('.default-channel-toggle');
             console.log(`Found ${toggles.length} toggle checkboxes.`);
 
             function enforceSingleCheck(changedCheckbox) {
                 console.log(`[Change Handler] Checkbox ${changedCheckbox.value} changed to state: ${changedCheckbox.checked}`); 
                 if (changedCheckbox.checked) {
                     console.log(`[Enforce Single] ${changedCheckbox.value} is checked. Unchecking others...`); 
                     toggles.forEach(otherToggle => {
                         if (otherToggle !== changedCheckbox && otherToggle.checked) {
                             console.log(`[Enforce Single] Unchecking ${otherToggle.value}`); 
                             otherToggle.checked = false;
                         }
                     });
                 }
             }
 
             toggles.forEach(checkbox => {
                 console.log(`Attaching change listener to checkbox ${checkbox.value}`);
                  checkbox.addEventListener('change', function() {
                      enforceSingleCheck(this); 
                  });
             });
 
             console.log("Running initial check for multiple 'checked' attributes..."); 
             let hasChecked = false;
             toggles.forEach(toggle => {
                 if (toggle.checked) {
                      console.log(`${toggle.value} has initial 'checked'`); 
                     if (hasChecked) {
                         console.warn(`Found multiple initial 'checked'. Unchecking ${toggle.value}`); 
                         toggle.checked = false; 
                     } else {
                         hasChecked = true;
                     }
                 }
             });
             console.log("Default toggle initialization complete."); 

            // --- Add Channel Modal Functionality (Now inside DOMContentLoaded) ---
            console.log("Initializing Add Channel Modal logic...");
            const addChannelModal = document.getElementById('addChannelModal');
            const openModalButton = document.getElementById('add-target-channel-btn');
            const closeModalButton = document.getElementById('closeAddChannelModal');
            const cancelAddChannelButton = document.getElementById('cancelAddChannel');
            const confirmAddChannelButton = document.getElementById('confirmAddChannel');
            const channelSearchInput = document.getElementById('channelSearchInput');
            const channelListContainer = document.getElementById('channelListContainer');
            const targetChannelsListDiv = document.getElementById('target-channel-list');

            // Debugging: Check if elements are found (now guaranteed to be after DOM load)
            console.log("Add Channel Modal:", addChannelModal);
            console.log("Open Modal Button:", openModalButton);
            console.log("Close Modal Button:", closeModalButton);
            console.log("Cancel Button:", cancelAddChannelButton);
            console.log("Confirm Button:", confirmAddChannelButton);
            console.log("Search Input:", channelSearchInput);
            console.log("Channel List Container:", channelListContainer);
            console.log("Target Channels List Div:", targetChannelsListDiv);

            // Event Listeners for Modal Control
            if (openModalButton && addChannelModal) {
                console.log("Attaching click listener to Open Modal Button.");
                openModalButton.addEventListener('click', () => {
                    console.log("Open Modal Button clicked!");
                    showModal(); // Call the globally defined function
                });
            } else {
                if (!openModalButton) console.error("Open Modal Button (add-target-channel-btn) not found!");
                if (!addChannelModal) console.error("Add Channel Modal element not found!");
            }

            if (closeModalButton && addChannelModal) {
                closeModalButton.addEventListener('click', () => {
                    console.log("Close Modal Button clicked!");
                    hideModal(); // Call the globally defined function
                });
            } else {
                 if (!closeModalButton) console.error("Close Modal Button (closeAddChannelModal) not found!");
                 if (!addChannelModal) console.error("Add Channel Modal element not found!");
            }

            if (cancelAddChannelButton && addChannelModal) {
                cancelAddChannelButton.addEventListener('click', () => {
                     console.log("Cancel Add Channel Button clicked!");
                     hideModal();
                });
            } else {
                 if (!cancelAddChannelButton) console.error("Cancel Add Channel Button (cancelAddChannel) not found!");
                 if (!addChannelModal) console.error("Add Channel Modal element not found!");
            }

            if (addChannelModal) {
                // Hide modal if clicking on the overlay background
                addChannelModal.addEventListener('click', (event) => {
                    if (event.target === addChannelModal) {
                         console.log("Clicked on modal overlay.");
                        hideModal();
                    }
                });
            } else {
                console.error("Add Channel Modal element not found for overlay click listener!");
            }
            
             // Channel Search Listener
            if (channelSearchInput && channelListContainer) {
                console.log("Attaching input listener to Channel Search Input.");
                channelSearchInput.addEventListener('input', filterChannels); // Call the globally defined function
                 // Initial filter call
                 filterChannels(); 
            } else {
                 if (!channelSearchInput) console.error("Channel Search Input not found!");
                 if (!channelListContainer) console.error("Channel List Container not found for search!");
            }
            
            // Confirm Button Listener
            if (confirmAddChannelButton && targetChannelsListDiv && channelListContainer && addChannelModal) {
                 console.log("Attaching click listener to Confirm Add Channel Button.");
                confirmAddChannelButton.addEventListener('click', () => {
                     console.log("Confirm Add Channel Button clicked!");
                    const selectedChannels = [];
                    const checkboxes = channelListContainer.querySelectorAll('input[type="checkbox"]:checked');

                    checkboxes.forEach(checkbox => {
                        const label = checkbox.nextElementSibling; // Assuming label is the next sibling
                        const containerDiv = checkbox.closest('.channel-option'); // Get the container to potentially get more data later
                        if (label && containerDiv) {
                            selectedChannels.push({
                                id: checkbox.value, // Use value attribute for channel ID
                                name: label.textContent.trim(),
                                // iconClass: containerDiv.dataset.iconClass || 'fa-solid fa-credit-card', // Example if storing icon info
                                // bgColor: containerDiv.dataset.bgColor || 'bg-gray-100'      // Example if storing color info
                            });
                        }
                    });

                    console.log("Selected Channels:", selectedChannels);

                    // Add selected channels to the main list
                    addChannelsToTargetList(selectedChannels); // Call the globally defined function

                    // Hide the modal
                    hideModal(); // Call the globally defined function
                });
             } else {
                 if (!confirmAddChannelButton) console.error("Confirm Add Channel Button not found!");
                 if (!targetChannelsListDiv) console.error("Target Channels List Div not found!");
                 if (!channelListContainer) console.error("Channel List Container not found for confirm!");
                 if (!addChannelModal) console.error("Add Channel Modal element not found for confirm!");
             }

            console.log("Add Channel Modal initialization complete.");

            // --- Configure Channel Condition Modal Functionality ---
            console.log("Initializing Configure Channel Condition Modal logic...");
            const configureModal = document.getElementById('configureChannelConditionModal');
            const configureModalTitle = document.getElementById('configureModalTitle');
            const conditionModuleListContainer = document.getElementById('channelConditionModuleListContainer');
            const conditionModuleSource = document.getElementById('condition-module-list-source');
            const closeConfigureModalButton = document.getElementById('closeConfigureModal');
            const cancelConfigureModalButton = document.getElementById('cancelConfigureModal');
            const confirmConfigureModalButton = document.getElementById('confirmConfigureModal');
            const mainChannelListContainer = document.getElementById('target-channel-list'); // Container for all channel cards

            // Debug checks
            console.log("Configure Modal:", configureModal);
            console.log("Configure Modal Title:", configureModalTitle);
            console.log("Condition Module List Container:", conditionModuleListContainer);
            console.log("Condition Module Source:", conditionModuleSource);
            console.log("Main Channel List Container:", mainChannelListContainer);

            // Event Delegation for "Add Condition" buttons
            if (mainChannelListContainer && configureModal && conditionModuleSource && configureModalTitle && conditionModuleListContainer) {
                mainChannelListContainer.addEventListener('click', (event) => {
                    const button = event.target.closest('.add-condition-btn');
                    if (!button) return; // Click wasn't on an add condition button or its child

                    event.preventDefault(); // Prevent any default button action if needed
                    console.log("Add Condition button clicked!");

                    const channelCard = button.closest('.channel-item');
                    if (!channelCard) return;

                    const channelId = channelCard.dataset.channelId;
                    const channelNameElement = channelCard.querySelector('h4'); // Find the h4 for the name
                    const channelName = channelNameElement ? channelNameElement.textContent.trim() : '未知渠道';
                    const currentModuleIdsString = channelCard.dataset.conditionModuleId || ''; // Get current module IDs string
                    const currentModuleIds = currentModuleIdsString ? currentModuleIdsString.split(',') : []; // Split into array, default to empty array

                    console.log(`Configuring conditions for Channel ID: ${channelId}, Name: ${channelName}, Current Modules: [${currentModuleIds.join(', ')}]`);

                    // Set modal title
                    configureModalTitle.textContent = `配置 ${channelName} 的独立条件`;

                    // Clear previous list and clone new one
                    conditionModuleListContainer.innerHTML = ''; // Clear existing content
                    const clonedContent = conditionModuleSource.cloneNode(true); // Deep clone the source list
                    // Append children of the cloned div, not the div itself, to maintain structure
                    while (clonedContent.firstChild) {
                         conditionModuleListContainer.appendChild(clonedContent.firstChild);
                    }

                    // Set the correct checkboxes as checked
                    const checkboxesInModal = conditionModuleListContainer.querySelectorAll('input[type="checkbox"]');
                    checkboxesInModal.forEach(checkbox => {
                        if (currentModuleIds.includes(checkbox.value)) {
                            checkbox.checked = true;
                            console.log(`Set checkbox with value '${checkbox.value}' to checked.`);
                        }
                    });

                    // Store channel ID in modal for confirm button to access
                    configureModal.dataset.editingChannelId = channelId;

                    // Show the modal
                    configureModal.classList.remove('hidden');
                    configureModal.classList.add('flex');
                });
            } else {
                 console.error("One or more elements needed for condition modal delegation are missing.");
            }

            // Function to hide the configure modal
            function hideConfigureModal() {
                 if (configureModal) {
                    configureModal.classList.add('hidden');
                    configureModal.classList.remove('flex');
                    delete configureModal.dataset.editingChannelId; // Clear editing state
                 } else {
                    console.error("Configure modal element not found in hideConfigureModal!");
                 }
            }

            // Event listeners for closing the configure modal
            if (closeConfigureModalButton) {
                closeConfigureModalButton.addEventListener('click', hideConfigureModal);
            }
            if (cancelConfigureModalButton) {
                cancelConfigureModalButton.addEventListener('click', hideConfigureModal);
            }
            if (configureModal) {
                configureModal.addEventListener('click', (event) => {
                    if (event.target === configureModal) { // Click on overlay
                         hideConfigureModal();
                    }
                });
            }

            // Event listener for confirming condition module selection
            if (confirmConfigureModalButton && mainChannelListContainer && conditionModuleListContainer) {
                confirmConfigureModalButton.addEventListener('click', () => {
                    const channelId = configureModal.dataset.editingChannelId;
                    if (!channelId) {
                        console.error("Cannot confirm: editingChannelId not found in modal dataset.");
                        hideConfigureModal();
                        return;
                    }

                    const channelCard = mainChannelListContainer.querySelector(`.channel-item[data-channel-id="${channelId}"]`);
                    if (!channelCard) {
                         console.error(`Cannot confirm: Channel card with ID ${channelId} not found.`);
                         hideConfigureModal();
                         return;
                    }

                    const selectedCheckboxes = conditionModuleListContainer.querySelectorAll('input[type="checkbox"]:checked');
                    const selectedModuleIds = [];
                    const selectedModulesData = []; // Store id and name for tag generation

                    selectedCheckboxes.forEach(checkbox => {
                        const moduleId = checkbox.value;
                        const label = conditionModuleListContainer.querySelector(`label[for="${checkbox.id}"]`);
                        const moduleName = label ? label.textContent.trim() : '未知模块';
                        selectedModuleIds.push(moduleId);
                        selectedModulesData.push({ id: moduleId, name: moduleName });
                    });

                    const newModuleIdsString = selectedModuleIds.join(','); // Join selected IDs with comma

                    console.log(`Confirming selection for Channel ID: ${channelId}. Module IDs: [${newModuleIdsString}]`);

                    // Update data attribute on the channel card
                    channelCard.dataset.conditionModuleId = newModuleIdsString;

                    // Update the condition tags display
                    const tagsContainer = channelCard.querySelector('.channel-condition-tags');
                    if (tagsContainer) {
                        tagsContainer.innerHTML = ''; // Clear existing tags/placeholder
                        if (selectedModulesData.length > 0) {
                            selectedModulesData.forEach(module => {
                                const newTag = document.createElement('span');
                                newTag.className = 'tag tag-purple'; // Use a consistent color for module tags
                                newTag.innerHTML = `<i class="fas fa-puzzle-piece mr-1"></i>模块: ${module.name}`;
                                tagsContainer.appendChild(newTag);
                            });
                        } else {
                            // Add back placeholder if nothing is selected
                            const placeholder = document.createElement('span');
                            placeholder.className = 'text-gray-400 italic text-sm condition-placeholder';
                            placeholder.textContent = '暂无独立条件';
                            tagsContainer.appendChild(placeholder);
                        }
                    } else {
                        console.warn(`Could not find .channel-condition-tags container for channel ${channelId}`);
                    }

                    hideConfigureModal();
                });
            } else {
                console.error("One or more elements needed for condition modal confirmation are missing.")
            }

            console.log("Configure Channel Condition Modal initialization complete.");

        });
        // --- End DOMContentLoaded Listener ---

        // --- Modal and Channel Functions (Defined Globally within script scope) ---

        // Function to show the modal
        function showModal() {
            // Modal element should be accessible here as it's defined in the outer scope
            // but this function is CALLED from within DOMContentLoaded where it's assigned.
             const modal = document.getElementById('addChannelModal'); // Re-get or ensure it's passed/available
             if (modal) {
                 console.log("Show Modal function called!");
                 modal.classList.remove('hidden');
                 modal.classList.add('flex'); // Use flex to center content
                 // Reset modal state every time it opens
                 const searchInput = document.getElementById('channelSearchInput');
                 const listContainer = document.getElementById('channelListContainer');
                 if(searchInput) searchInput.value = '';
                 if(listContainer) {
                     const checkboxes = listContainer.querySelectorAll('input[type="checkbox"]');
                     checkboxes.forEach(cb => cb.checked = false);
                 }
                 filterChannels(); // Ensure list is filtered correctly on open
             } else {
                 console.error("Modal element not found inside showModal! This shouldn't happen if called correctly.");
             }
        }

        // Function to hide the modal
        function hideModal() {
            const modal = document.getElementById('addChannelModal');
            if (modal) {
                console.log("Hide Modal function called.")
                modal.classList.add('hidden');
                modal.classList.remove('flex');
            } else {
                 console.error("Modal element not found inside hideModal!");
            }
        }

        // --- Channel Search Functionality ---
        function filterChannels() {
            const searchInput = document.getElementById('channelSearchInput');
            const listContainer = document.getElementById('channelListContainer');
            if (!searchInput || !listContainer) {
                console.warn("Search input or list container not found in filterChannels.");
                return; 
            }

            const searchTerm = searchInput.value.toLowerCase();
            const channelItems = listContainer.querySelectorAll('.channel-option');
            // console.log(`Filtering with term: "${searchTerm}"`); // Optional: Log search term

            channelItems.forEach(item => {
                const label = item.querySelector('label');
                if (label) {
                    const channelName = label.textContent.toLowerCase();
                    if (channelName.includes(searchTerm)) {
                        item.style.display = 'flex';
                    } else {
                        item.style.display = 'none';
                    }
                }
            });
        }

        // Function to add selected channels to the target list
        function addChannelsToTargetList(channelsToAdd) {
             const targetListDiv = document.getElementById('target-channel-list');
             if (!targetListDiv) {
                 console.error("Target channel list container not found in addChannelsToTargetList!");
                 return;
             }
             const existingChannelIds = new Set();
             targetListDiv.querySelectorAll('.channel-item').forEach(item => {
                 if(item.dataset.channelId) {
                    existingChannelIds.add(item.dataset.channelId);
                 }
             });

             channelsToAdd.forEach(channel => {
                if (!existingChannelIds.has(channel.id)) {
                     console.log(`Adding channel: ${channel.name} (ID: ${channel.id})`);
                     const newChannelElement = createChannelCardElement(channel); // Call the globally defined function
                     if (newChannelElement) {
                         targetListDiv.appendChild(newChannelElement);
                         // Re-initialize toggle switches for the newly added item
                         // Find the toggle specifically within the new element
                         const newToggleCheckbox = newChannelElement.querySelector('.default-channel-toggle');
                         if (newToggleCheckbox) {
                             console.log(`Initializing toggle for new channel: ${channel.id}`);
                             // Add listener directly - the enforceSingleCheck needs access to *all* toggles
                             newToggleCheckbox.addEventListener('change', function() {
                                 // We need a way to access the enforceSingleCheck or re-run the main init
                                 // Simpler approach: Re-run the toggle init logic on the entire list? Or pass the full list to enforce
                                 // Let's try re-running the full initialization for simplicity for now
                                  initializeDefaultToggles(); // Re-run the setup for all toggles
                             });
                             // Add to the global list if needed, or re-query inside enforceSingleCheck
                         } else {
                            console.warn(`Could not find toggle checkbox in new element for ${channel.id}`);
                         }
                         // Initialize remove button listener
                         const removeBtn = newChannelElement.querySelector('.remove-channel-btn');
                         if (removeBtn) {
                            removeBtn.addEventListener('click', () => {
                                console.log(`Removing channel: ${channel.id}`);
                                newChannelElement.remove();
                                // Potentially update default backup if the removed one was default
                                checkIfDefaultRemoved();
                            });
                         }
                         // Initialize add condition button listener
                         const addConditionBtn = newChannelElement.querySelector('.add-condition-btn');
                         if (addConditionBtn) {
                             addConditionBtn.addEventListener('click', () => {
                                 alert(`Configure conditions for ${channel.name} (ID: ${channel.id})`);
                                 // TODO: Implement condition configuration modal
                             });
                         }
                     }
                 } else {
                    console.warn(`Channel "${channel.name}" (ID: ${channel.id}) is already in the list.`);
                 }
             });
        }

         // Function to create a new channel card element
        function createChannelCardElement(channel) {
            const channelId = channel.id; 
            const channelName = channel.name;
            let iconHtml = `<i class="fa-solid fa-credit-card text-gray-500 text-xl"></i>`;
            let bgColor = 'bg-gray-100';

            // Assign specific icons and colors
            if (channelId === 'wechat') {
                iconHtml = `<i class="fab fa-weixin text-xl text-green-600"></i>`;
                bgColor = 'bg-green-100';
            } else if (channelId === 'alipay') {
                iconHtml = `<i class="fab fa-alipay text-xl text-blue-600"></i>`;
                bgColor = 'bg-blue-100';
            } else if (channelId === 'yinsheng') {
                 iconHtml = `<i class="fas fa-university text-xl text-purple-600"></i>`; // Example
                 bgColor = 'bg-purple-100';
            } else if (channelId === 'yeepay') {
                 iconHtml = `<i class="fas fa-yen-sign text-xl text-red-600"></i>`; // Example
                 bgColor = 'bg-red-100';
            } else if (channelId === 'tonglian') {
                 iconHtml = `<i class="fas fa-link text-xl text-orange-600"></i>`; // Example
                 bgColor = 'bg-orange-100';
            }
            
            const div = document.createElement('div');
            div.className = 'channel-item p-4 mb-4'; // Main container class
            div.dataset.channelId = channelId;

            div.innerHTML = `
                <div class="flex items-center justify-between flex-wrap">
                    <!-- Left side: Icon, Name, Conditions -->
                    <div class="flex items-center flex-grow mb-2 md:mb-0">
                        <div class="channel-icon ${bgColor} mr-3">
                            ${iconHtml}
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-800">${channelName}</h4>
                            <p class="text-xs text-gray-500">${channelId.toUpperCase()}</p> <!-- Display ID -->
                        </div>
                        <div class="channel-condition-tags ml-4">
                            <span class="text-gray-400 italic text-sm condition-placeholder">暂无独立条件</span>
                            <!-- Condition tags will be dynamically added here -->
                        </div>
                    </div>
                    <!-- Right side: Priority, Add Condition, Default Toggle, Delete -->
                    <div class="flex items-center space-x-3 md:space-x-4 flex-shrink-0">
                         <div class="flex items-center">
                             <label class="text-sm text-gray-600 mr-1 md:mr-2 whitespace-nowrap">优先级:</label>
                             <input type="number" value="99" min="0" class="channel-priority w-14 md:w-16 text-sm p-1 border border-gray-300 rounded">
                         </div>
                         <button type="button" class="add-condition-btn btn btn-primary btn-sm flex items-center text-xs px-2 md:px-3">
                             <i class="fas fa-plus mr-1"></i> 添加条件
                         </button>
                         <label class="flex items-center cursor-pointer text-sm text-gray-600">
                             <span class="mr-2">默认备选</span>
                             <div class="switch">
                                  <input type="checkbox" class="default-channel-toggle" value="${channelId}">
                                  <span class="slider"></span>
                             </div>
                         </label>
                         <button type="button" class="remove-channel-btn text-red-500 hover:text-red-700">
                             <i class="fas fa-trash-alt"></i>
                         </button>
                    </div>
                </div>
            `;
            
            return div;
        }

        // Function to re-initialize default toggles (call after adding/removing channels)
        function initializeDefaultToggles() {
            console.log("Re-initializing default channel toggle logic...");
            const toggles = document.querySelectorAll('.default-channel-toggle');
            console.log(`Found ${toggles.length} toggle checkboxes for re-initialization.`);

            // Remove existing listeners to prevent duplicates (important!)
            toggles.forEach(checkbox => {
                const clonedCheckbox = checkbox.cloneNode(true);
                checkbox.parentNode.replaceChild(clonedCheckbox, checkbox);
            });

            // Get the fresh list of toggles after cloning
            const freshToggles = document.querySelectorAll('.default-channel-toggle');

            function enforceSingleCheck(changedCheckbox) {
                 console.log(`[Re-init Change Handler] Checkbox ${changedCheckbox.value} changed to state: ${changedCheckbox.checked}`);
                if (changedCheckbox.checked) {
                     console.log(`[Re-init Enforce Single] ${changedCheckbox.value} is checked. Unchecking others...`);
                    freshToggles.forEach(otherToggle => {
                        if (otherToggle !== changedCheckbox && otherToggle.checked) {
                             console.log(`[Re-init Enforce Single] Unchecking ${otherToggle.value}`);
                            otherToggle.checked = false;
                        }
                    });
                }
                 checkIfDefaultRemoved(); // Check if a default exists after change
            }

            freshToggles.forEach(checkbox => {
                 console.log(`[Re-init] Attaching change listener to checkbox ${checkbox.value}`);
                 checkbox.addEventListener('change', function() {
                    enforceSingleCheck(this);
                });
            });
             console.log("Default toggle re-initialization complete.");
        }

        // Function to check if the default backup was removed and reset if needed
        function checkIfDefaultRemoved() {
            const toggles = document.querySelectorAll('.default-channel-toggle');
            let defaultExists = false;
            toggles.forEach(toggle => {
                if (toggle.checked) {
                    defaultExists = true;
                }
            });
            if (!defaultExists) {
                console.log("Default backup was removed or unset. No default backup is currently selected.");
                // Optionally, you could auto-select the first one, but for now, just log it.
            }
        }


    </script>

    <!-- Hidden Source List for Condition Modules (Now Checkboxes) -->
    <div id="condition-module-list-source" style="display: none;">
        <!-- Removed 'none' option -->
        <div class="condition-module-option flex items-center p-2 hover:bg-gray-100 rounded">
            <input id="modal-module-CM001" type="checkbox" value="CM001" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
            <label for="modal-module-CM001" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">乘客角色条件</label>
        </div>
        <div class="condition-module-option flex items-center p-2 hover:bg-gray-100 rounded">
            <input id="modal-module-CM002" type="checkbox" value="CM002" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
            <label for="modal-module-CM002" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">高级会员促销</label>
        </div>
         <div class="condition-module-option flex items-center p-2 hover:bg-gray-100 rounded">
            <input id="modal-module-CM003" type="checkbox" value="CM003" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
            <label for="modal-module-CM003" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">工作日晚间限制</label>
        </div>
        <!-- Add more predefined condition modules here as needed -->
    </div>

    <!-- Add Channel Modal (Existing) -->
    <div id="addChannelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md p-6 mx-4"> <!-- Added mx-4 for small screen padding -->
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-3 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">选择目标渠道</h3>
                <button id="closeAddChannelModal" class="text-gray-400 hover:text-gray-600">
                    <i class="material-icons">close</i> <!-- Using Material Icon -->
                </button>
            </div>
            <!-- Modal Body -->
            <div class="mt-4">
                <!-- Search Input -->
                <input type="text" id="channelSearchInput" placeholder="搜索渠道名称..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 mb-4">
                <!-- Channel List -->
                <div id="channelListContainer" class="max-h-60 overflow-y-auto space-y-2 pr-2"> <!-- Added pr-2 for scrollbar space -->
                    <!-- Channel items will be populated here -->
                    <div class="channel-option flex items-center p-2 hover:bg-gray-100 rounded">
                        <input id="modal-channel-wechat" type="checkbox" value="wechat" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                        <label for="modal-channel-wechat" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">微信支付</label>
                    </div>
                    <div class="channel-option flex items-center p-2 hover:bg-gray-100 rounded">
                        <input id="modal-channel-alipay" type="checkbox" value="alipay" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                        <label for="modal-channel-alipay" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">支付宝</label>
                    </div>
                    <div class="channel-option flex items-center p-2 hover:bg-gray-100 rounded">
                        <input id="modal-channel-yinsheng" type="checkbox" value="yinsheng" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                        <label for="modal-channel-yinsheng" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">银盛支付</label>
                    </div>
                     <div class="channel-option flex items-center p-2 hover:bg-gray-100 rounded">
                        <input id="modal-channel-yeepay" type="checkbox" value="yeepay" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                        <label for="modal-channel-yeepay" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">易宝支付</label>
                    </div>
                     <div class="channel-option flex items-center p-2 hover:bg-gray-100 rounded">
                        <input id="modal-channel-tonglian" type="checkbox" value="tonglian" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                        <label for="modal-channel-tonglian" class="ml-3 block text-sm font-medium text-gray-700 cursor-pointer flex-grow">通联支付</label>
                    </div>
                     <!-- Add more channels as needed, ensure value attribute is set -->
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="mt-6 flex justify-end space-x-3 border-t border-gray-200 pt-4">
                <button id="cancelAddChannel" class="btn btn-light">取消</button>
                <button id="confirmAddChannel" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>

     <!-- Configure Channel Condition Modal (New) -->
    <div id="configureChannelConditionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-lg p-6 mx-4">
            <!-- Modal Header -->
            <div class="flex justify-between items-center pb-3 border-b border-gray-200">
                <h3 id="configureModalTitle" class="text-lg font-semibold text-gray-900">配置独立条件</h3> <!-- Title will be set dynamically -->
                <button id="closeConfigureModal" class="text-gray-400 hover:text-gray-600">
                    <i class="material-icons">close</i>
                </button>
            </div>
            <!-- Modal Body -->
            <div class="mt-4">
                <p class="text-sm text-gray-600 mb-4">请为该渠道选择一个或多个预定义的条件模块。不选择则表示该渠道没有独立条件。</p>
                <!-- Condition Module List Container -->
                <div id="channelConditionModuleListContainer" class="max-h-72 overflow-y-auto space-y-2 pr-2 border border-gray-200 rounded-md p-3 bg-gray-50">
                    <!-- Checkbox list will be populated here by JS -->
                     <p class="text-center text-gray-500">正在加载条件模块...</p> <!-- Placeholder -->
                </div>
            </div>
            <!-- Modal Footer -->
            <div class="mt-6 flex justify-end space-x-3 border-t border-gray-200 pt-4">
                <button id="cancelConfigureModal" class="btn btn-light">取消</button>
                <button id="confirmConfigureModal" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>

</body>
</html> 