<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道路由管理 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 from layout_template.html */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }
         /* 确保字体应用 (移除 !important) */
        * {
             font-family: 'Noto Sans SC', sans-serif;
        }
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }
        .content {
            margin-left: 260px;
            transition: all 0.3s;
            padding-top: 4rem; /* Account for fixed header height (h-16) */
        }
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 4rem; /* h-16 */
        }
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        /* Ensure hover applies to the container div/a */
        .nav-item > a:hover, .nav-item > div:hover {
            background-color: var(--primary-light);
            border-radius: 0.5rem;
        }
        .nav-item.active {
            /* Using border-right as in template */
            background-color: var(--primary-light);
            border-right: 3px solid var(--primary-color);
        }
        /* Adjust padding for active item if needed to align text */
        .nav-item.active > a, .nav-item.active > div {
             padding-right: calc(1rem - 3px);
             color: var(--primary-color); /* Ensure text color changes */
        }
        .nav-item > a, .nav-item > div.menu-toggle {
             display: flex;
             align-items: center;
             padding: 0.75rem 1rem; /* Consistent padding py-3 px-4 */
             color: #4a5568; /* Default text color */
             font-weight: 500;
             cursor: pointer;
             border-radius: 0.5rem;
        }
        .nav-item > div.menu-toggle {
             justify-content: space-between;
        }
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
             padding-left: calc(1rem + 20px + 0.75rem); /* Indentation for subitems */
             /* Styles from previous attempt - keeping consistent with template */
        }
        .submenu.active {
            max-height: 500px;
        }
        .submenu li {
             margin-top: 0.25rem; /* Space between subitems */
        }
        .submenu a {
             display: block;
             padding: 0.5rem 0; /* Subitem padding */
             color: #718096;
             font-size: 0.875rem;
             transition: color 0.2s;
        }
        .submenu a:hover {
             color: var(--primary-color);
        }
        /* Active subitem link */
        .submenu li.active a {
            color: var(--primary-color);
            font-weight: 500;
        }

        /* Card Styles from template */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }
         /* Table Styles from template */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        .table-header {
             /* Use the gradient from template */
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
            font-weight: 500;
            font-size: 0.75rem;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        .table-header th {
            padding: 0.75rem 1rem; /* Consistent padding */
            text-align: left;
        }
         tbody tr {
             border-bottom: 1px solid var(--border-color);
         }
         tbody tr:last-child {
             border-bottom: none;
         }
         tbody tr:hover {
             background-color: #f9fafb; /* Light hover */
         }
         tbody td {
             padding: 0.75rem 1rem; /* Consistent padding */
             font-size: 0.875rem;
             color: #374151;
             white-space: nowrap;
         }

        /* Tag Styles from template */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #b48700; /* Adjusted template color */
        }
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* Form Styles from template */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.6rem 1rem !important; /* Adjusted padding */
            transition: all 0.3s !important;
            font-size: 0.875rem !important;
        }
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

         /* Button Styles from template */
        .btn {
            border-radius: 0.5rem;
            padding: 0.6rem 1.2rem;
            transition: all 0.3s;
            font-weight: 500;
            font-size: 0.875rem;
            border: none;
        }
         .btn i {
             margin-right: 0.3rem;
        }
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(59, 124, 254, 0.3);
        }
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
         .btn-secondary:hover {
            background-color: #5445c8;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(108, 92, 231, 0.3);
        }
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        .btn-success:hover {
             background-color: #00a884;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 184, 148, 0.3);
        }
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
         .btn-danger:hover {
             background-color: #c75c43;
             transform: translateY(-2px);
             box-shadow: 0 4px 10px rgba(225, 112, 85, 0.3);
        }
        .btn-light {
            background-color: #f1f5f9; /* Adjusted template color */
            color: #64748b;
        }
        .btn-light:hover {
            background-color: #e2e8f0;
             transform: translateY(-1px);
             box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

         /* Action Link Styles */
         .action-link {
             color: var(--primary-color);
             font-weight: 500;
             transition: color 0.2s;
             margin: 0 0.3rem;
             cursor: pointer;
             font-size: 0.875rem;
         }
         .action-link:hover {
             color: var(--primary-dark);
             text-decoration: underline;
         }
         .action-link-danger {
             color: var(--danger-color);
         }
          .action-link-danger:hover {
             color: #c75c43;
         }

          /* Pagination Styles (using btn-light from template) */
         .pagination .btn {
             padding: 0.5rem 0.8rem;
             font-size: 0.8rem;
             margin: 0 0.1rem;
         }

    </style>
</head>
<body>
    <!-- 页面头部 from layout_template.html -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold text-white">收银台及渠道管理系统</h1> <!-- Adjusted text color -->
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40 border border-gray-100">
                     <!-- User menu content from template -->
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 from layout_template.html (modified for this page) -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center mr-3"></i>
                        <span>仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center mr-3"></i>
                        <span>数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                 <li class="nav-item">
                     <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center mr-3"></i>
                            <span>支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                 <!-- 条件筛选管理 -->
                 <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center mr-3"></i>
                            <span>条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                     <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                 <!-- 支付方式管理 -->
                 <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-credit-card w-5 h-5 text-center mr-3"></i>
                         <span>支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 (Active) -->
                <li class="nav-item active"> <!-- Parent item active -->
                     <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light"> <!-- Toggle active style -->
                         <div class="flex items-center">
                              <i class="fas fa-random w-5 h-5 text-center mr-3"></i> <!-- Corrected icon -->
                              <span>渠道路由管理</span>
                         </div>
                         <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300 transform rotate-180"></i> <!-- Icon rotated -->
                     </div>
                     <ul class="submenu pl-12 pr-4 active"> <!-- Submenu expanded -->
                         <li class="my-2 active"> <!-- Active subitem -->
                             <a href="channel_route_rules.html" class="block py-2 text-primary font-semibold"> <!-- Active link style -->
                                 路由规则
                             </a>
                         </li>
                         <li class="my-2">
                             <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                 版本管理
                             </a>
                         </li>
                     </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center mr-3"></i> <!-- Added mr-3 for consistency -->
                         <span>功能开关</span>
                     </a>
                 </li>

                 <!-- 商户号管理 -->
                 <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center mr-3"></i>
                        <span>商户号管理</span>
                    </a>
                </li>

                 <!-- 应用管理 -->
                 <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center mr-3"></i>
                        <span>应用管理</span>
                    </a>
                </li>

                 <!-- 渠道账号管理 -->
                 <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center mr-3"></i>
                            <span>渠道账号管理</span> <!-- Corrected text -->
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4"> <!-- Added submenu structure -->
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                     <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-history w-5 h-5 text-center mr-3"></i>
                         <span>操作日志</span>
                     </a>
                 </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="content">
        <!-- 页面内容 -->
        <main class="p-6">
            <!-- Breadcrumb/Page Title Area -->
            <div class="mb-6">
                <h2 class="text-2xl font-semibold text-gray-800">渠道路由管理</h2>
                <!-- Optional Breadcrumb:
                <nav class="text-sm text-gray-500 mt-1">
                    <a href="#" class="hover:text-primary">主页</a>
                    <span class="mx-1">/</span>
                    <span>渠道路由管理</span>
                </nav>
                -->
            </div>

            <div class="card">
                 <!-- Action Bar and Filters in separate sections inside card -->
                 <div class="p-5 border-b border-gray-200">
                     <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-700">规则列表</h3>
                        <a href="channel_route_rule_edit.html" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增规则
                        </a>
                    </div>
                 </div>

                 <!-- Filters -->
                 <div class="p-5 border-b border-gray-200 bg-gray-50">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                         <div>
                            <label for="ruleName" class="block text-xs font-medium text-gray-600 mb-1">规则名称</label>
                            <input type="text" id="ruleName" placeholder="输入规则名称" class="w-full">
                        </div>
                        <div>
                            <label for="channelSelect" class="block text-xs font-medium text-gray-600 mb-1">渠道</label>
                            <select id="channelSelect" class="w-full">
                                <option value="">所有渠道</option>
                                <option value="alipay">支付宝</option>
                                <option value="wechatpay">微信支付</option>
                                <option value="unionpay">银联商务</option>
                            </select>
                        </div>
                         <div>
                             <label for="statusSelect" class="block text-xs font-medium text-gray-600 mb-1">状态</label>
                             <select id="statusSelect" class="w-full">
                                <option value="">所有状态</option>
                                <option value="published">已发布</option>
                                <option value="unpublished">未发布</option>
                                <option value="archived">已归档</option>
                            </select>
                         </div>
                         <div class="flex space-x-2">
                            <button class="btn btn-primary w-full sm:w-auto">
                                <i class="fas fa-search"></i> 查询
                            </button>
                             <button class="btn btn-light w-full sm:w-auto">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                         </div>
                    </div>
                 </div>

                <!-- Rules Table -->
                <div class="overflow-x-auto">
                    <div class="table-container">
                        <table class="min-w-full">
                            <thead class="table-header">
                                <tr>
                                    <th>规则名称</th>
                                    <th>适用支付方式</th>
                                    <th>目标渠道数</th>
                                    <th>默认备选</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>最后修改时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white">
                                <!-- Placeholder Row 1 -->
                                <tr>
                                    <td>支付宝主路由规则</td>
                                    <td>支付宝</td>
                                    <td>3</td>
                                    <td>支付宝官方通道</td>
                                    <td>1</td>
                                    <td>
                                        <span class="tag tag-green">已发布</span>
                                    </td>
                                    <td>2024-07-28 10:30:00</td>
                                    <td>
                                        <a href="channel_route_rule_edit.html" class="action-link">编辑</a>
                                        <a href="#" class="action-link text-indigo-600">详情</a>
                                        <a href="#" class="action-link text-blue-600">版本</a>
                                        <a href="#" class="action-link text-purple-600">模拟</a>
                                        <a href="#" class="action-link action-link-danger">删除</a>
                                    </td>
                                </tr>
                                 <!-- Placeholder Row 2 -->
                                <tr>
                                    <td>微信支付备用规则</td>
                                    <td>微信支付</td>
                                    <td>2</td>
                                    <td>某银行聚合支付</td>
                                    <td>2</td>
                                    <td>
                                        <span class="tag tag-yellow">未发布</span>
                                    </td>
                                    <td>2024-07-27 15:00:00</td>
                                     <td>
                                        <a href="channel_route_rule_edit.html" class="action-link">编辑</a>
                                        <a href="#" class="action-link text-indigo-600">详情</a>
                                        <a href="#" class="action-link text-blue-600">版本</a>
                                        <a href="#" class="action-link text-purple-600">模拟</a>
                                        <a href="#" class="action-link action-link-danger">删除</a>
                                    </td>
                                </tr>
                                <!-- Placeholder Row 3 -->
                                 <tr>
                                    <td>银联大额支付规则</td>
                                    <td>银联支付</td>
                                    <td>1</td>
                                    <td>无</td>
                                    <td>3</td>
                                    <td>
                                        <span class="tag tag-green">已发布</span>
                                    </td>
                                    <td>2024-07-26 09:15:00</td>
                                     <td>
                                        <a href="channel_route_rule_edit.html" class="action-link">编辑</a>
                                        <a href="#" class="action-link text-indigo-600">详情</a>
                                        <a href="#" class="action-link text-blue-600">版本</a>
                                        <a href="#" class="action-link text-purple-600">模拟</a>
                                        <a href="#" class="action-link action-link-danger">删除</a>
                                    </td>
                                </tr>
                                <!-- No data row example -->
                                <!--
                                <tr>
                                    <td colspan="8" class="text-center text-gray-500 py-10">
                                        <i class="fas fa-box-open text-4xl text-gray-400 mb-3"></i><br>
                                        暂无渠道路由规则
                                    </td>
                                </tr>
                                -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="p-5 flex justify-between items-center border-t border-gray-200">
                    <span class="text-sm text-gray-600">
                        共 <span class="font-semibold">3</span> 条记录， 第 <span class="font-semibold">1</span> / <span class="font-semibold">1</span> 页
                    </span>
                    <div class="pagination inline-flex items-center space-x-1">
                        <button class="btn btn-light" disabled>
                           <i class="fas fa-chevron-left"></i>
                        </button>
                         <!-- Page numbers could go here -->
                         <button class="btn btn-light">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Sidebar Submenu Toggle from payment_scene.html
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function(e) {
                // Allow link navigation if the toggle itself is a link (optional)
                // e.preventDefault(); 
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                const parentNavItem = this.closest('.nav-item');

                // Close other open submenus first
                document.querySelectorAll('.nav-item.active .submenu.active').forEach(openSubmenu => {
                    const otherParent = openSubmenu.closest('.nav-item');
                    if (openSubmenu !== submenu) {
                        openSubmenu.classList.remove('active');
                        openSubmenu.style.maxHeight = '0';
                        const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                        if (otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        // Deactivate other parent nav-item only if it's not the current one we are opening
                        if(otherParent !== parentNavItem) {
                            otherParent.classList.remove('active');
                            // Reset style of the other menu toggle
                            const otherToggle = otherParent.querySelector('.menu-toggle');
                            if (otherToggle) {
                                otherToggle.classList.remove('text-primary', 'bg-primary-light');
                                otherToggle.classList.add('text-gray-700');
                            }
                        }
                    }
                });

                // Toggle current submenu
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    submenu.style.maxHeight = '0';
                    if(icon) icon.style.transform = 'rotate(0deg)';
                    // Optionally deactivate parent if clicking again closes it
                    // parentNavItem.classList.remove('active'); 
                    // this.classList.remove('text-primary', 'bg-primary-light');
                    // this.classList.add('text-gray-700');
                } else {
                    submenu.classList.add('active');
                    submenu.style.maxHeight = submenu.scrollHeight + 'px';
                    if(icon) icon.style.transform = 'rotate(180deg)';
                    // Activate parent item and toggle styles
                    parentNavItem.classList.add('active'); 
                    this.classList.remove('text-gray-700');
                    this.classList.add('text-primary', 'bg-primary-light');
                }
            });
        });

        // User Dropdown Toggle from payment_scene.html
        const userDropdownButton = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdownButton && userMenu) {
            userDropdownButton.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
            document.addEventListener('click', function(event) {
                if (!userDropdownButton.contains(event.target) && !userMenu.contains(event.target)) {
                    userMenu.classList.add('hidden');
                }
            });
        }

        // Active Menu Item Handling (logic based on payment_scene.html structure)
         document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname.split('/').pop() || 'channel_route_rules.html';

            // Clear previous active states
            document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
            document.querySelectorAll('.submenu.active').forEach(submenu => {
                 submenu.classList.remove('active');
                 submenu.style.maxHeight = '0';
                 const icon = submenu.previousElementSibling.querySelector('.submenu-icon');
                 if(icon) icon.style.transform = 'rotate(0deg)';
            });
             document.querySelectorAll('.menu-toggle.text-primary').forEach(toggle => {
                 toggle.classList.remove('text-primary', 'bg-primary-light');
                 toggle.classList.add('text-gray-700');
            });
            document.querySelectorAll('.submenu li.active').forEach(li => li.classList.remove('active'));
             document.querySelectorAll('.submenu a.font-semibold').forEach(a => a.classList.remove('font-semibold','text-primary'));


            // Find the target link for the current page
            const targetLink = document.querySelector(`.sidebar a[href="${currentPath}"]`);

            if (targetLink) {
                const navItem = targetLink.closest('.nav-item');
                const submenuLi = targetLink.closest('.submenu > li');

                if (navItem) {
                    navItem.classList.add('active');

                    // If the target link is inside a submenu
                    if (submenuLi) {
                        submenuLi.classList.add('active');
                        targetLink.classList.add('text-primary', 'font-semibold'); // Style active sublink

                        const submenu = submenuLi.closest('.submenu');
                        const toggle = submenu.previousElementSibling; // The .menu-toggle div

                        if (submenu && toggle && toggle.classList.contains('menu-toggle')) {
                            submenu.classList.add('active');
                            submenu.style.maxHeight = submenu.scrollHeight + 'px';
                            toggle.classList.remove('text-gray-700');
                            toggle.classList.add('text-primary', 'bg-primary-light'); // Style active toggle
                            const icon = toggle.querySelector('.submenu-icon');
                            if(icon) icon.style.transform = 'rotate(180deg)';
                        }
                    } else {
                         // If the target link is a direct link under nav-item (no submenu)
                         // Apply active style directly to the link's container (nav-item)
                         // The nav-item already has .active from above
                         // If the link itself needs specific styling when active:
                         // targetLink.classList.add('text-primary', 'bg-primary-light'); 
                    }
                } 
            }
        });

    </script>
</body>
</html> 
