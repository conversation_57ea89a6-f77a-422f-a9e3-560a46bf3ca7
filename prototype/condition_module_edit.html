<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑条件模块 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 条件规则构建器样式 */
        .condition-builder {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            background-color: #f9fafb;
        }
        
        .condition-group {
            border: 1px dashed #d1d5db;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
        }
        
        .condition-rule {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background-color: #f3f4f6;
        }
        
        .operator-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            min-width: 2rem;
            text-align: center;
        }
        
        .operator-badge.and {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .operator-badge.or {
            background-color: #fdecde;
            color: #ed8936;
        }

        /* 切换开关样式 */
        .relation-toggle.selected-and {
            background-color: #3b82f6;
            color: white;
        }
        
        .relation-toggle.selected-or {
            background-color: #f97316;
            color: white;
        }

        /* 修改卡片折叠面板相关样式 */
        .card-header {
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 1px solid #e5e7eb;
        }

        .card-header:hover {
            background-color: #f8fafc;
        }

        .card-header .expand-icon {
            transition: transform 0.3s;
        }

        .card-header.collapsed {
            border-bottom: none; /* 折叠时移除底部边框 */
        }

        .card-header.collapsed .expand-icon {
            transform: rotate(-90deg);
        }

        .card-body {
            max-height: 1000px;
            overflow: hidden;
            transition: all 0.3s ease;
            opacity: 1;
        }

        .card-body.collapsed {
            max-height: 0;
            padding-top: 0;
            padding-bottom: 0;
            opacity: 0;
            pointer-events: none; /* 防止点击已折叠内容 */
            margin: 0;
        }

        .condition-group {
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 0;
            margin-bottom: 1rem;
            background-color: #fff;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300" style="transform: rotate(180deg);"></i>
                    </div>
                    <ul class="submenu active pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-primary font-medium">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">编辑条件组合模块</h2>
                <p class="text-gray-500 mt-1">创建和编辑条件组合模块，通过组合基础规则构建复杂筛选条件</p>
            </div>
            <div class="flex space-x-2">
                <a href="condition_module.html" class="btn btn-light flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>返回模块列表
                </a>
            </div>
        </div>

        <!-- 基本信息 -->
        <div class="card p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">基本信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模块名称</label>
                    <input type="text" class="w-full" value="乘客角色条件" placeholder="输入模块名称">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模块编码</label>
                    <input type="text" class="w-full" value="PASSENGER_ROLE" placeholder="输入模块编码">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <div class="flex items-center space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" class="form-radio h-4 w-4 text-primary" name="status" value="ENABLED" checked>
                            <span class="ml-2">启用</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" class="form-radio h-4 w-4 text-primary" name="status" value="DISABLED">
                            <span class="ml-2">停用</span>
                        </label>
                    </div>
                </div>
                <div>
                    <!-- 留空以保持布局平衡 -->
                </div>
            </div>
            
            <div class="grid grid-cols-1 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模块描述</label>
                    <textarea rows="3" class="w-full" placeholder="请输入模块描述">用于判断当前用户是否为乘客角色，适用于乘客专属支付场景的筛选。</textarea>
                </div>
            </div>
        </div>
        
        <!-- 条件模块定义 -->
        <div class="card p-5 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800">条件内容定义</h3>
                <div class="flex space-x-2">
                    <button id="btn-save-template" class="btn btn-light text-sm">
                        <i class="fas fa-save mr-1"></i>保存为模板
                    </button>
                </div>
            </div>
            
            <!-- 提示信息 -->
            <div class="bg-blue-50 p-3 rounded-lg mb-6 text-sm text-blue-800">
                <div class="flex">
                    <i class="fas fa-info-circle mr-2 mt-0.5"></i>
                    <div>
                        <p>条件组合模块仅支持从预定义规则添加条件：</p>
                        <p class="pl-2 mt-1">
                            从<a href="condition_rule.html" class="underline">基础条件规则库</a>中选择已定义的规则
                        </p>
                        <p class="mt-1">预定义规则的优势：跨模块复用、统一管理、批量更新</p>
                    </div>
                </div>
            </div>
            
            <!-- 修改条件组关系部分为切换开关式设计 -->
            <div class="mb-6 border-b pb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <h4 class="text-md font-medium mr-4">条件组关系</h4>
                        <button id="btn-add-group" class="btn btn-primary text-sm">
                            <i class="fas fa-plus mr-1"></i>添加条件组
                        </button>
                    </div>
                    <div class="bg-gray-200 rounded-full p-1 flex w-72" id="group-relation-container">
                        <label class="w-1/2">
                            <input type="radio" class="hidden" name="groupRelation" value="AND" checked>
                            <div class="cursor-pointer py-2 px-4 rounded-full text-center text-sm font-medium relation-toggle" id="toggle-and">
                                <i class="fas fa-link mr-2"></i>满足所有条件组 (AND)
                            </div>
                        </label>
                        <label class="w-1/2">
                            <input type="radio" class="hidden" name="groupRelation" value="OR">
                            <div class="cursor-pointer py-2 px-4 rounded-full text-center text-sm font-medium relation-toggle" id="toggle-or">
                                <i class="fas fa-code-branch mr-2"></i>满足任一条件组 (OR)
                            </div>
                        </label>
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-3">选择条件组之间的关系，决定如何组合多个条件组的结果</p>
            </div>
            
            <!-- 条件组列表 - 初始为空 -->
            <div id="condition-groups" class="empty-placeholder text-center py-10">
                <div class="text-gray-400">
                    <i class="fas fa-layer-group text-5xl mb-4"></i>
                    <p class="text-lg">暂无条件组</p>
                    <p class="text-sm mt-2">点击上方"添加条件组"按钮创建新条件组</p>
                </div>
            </div>
            
            <!-- 预览和测试区域 -->
            <div class="mt-6 border-t pt-4">
                <div class="flex items-center justify-between mb-2" id="preview-test-header" style="cursor: pointer;" onclick="togglePreviewTest()">
                    <h4 class="text-md font-medium">预览和测试工具</h4>
                    <i class="fas fa-chevron-down text-gray-500" id="preview-test-icon"></i>
                </div>
                
                <div id="preview-test-content" style="display: none;">
                    <!-- 预览JSON -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <h5 class="text-sm font-medium">JSON预览</h5>
                            <button class="text-blue-500 hover:text-blue-700 text-sm">
                                <i class="fas fa-code mr-1"></i>格式化
                            </button>
                        </div>
                        <div class="bg-gray-100 p-3 rounded-lg overflow-auto max-h-60">
                            <pre class="text-xs"><code>{
  "conditionGroups": [],
  "groupRelation": "AND"
}</code></pre>
                        </div>
                    </div>
                    
                    <!-- 条件测试 -->
                    <div class="mb-3 pt-4 border-t">
                        <div class="flex justify-between items-center mb-2">
                            <h5 class="text-sm font-medium">条件测试</h5>
                            <button class="btn btn-primary btn-sm text-sm">
                                <i class="fas fa-play mr-1"></i>执行测试
                            </button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">测试数据</label>
                                <textarea rows="5" class="w-full text-xs">{
  "user": {
    "userId": "12345678",
    "userRole": "passenger"
  },
  "trip": {
    "tripType": "shared",
    "tripDistance": 65.3
  }
}</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">测试结果</label>
                                <div class="p-3 bg-green-50 border border-green-200 rounded-lg text-sm h-full flex flex-col">
                                    <div class="flex items-center text-green-700 mb-2">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        <span class="font-medium">测试通过</span>
                                    </div>
                                    <div class="text-xs text-gray-600">
                                        <div class="mb-1">- 条件组1 "乘客角色条件组": <span class="text-green-600">满足</span></div>
                                        <div class="mb-1">- 条件组2 "行程条件组": <span class="text-green-600">满足</span></div>
                                        <div class="mb-1">- 条件组关系: AND（满足所有条件组）</div>
                                        <div class="mt-2 pt-2 border-t border-dashed border-green-200">
                                            最终结果: <span class="text-green-600 font-medium">条件满足</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 按钮组 -->
        <div class="flex justify-end space-x-4 mt-6">
            <button class="btn btn-light px-6">取消</button>
            <button class="btn btn-primary px-6">保存草稿</button>
            <button class="btn-gradient px-6 py-2 rounded-lg">保存并启用</button>
        </div>
    </main>

    <script>
        // 修改卡片折叠面板展开收起函数
        function toggleCardBody(header) {
            const cardBody = header.nextElementSibling;
            const expandIcon = header.querySelector('.expand-icon');
            
            if (header.classList.contains('collapsed')) {
                // 展开
                header.classList.remove('collapsed');
                cardBody.classList.remove('collapsed');
                expandIcon.classList.replace('fa-chevron-right', 'fa-chevron-down');
                
                // 恢复内容区的padding
                setTimeout(() => {
                    cardBody.style.paddingTop = '1rem';
                    cardBody.style.paddingBottom = '1rem';
                    cardBody.style.paddingLeft = '1rem';
                    cardBody.style.paddingRight = '1rem';
                }, 50);
            } else {
                // 折叠
                header.classList.add('collapsed');
                cardBody.classList.add('collapsed');
                expandIcon.classList.replace('fa-chevron-down', 'fa-chevron-right');
                
                // 移除内容区的padding
                cardBody.style.paddingTop = '0';
                cardBody.style.paddingBottom = '0';
                cardBody.style.paddingLeft = '0';
                cardBody.style.paddingRight = '0';
            }
        }

        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 条件模块编辑相关功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化条件组关系切换开关
            initGroupRelationToggle();
            
            // 初始化条件分组折叠功能
            initConditionSectionToggle();
            
            // 添加条件组
            document.getElementById('btn-add-group').addEventListener('click', function() {
                const groupsContainer = document.getElementById('condition-groups');
                
                // 检查是否为首次添加(有占位符)
                if (groupsContainer.classList.contains('empty-placeholder')) {
                    // 清空占位内容
                    groupsContainer.innerHTML = '';
                    groupsContainer.classList.remove('empty-placeholder', 'text-center', 'py-10');
                }
                
                const newGroupId = 'g' + (groupsContainer.querySelectorAll('.condition-group').length + 1);
                const newGroupNum = groupsContainer.querySelectorAll('.condition-group').length + 1;
                
                const newGroup = document.createElement('div');
                newGroup.className = 'condition-group mb-6';
                newGroup.dataset.groupId = newGroupId;
                
                newGroup.innerHTML = `
                    <div class="card-header flex justify-between items-center p-4 border-b" onclick="toggleCardBody(this)">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-down expand-icon mr-2 text-gray-500"></i>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-2">组 ${newGroupNum}</span>
                            <input type="text" class="border-b border-dashed border-gray-300 bg-transparent px-2 py-1 text-sm" value="新条件组" placeholder="条件组名称" onclick="event.stopPropagation()">
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-500">0 个规则</span>
                            <div class="flex space-x-2 ml-4">
                                <button class="text-gray-500 hover:text-gray-700 btn-copy-group" onclick="event.stopPropagation()">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="text-red-500 hover:text-red-700 btn-delete-group" onclick="event.stopPropagation()">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <h5 class="text-sm font-medium mb-2">组内条件关系</h5>
                            <div class="flex items-center space-x-4 text-sm">
                                <label class="inline-flex items-center">
                                    <input type="radio" class="form-radio h-4 w-4 text-primary" name="conditionRelation_${newGroupId}" value="AND" checked>
                                    <span class="ml-2">满足所有条件 (AND)</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" class="form-radio h-4 w-4 text-primary" name="conditionRelation_${newGroupId}" value="OR">
                                    <span class="ml-2">满足任一条件 (OR)</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="conditions-list mb-3">
                        </div>
                        
                        <div class="flex space-x-2 mb-3">
                            <button class="btn btn-primary btn-sm text-sm btn-add-predefined-rule w-full">
                                <i class="fas fa-list-check mr-1"></i>添加预定义规则
                            </button>
                        </div>
                    </div>
                `;
                
                groupsContainer.appendChild(newGroup);
                bindGroupEvents(newGroup);
                
                // 更新规则计数
                updateRuleCount(newGroup);
                
                // 更新JSON预览
                updateJsonPreview();
            });
            
            // 保存为模板按钮
            document.getElementById('btn-save-template').addEventListener('click', function() {
                showSaveTemplateModal();
            });
            
            // 绑定已有条件组的事件
            document.querySelectorAll('.condition-group').forEach(group => {
                bindGroupEvents(group);
                // 初始化更新规则计数
                updateRuleCount(group);
            });
            
            // 初始化条件按类型分组
            initConditionGroups();
            
            // 为条件组关系单选按钮添加change事件
            document.querySelectorAll('input[name="groupRelation"]').forEach(radio => {
                radio.addEventListener('change', updateJsonPreview);
            });
            
            // 初始调用一次更新JSON预览
            updateJsonPreview();
        });
        
        // 初始化条件组函数 - 将现有条件按类型分组
        function initConditionGroups() {
            // 初始化每个条件组的条件列表
            document.querySelectorAll('.condition-group').forEach(group => {
                // 获取条件列表容器
                const conditionsList = group.querySelector('.conditions-list');
                if (!conditionsList) return;
                
                // 获取所有条件项并按类型分类
                const items = conditionsList.querySelectorAll('.condition-item');
                if (items.length === 0) return; // 如果没有条件项，跳过
                
                const typeMap = new Map();
                
                items.forEach(item => {
                    // 获取条件类型（通过内容判断）
                    let typeKey, typeText, typeColor;
                    const conditionText = item.querySelector('.text-xs.text-gray-600').textContent;
                    
                    if (conditionText.includes('用户')) {
                        typeKey = 'user';
                        typeText = '用户属性';
                        typeColor = 'blue-500';
                    } else if (conditionText.includes('行程')) {
                        typeKey = 'trip';
                        typeText = '行程属性';
                        typeColor = 'green-500';
                    } else if (conditionText.includes('订单')) {
                        typeKey = 'order';
                        typeText = '订单属性';
                        typeColor = 'yellow-500';
                    } else if (conditionText.includes('当前时段')) {
                        typeKey = 'environment';
                        typeText = '环境条件';
                        typeColor = 'purple-500';
                    } else {
                        typeKey = 'other';
                        typeText = '其他条件';
                        typeColor = 'gray-500';
                    }
                    
                    // 创建类型分组映射
                    if (!typeMap.has(typeKey)) {
                        typeMap.set(typeKey, {
                            text: typeText,
                            color: typeColor,
                            items: []
                        });
                    }
                    
                    // 添加到对应类型
                    typeMap.get(typeKey).items.push(item);
                });
                
                // 跳过没有必要重新组织的情况，避免DOM重复操作
                if (typeMap.size <= 1) return;
                
                // 检查现有分组是否已经按类型组织 - 如果分组数量和类型数量一致，可能已经分组过了
                const existingSections = conditionsList.querySelectorAll('.condition-group-section');
                if (existingSections.length === typeMap.size) {
                    let alreadyGrouped = true;
                    existingSections.forEach(section => {
                        const typeKey = section.dataset.ruleType;
                        if (!typeMap.has(typeKey)) {
                            alreadyGrouped = false;
                        }
                    });
                    if (alreadyGrouped) return;
                }
                
                // 保存所有条件项的引用（克隆版本）
                const allConditions = [];
                typeMap.forEach(typeInfo => {
                    typeInfo.items.forEach(item => {
                        allConditions.push(item.cloneNode(true));
                    });
                });
                
                // 清空原有条件列表
                conditionsList.innerHTML = '';
                
                // 为每个类型创建分组，并添加条件
                typeMap.forEach((typeInfo, typeKey) => {
                    // 创建分组容器
                    const typeSection = document.createElement('div');
                    typeSection.className = 'condition-group-section mb-3';
                    typeSection.dataset.ruleType = typeKey;
                    
                    // 添加分组标题
                    typeSection.innerHTML = `
                        <div class="bg-gray-100 px-3 py-2 rounded-t flex justify-between items-center" style="cursor: pointer;" onclick="toggleConditionSection(this)">
                            <div class="flex items-center">
                                <span class="bg-${typeInfo.color} h-3 w-3 rounded-full mr-2"></span>
                                <span class="font-medium text-sm">${typeInfo.text}</span>
                            </div>
                            <span class="text-xs text-gray-500">▼</span>
                        </div>
                    `;
                    
                    // 添加所有该类型的条件
                    typeInfo.items.forEach((item, index) => {
                        // 复制原条件，并应用新样式
                        const newItem = item.cloneNode(true);
                        newItem.className = 'condition-item bg-gray-50 p-3 mb-2 border border-gray-200';
                        
                        if (index === 0) {
                            // 第一个条件有特殊样式
                            newItem.classList.add('rounded-b');
                            newItem.classList.add('border-t-0');
                        } else {
                            newItem.classList.add('rounded');
                        }
                        
                        // 添加到分组
                        typeSection.appendChild(newItem);
                        
                        // 重新绑定事件
                        bindConditionEvents(newItem, group);
                    });
                    
                    // 添加分组到条件列表
                    conditionsList.appendChild(typeSection);
                });
                
                // 更新条件计数
                updateRuleCount(group);
            });
        }

        // 条件组关系切换开关初始化
        function initGroupRelationToggle() {
            const andToggle = document.getElementById('toggle-and');
            const orToggle = document.getElementById('toggle-or');
            
            // 设置初始状态
            andToggle.classList.add('selected-and');
            
            // 监听单选按钮变更
            document.querySelectorAll('input[name="groupRelation"]').forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'AND') {
                        andToggle.classList.add('selected-and');
                        andToggle.classList.remove('selected-or');
                        orToggle.classList.remove('selected-or');
                        orToggle.classList.remove('selected-and');
                    } else {
                        andToggle.classList.remove('selected-and');
                        andToggle.classList.remove('selected-or');
                        orToggle.classList.add('selected-or');
                        orToggle.classList.remove('selected-and');
                    }
                    
                    // 更新JSON预览
                    updateJsonPreview();
                });
            });
        }

        // 绑定条件组内事件函数
        function bindGroupEvents(group) {
            // 删除条件组
            group.querySelector('.btn-delete-group').addEventListener('click', function(e) {
                e.stopPropagation(); // 防止触发折叠事件
                if (document.querySelectorAll('.condition-group').length > 1) {
                    group.remove();
                    // 更新组序号
                    updateGroupNumbers();
                    // 更新JSON预览
                    updateJsonPreview();
                } else {
                    alert('至少需要保留一个条件组');
                }
            });
            
            // 复制条件组
            group.querySelector('.btn-copy-group').addEventListener('click', function(e) {
                e.stopPropagation(); // 防止触发折叠事件
                const groupsContainer = document.getElementById('condition-groups');
                const newGroupId = 'g' + (groupsContainer.children.length + 1);
                const newGroupNum = groupsContainer.children.length + 1;
                
                const newGroup = group.cloneNode(true);
                newGroup.dataset.groupId = newGroupId;
                
                // 更新组号
                newGroup.querySelector('.bg-blue-100').innerText = `组 ${newGroupNum}`;
                
                // 更新单选按钮name
                newGroup.querySelectorAll('input[type="radio"]').forEach(radio => {
                    radio.name = `conditionRelation_${newGroupId}`;
                });
                
                groupsContainer.appendChild(newGroup);
                bindGroupEvents(newGroup);
                
                // 更新规则计数
                updateRuleCount(newGroup);
                
                // 更新JSON预览
                updateJsonPreview();
            });
            
            // 添加预定义规则
            group.querySelector('.btn-add-predefined-rule').addEventListener('click', function(e) {
                e.stopPropagation(); // 防止触发折叠事件
                showPredefinedRulesModal(group);
            });
            
            // 绑定已有条件的事件
            group.querySelectorAll('.condition-item').forEach(condition => {
                bindConditionEvents(condition, group);
            });
        }
        
        // 修改bindConditionEvents函数
        function bindConditionEvents(condition, group) {
            // 检查是否为表格行
            const isTableRow = condition.tagName === 'TR';
            
            // 删除条件
            if (isTableRow) {
                // 表格行形式
                condition.querySelector('.btn-delete-condition').addEventListener('click', function() {
                    const tbody = condition.parentElement;
                    const table = tbody.parentElement;
                    const parentSection = table.closest('.condition-group-section');
                    
                    condition.remove();
                    
                    // 如果表格中没有其他行，移除整个分组
                    if (tbody.querySelectorAll('tr').length === 0) {
                        parentSection.remove();
                    } else {
                        // 更新分组中的规则计数
                        const count = tbody.querySelectorAll('tr').length;
                        parentSection.querySelector('.font-medium.text-sm').textContent = 
                            parentSection.querySelector('.font-medium.text-sm').textContent.replace(/\(\d+个规则\)/, `(${count}个规则)`);
                    }
                    
                    // 更新规则计数
                    updateRuleCount(group);
                    
                    // 更新JSON预览
                    updateJsonPreview();
                });
            } else {
                // 旧版卡片形式 - 保留以兼容旧代码
                condition.querySelector('.btn-delete-condition').addEventListener('click', function() {
                    // 查找父分组
                    const parentSection = condition.closest('.condition-group-section');
                    condition.remove();
                    
                    // 如果分组中没有其他条件，移除整个分组
                    if (parentSection && parentSection.querySelectorAll('.condition-item').length === 0) {
                        parentSection.remove();
                    } else if (parentSection) {
                        // 更新分组中的规则计数
                        const count = parentSection.querySelectorAll('.condition-item').length;
                        parentSection.querySelector('.text-xs.text-gray-500').textContent = count + ' 个规则';
                    }
                    
                    // 更新规则计数
                    updateRuleCount(group);
                    
                    // 更新JSON预览
                    updateJsonPreview();
                });
            }
        }
        
        // 更新组序号
        function updateGroupNumbers() {
            document.querySelectorAll('.condition-group').forEach((group, index) => {
                group.querySelector('.bg-blue-100').innerText = `组 ${index + 1}`;
            });
        }
        
        // 更新规则计数
        function updateRuleCount(group) {
            // 计算规则数量 - 支持两种形式：表格行和条件项
            const tableRows = group.querySelectorAll('table tbody tr').length;
            const conditionItems = group.querySelectorAll('.condition-item').length;
            const conditionCount = tableRows + conditionItems;
            
            const countElement = group.querySelector('.card-header .text-xs.text-gray-500');
            countElement.textContent = `${conditionCount} 个规则`;
        }
        
        // 显示保存模板模态框
        function showSaveTemplateModal() {
            // 这里实现模态框逻辑
            alert('保存为模板功能 - 这里会弹出保存模板的对话框');
        }
        
        // 显示预定义规则选择模态框
        function showPredefinedRulesModal(group) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            modal.id = 'predefined-rules-modal';
            
            // 添加内容
            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl w-3/4 max-w-4xl max-h-3/4 flex flex-col">
                    <div class="px-6 py-4 border-b flex justify-between items-center">
                        <h3 class="text-lg font-bold">选择预定义条件规则</h3>
                        <button class="text-gray-500 hover:text-gray-700 btn-close-modal">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="p-6 overflow-auto flex-grow">
                        <div class="mb-4 flex items-center gap-4">
                            <div class="flex-grow">
                                <input type="text" class="w-full p-2 border rounded" placeholder="搜索条件规则...">
                            </div>
                            <div>
                                <select class="p-2 border rounded">
                                    <option value="all">全部类型</option>
                                    <option value="user">用户属性</option>
                                    <option value="trip">行程属性</option>
                                    <option value="order">订单属性</option>
                                    <option value="env">环境条件</option>
                                </select>
                            </div>
                        </div>
                        <table class="w-full border-collapse">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="p-2 text-left">选择</th>
                                    <th class="p-2 text-left">规则名称</th>
                                    <th class="p-2 text-left">规则类型</th>
                                    <th class="p-2 text-left">规则内容</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="p-2">
                                        <input type="checkbox" name="rule_select" value="CR001">
                                    </td>
                                    <td class="p-2">高级会员规则</td>
                                    <td class="p-2">用户属性</td>
                                    <td class="p-2">用户等级 > 3</td>
                                </tr>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="p-2">
                                        <input type="checkbox" name="rule_select" value="CR002">
                                    </td>
                                    <td class="p-2">乘客角色规则</td>
                                    <td class="p-2">用户属性</td>
                                    <td class="p-2">用户角色 = passenger</td>
                                </tr>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="p-2">
                                        <input type="checkbox" name="rule_select" value="CR003">
                                    </td>
                                    <td class="p-2">长途行程规则</td>
                                    <td class="p-2">行程属性</td>
                                    <td class="p-2">行程距离 > 100</td>
                                </tr>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="p-2">
                                        <input type="checkbox" name="rule_select" value="CR004">
                                    </td>
                                    <td class="p-2">新用户规则</td>
                                    <td class="p-2">用户属性</td>
                                    <td class="p-2">注册时间 < 30天</td>
                                </tr>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="p-2">
                                        <input type="checkbox" name="rule_select" value="CR005">
                                    </td>
                                    <td class="p-2">高峰期规则</td>
                                    <td class="p-2">环境条件</td>
                                    <td class="p-2">当前时段 IN [早高峰,晚高峰]</td>
                                </tr>
                                <tr class="border-b hover:bg-gray-50">
                                    <td class="p-2">
                                        <input type="checkbox" name="rule_select" value="CR006">
                                    </td>
                                    <td class="p-2">共享行程规则</td>
                                    <td class="p-2">行程属性</td>
                                    <td class="p-2">行程类型 = shared</td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <!-- 分页控件 -->
                        <div class="flex justify-between items-center mt-4 text-sm">
                            <div class="text-gray-600">
                                显示 1-6，共 24 条
                            </div>
                            <div class="flex">
                                <select class="mr-2 p-1 border rounded text-sm">
                                    <option value="5">5条/页</option>
                                    <option value="10" selected>10条/页</option>
                                    <option value="20">20条/页</option>
                                </select>
                                <div class="flex rounded border">
                                    <button class="px-3 py-1 border-r disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button class="px-3 py-1 bg-blue-500 text-white">1</button>
                                    <button class="px-3 py-1 border-l border-r hover:bg-gray-100">2</button>
                                    <button class="px-3 py-1 border-r hover:bg-gray-100">3</button>
                                    <button class="px-3 py-1 hover:bg-gray-100">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 py-4 border-t flex justify-between items-center">
                        <div class="text-sm">
                            <span class="text-blue-600">已选择 <span class="font-medium">0</span> 项</span>
                            <button class="text-blue-600 hover:underline ml-2 select-all-btn">全选</button>
                            <button class="text-blue-600 hover:underline ml-2 deselect-all-btn">取消全选</button>
                        </div>
                        <div>
                            <button class="btn btn-light mr-2 btn-cancel-modal">取消</button>
                            <button class="btn btn-primary btn-confirm-add-rule">添加选中规则</button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
            
            // 绑定关闭事件
            modal.querySelector('.btn-close-modal').addEventListener('click', function() {
                modal.remove();
            });
            
            modal.querySelector('.btn-cancel-modal').addEventListener('click', function() {
                modal.remove();
            });

            // 绑定全选和取消全选事件
            modal.querySelectorAll('input[name="rule_select"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedCount(modal);
                });
            });

            // 更新已选择数量的函数
            function updateSelectedCount(modal) {
                const selectedCount = modal.querySelectorAll('input[name="rule_select"]:checked').length;
                modal.querySelector('.text-blue-600 .font-medium').textContent = selectedCount;
            }
            
            // 绑定确认添加事件
            modal.querySelector('.btn-confirm-add-rule').addEventListener('click', function() {
                const selectedRules = modal.querySelectorAll('input[name="rule_select"]:checked');
                if (selectedRules.length > 0) {
                    const conditionsList = group.querySelector('.conditions-list');
                    
                    // 按规则类型分组存储规则
                    const rulesByType = {};
                    
                    // 收集所有选中规则进行添加
                    selectedRules.forEach(rule => {
                        const ruleId = rule.value;
                        const ruleName = rule.closest('tr').cells[1].textContent;
                        const ruleType = rule.closest('tr').cells[2].textContent;
                        const ruleContent = rule.closest('tr').cells[3].textContent;
                        
                        if (!rulesByType[ruleType]) {
                            rulesByType[ruleType] = [];
                        }
                        
                        rulesByType[ruleType].push({
                            id: ruleId,
                            name: ruleName,
                            content: ruleContent
                        });
                    });
                    
                    // 为每种类型创建或更新分组，并添加规则
                    for (const [ruleType, rules] of Object.entries(rulesByType)) {
                        // 获取规则类型的唯一标识
                        const typeKey = ruleType.replace(/\s+/g, '').toLowerCase();
                        
                        // 检查是否已有此类型的分组
                        let typeSection = conditionsList.querySelector(`.condition-group-section[data-rule-type="${typeKey}"]`);
                        
                        if (!typeSection) {
                            // 创建新的类型分组
                            typeSection = document.createElement('div');
                            typeSection.className = 'condition-group-section mb-3';
                            typeSection.dataset.ruleType = typeKey;
                            
                            // 设置不同类型的颜色
                            let typeColor = 'blue-500';
                            if (ruleType.includes('用户')) typeColor = 'blue-500';
                            else if (ruleType.includes('行程')) typeColor = 'green-500';
                            else if (ruleType.includes('订单')) typeColor = 'yellow-500';
                            else if (ruleType.includes('环境')) typeColor = 'purple-500';
                            else if (ruleType.includes('支付')) typeColor = 'red-500';
                            
                            // 使用新的表格结构
                            typeSection.innerHTML = `
                                <div class="bg-gray-100 px-3 py-2 rounded-t flex justify-between items-center" style="cursor: pointer;" onclick="toggleConditionSection(this)">
                                    <div class="flex items-center">
                                        <span class="bg-${typeColor} h-3 w-3 rounded-full mr-2"></span>
                                        <span class="font-medium text-sm">${ruleType} (${rules.length}个规则)</span>
                                    </div>
                                    <span class="text-xs text-gray-500">▼</span>
                                </div>
                                <div class="p-3 border border-gray-200 border-t-0 rounded-b">
                                    <table class="w-full">
                                        <thead>
                                            <tr>
                                                <th class="text-left text-sm font-medium text-gray-700">规则名称</th>
                                                <th class="text-left text-sm font-medium text-gray-700">条件表达式</th>
                                                <th class="text-left text-sm font-medium text-gray-700">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            `;
                            
                            conditionsList.appendChild(typeSection);
                        } else {
                            // 更新已有分组的规则计数
                            const existingCount = typeSection.querySelectorAll('table tbody tr').length;
                            const newCount = existingCount + rules.length;
                            typeSection.querySelector('.font-medium.text-sm').textContent = 
                                typeSection.querySelector('.font-medium.text-sm').textContent.replace(/\(\d+个规则\)/, `(${newCount}个规则)`);
                        }
                        
                        // 找到表格的tbody
                        const tbody = typeSection.querySelector('table tbody');
                        
                        // 添加每条规则到表格中
                        rules.forEach((rule, index) => {
                            // 创建新的表格行
                            const tr = document.createElement('tr');
                            
                            // 如果不是最后一个，添加底部边框
                            if (index < rules.length - 1 || tbody.children.length > 0) {
                                tr.className = 'border-b';
                            }
                            
                            tr.innerHTML = `
                                <td class="py-3">
                                    <div class="flex items-center">
                                        <span class="text-blue-500 font-medium mr-2">${rule.id}</span>
                                        <span class="text-gray-700">${rule.name}</span>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="text-gray-700">${rule.content}</span>
                                </td>
                                <td class="py-3">
                                    <div class="flex space-x-2">
                                        <button class="text-red-500 hover:text-red-700 btn-delete-condition">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            `;
                            
                            tbody.appendChild(tr);
                            bindConditionEvents(tr, group);
                        });
                    }
                    
                    // 更新规则计数
                    updateRuleCount(group);
                    
                    // 更新JSON预览
                    updateJsonPreview();
                    
                    // 关闭模态框
                    modal.remove();
                } else {
                    alert('请至少选择一个规则');
                }
            });

            // 实现分页功能的基本绑定
            modal.querySelectorAll('.flex.rounded.border button').forEach(button => {
                if (!button.disabled) {
                    button.addEventListener('click', function() {
                        // 移除所有按钮的活跃状态
                        modal.querySelectorAll('.flex.rounded.border button').forEach(btn => {
                            btn.classList.remove('bg-blue-500', 'text-white');
                        });
                            
                        // 为当前点击的按钮添加活跃状态
                        if (!this.querySelector('i')) { // 确保不是箭头按钮
                            this.classList.add('bg-blue-500', 'text-white');
                        }
                        
                        // 这里可以添加实际的分页数据加载逻辑
                        // 此处仅为示例，实际应用中需要根据后端数据结构调整
                    });
                }
            });
        }

        // 添加条件分组标题栏的折叠功能
        function toggleConditionSection(header) {
            const contentSection = header.nextElementSibling;
            const arrow = header.querySelector('.text-xs.text-gray-500');
            
            if (contentSection.style.display === 'none') {
                // 展开
                contentSection.style.display = 'block';
                arrow.textContent = '▼';
            } else {
                // 折叠
                contentSection.style.display = 'none';
                arrow.textContent = '▶';
            }
        }

        // 在initConditionGroups函数中或document.addEventListener('DOMContentLoaded')中添加
        function initConditionSectionToggle() {
            document.querySelectorAll('.condition-group-section .bg-gray-100').forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    toggleConditionSection(this);
                });
            });
        }

        // 添加用于更新JSON预览的函数
        function updateJsonPreview() {
            // 获取JSON预览容器
            const jsonPreviewContainer = document.querySelector('.bg-gray-100.p-3.rounded-lg.overflow-auto pre code');
            
            // 构建JSON对象
            const jsonData = {
                conditionGroups: [],
                groupRelation: document.querySelector('input[name="groupRelation"]:checked').value
            };
            
            // 获取所有条件组
            const conditionGroups = document.querySelectorAll('.condition-group');
            
            // 如果没有条件组，则返回空数组
            if (conditionGroups.length === 0) {
                jsonPreviewContainer.textContent = JSON.stringify(jsonData, null, 2);
                return;
            }
            
            // 遍历每个条件组
            conditionGroups.forEach(group => {
                const groupId = group.dataset.groupId;
                const groupName = group.querySelector('input[type="text"]').value;
                const groupRelation = group.querySelector('input[type="radio"]:checked').value;
                
                // 构建条件组对象
                const groupData = {
                    groupId: groupId,
                    groupName: groupName,
                    groupRelation: groupRelation,
                    conditions: []
                };
                
                // 获取所有条件，包括表格行和旧的条件项
                const tableRows = group.querySelectorAll('table tbody tr');
                const conditionItems = group.querySelectorAll('.condition-item');
                
                // 处理表格行形式的条件
                tableRows.forEach(row => {
                    const ruleId = row.querySelector('.text-blue-500.font-medium').textContent;
                    const ruleName = row.querySelector('.text-blue-500.font-medium').nextElementSibling.textContent;
                    const ruleContent = row.querySelectorAll('td')[1].textContent.trim();
                    
                    groupData.conditions.push({
                        ruleId: ruleId,
                        ruleName: ruleName,
                        description: ruleContent
                    });
                });
                
                // 处理旧格式的条件项（兼容）
                conditionItems.forEach(item => {
                    const ruleIdInput = item.querySelector('input[name="predefined_rule_id"]');
                    if (ruleIdInput) {
                        const ruleId = ruleIdInput.value;
                        const ruleName = item.querySelector('.font-medium').textContent;
                        const ruleContent = item.querySelector('.text-xs.text-gray-600').textContent;
                        
                        groupData.conditions.push({
                            ruleId: ruleId,
                            ruleName: ruleName,
                            description: ruleContent
                        });
                    }
                });
                
                // 添加条件组到JSON对象
                jsonData.conditionGroups.push(groupData);
            });
            
            // 更新JSON预览
            jsonPreviewContainer.textContent = JSON.stringify(jsonData, null, 2);
        }

        // 添加预览和测试区域的折叠/展开功能
        function togglePreviewTest() {
            const content = document.getElementById('preview-test-content');
            const icon = document.getElementById('preview-test-icon');
            
            if (content.style.display === 'none') {
                // 展开
                content.style.display = 'block';
                icon.classList.replace('fa-chevron-down', 'fa-chevron-up');
            } else {
                // 折叠
                content.style.display = 'none';
                icon.classList.replace('fa-chevron-up', 'fa-chevron-down');
            }
        }
    </script>
</body>
</html> 
