<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑条件模块 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 条件规则构建器样式 */
        .condition-builder {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            background-color: #f9fafb;
        }
        
        .condition-group {
            border: 1px dashed #d1d5db;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
        }
        
        .condition-rule {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background-color: #f3f4f6;
        }
        
        .operator-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            min-width: 2rem;
            text-align: center;
        }
        
        .operator-badge.and {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .operator-badge.or {
            background-color: #fdecde;
            color: #ed8936;
        }

        /* 添加卡片折叠面板相关样式 */
        .card-header {
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 1px solid #e5e7eb;
        }

        .card-header:hover {
            background-color: #f8fafc;
        }

        .card-header .expand-icon {
            transition: transform 0.3s;
        }

        .card-header.collapsed {
            border-bottom: none; /* 折叠时移除底部边框 */
        }

        .card-header.collapsed .expand-icon {
            transform: rotate(-90deg);
        }

        .card-body {
            max-height: 1000px;
            overflow: hidden;
            transition: all 0.3s ease;
            opacity: 1;
        }

        .card-body.collapsed {
            max-height: 0;
            padding-top: 0;
            padding-bottom: 0;
            opacity: 0;
            pointer-events: none; /* 防止点击已折叠内容 */
            margin: 0;
        }

        .condition-group {
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 0;
            margin-bottom: 1rem;
            background-color: #fff;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300" style="transform: rotate(180deg);"></i>
                    </div>
                    <ul class="submenu active pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-primary font-medium">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                        
                    </ul>
                </li>
                
                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">查看条件模块</h2>
                <p class="text-gray-500 mt-1">查看条件模块配置详情</p>
            </div>
            <div class="flex space-x-2">
                <a href="condition_module.html" class="btn btn-light flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>返回模块列表
                </a>
                <a href="condition_module_edit.html" class="btn btn-primary flex items-center">
                    <i class="fas fa-edit mr-2"></i>编辑模块
                </a>
            </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="card p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">基本信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模块名称</label>
                    <div class="p-2 bg-gray-50 rounded">乘客角色条件</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模块编码</label>
                    <div class="p-2 bg-gray-50 rounded">PASSENGER_ROLE</div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">条件类型</label>
                    <div class="p-2 bg-gray-50 rounded">用户属性</div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <div class="p-2 bg-gray-50 rounded">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>启用
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">模块描述</label>
                    <div class="p-2 bg-gray-50 rounded">用于判断当前用户是否为乘客角色，适用于乘客专属支付场景的筛选。</div>
                </div>
            </div>
        </div>
        
        <!-- 条件模块定义 -->
        <div class="card p-5 mb-6">
            <div class="mb-4">
                <h3 class="text-lg font-bold text-gray-800">条件内容定义</h3>
            </div>
            
            <!-- 条件组间关系 -->
            <div class="mb-6 border-b pb-4">
                <h4 class="text-md font-medium mb-2">条件组关系</h4>
                <div class="p-2 bg-gray-50 rounded">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <i class="fas fa-link mr-1"></i>满足所有条件组 (AND)
                    </span>
                </div>
            </div>
            
            <!-- 条件组列表 -->
            <div id="condition-groups">
                <!-- 条件组 1 -->
                <div class="condition-group mb-6" data-group-id="g1">
                    <div class="card-header flex justify-between items-center p-4 border-b" onclick="toggleCardBody(this)">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-down expand-icon mr-2 text-gray-500"></i>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-2">组 1</span>
                            <span class="text-sm font-medium">乘客角色条件组</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-500">1 个规则</span>
                        </div>
                    </div>
                    
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <h5 class="text-sm font-medium mb-2">组内条件关系</h5>
                            <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-link mr-1"></i>满足所有条件 (AND)
                            </div>
                        </div>
                        
                        <!-- 条件列表 -->
                        <div class="conditions-list mb-3">
                            <!-- 修改为与编辑页面相同的表格结构 -->
                            <div class="condition-group-section mb-3" data-rule-type="user">
                                <div class="bg-gray-100 px-3 py-2 rounded-t flex justify-between items-center" style="cursor: pointer;" onclick="toggleConditionSection(this)">
                                    <div class="flex items-center">
                                        <span class="bg-blue-500 h-3 w-3 rounded-full mr-2"></span>
                                        <span class="font-medium text-sm">用户属性 (1个规则)</span>
                                    </div>
                                    <span class="text-xs text-gray-500">▼</span>
                                </div>
                                <div class="p-3 border border-gray-200 border-t-0 rounded-b">
                                    <table class="w-full">
                                        <thead>
                                            <tr class="bg-gray-50">
                                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">条件表达式</th>
                                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="border-b last:border-b-0">
                                                <td class="py-3 px-4">
                                                    <div class="flex items-center">
                                                        <span class="text-blue-500 font-medium mr-2">CR002</span>
                                                        <span class="text-gray-700">乘客角色规则</span>
                                                    </div>
                                                </td>
                                                <td class="py-3 px-4">
                                                    <span class="text-gray-700">用户角色 = passenger</span>
                                                </td>
                                                <td class="py-3 px-4">
                                                    <span class="text-gray-400 text-xs">查看模式</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 条件组 2 -->
                <div class="condition-group mb-6" data-group-id="g2">
                    <div class="card-header flex justify-between items-center p-4 border-b" onclick="toggleCardBody(this)">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-down expand-icon mr-2 text-gray-500"></i>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-2">组 2</span>
                            <span class="text-sm font-medium">乘客行程类型条件组</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-500">1 个规则</span>
                        </div>
                    </div>
                    
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <h5 class="text-sm font-medium mb-2">组内条件关系</h5>
                            <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-link mr-1"></i>满足所有条件 (AND)
                            </div>
                        </div>
                        
                        <!-- 条件列表 -->
                        <div class="conditions-list mb-3">
                            <!-- 修改为与编辑页面相同的表格结构 -->
                            <div class="condition-group-section mb-3" data-rule-type="trip">
                                <div class="bg-gray-100 px-3 py-2 rounded-t flex justify-between items-center" style="cursor: pointer;" onclick="toggleConditionSection(this)">
                                    <div class="flex items-center">
                                        <span class="bg-green-500 h-3 w-3 rounded-full mr-2"></span>
                                        <span class="font-medium text-sm">行程属性 (1个规则)</span>
                                    </div>
                                    <span class="text-xs text-gray-500">▼</span>
                                </div>
                                <div class="p-3 border border-gray-200 border-t-0 rounded-b">
                                    <table class="w-full">
                                        <thead>
                                            <tr class="bg-gray-50">
                                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">条件表达式</th>
                                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="border-b last:border-b-0">
                                                <td class="py-3 px-4">
                                                    <div class="flex items-center">
                                                        <span class="text-green-500 font-medium mr-2">CR006</span>
                                                        <span class="text-gray-700">共享行程规则</span>
                                                    </div>
                                                </td>
                                                <td class="py-3 px-4">
                                                    <span class="text-gray-700">行程类型 = shared</span>
                                                </td>
                                                <td class="py-3 px-4">
                                                    <span class="text-gray-400 text-xs">查看模式</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 预览JSON -->
            <div class="mt-6 border-t pt-4">
                <div class="flex justify-between items-center mb-2">
                    <h4 class="text-md font-medium">JSON结构</h4>
                </div>
                <div class="bg-gray-100 p-3 rounded-lg overflow-auto max-h-60">
                    <pre class="text-xs"><code>{
  "conditionGroups": [
    {
      "groupId": "g1",
      "groupName": "乘客角色条件组",
      "conditions": [
        {
          "field": "userRole",
          "fieldName": "用户角色",
          "dimensionType": "USER",
          "operator": "=",
          "value": "passenger",
          "valueType": "STRING",
          "description": "用户角色为乘客"
        }
      ]
    },
    {
      "groupId": "g2",
      "groupName": "乘客行程类型条件组",
      "conditions": [
        {
          "field": "tripType",
          "fieldName": "行程类型",
          "dimensionType": "TRIP",
          "operator": "=",
          "value": "shared",
          "valueType": "STRING",
          "description": "行程类型为拼车"
        }
      ]
    }
  ],
  "groupRelation": "AND"
}</code></pre>
                </div>
            </div>
            
            <!-- 条件测试结果 -->
            <div class="mt-6 border-t pt-4">
                <div class="flex justify-between items-center mb-2">
                    <h4 class="text-md font-medium">最近测试结果</h4>
                    <span class="text-sm text-gray-500">测试时间: 2023-11-15 14:30:45</span>
                </div>
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg text-sm">
                    <div class="flex items-center text-green-700 mb-2">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span class="font-medium">测试通过</span>
                    </div>
                    <div class="text-xs text-gray-600">
                        <div class="mb-1">- 条件组1 "乘客角色条件组": <span class="text-green-600">满足</span></div>
                        <div class="mb-1">- 条件组2 "乘客行程类型条件组": <span class="text-green-600">满足</span></div>
                        <div class="mb-1">- 条件组关系: AND（满足所有条件组）</div>
                        <div class="mt-2 pt-2 border-t border-dashed border-green-200">
                            最终结果: <span class="text-green-600 font-medium">条件满足</span>
                        </div>
                    </div>
                </div>
                <div class="mt-3 grid grid-cols-1 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">测试数据</label>
                        <div class="bg-gray-100 p-3 rounded-lg overflow-auto">
                            <pre class="text-xs"><code>{
  "user": {
    "userId": "12345678",
    "userRole": "passenger"
  },
  "trip": {
    "tripType": "shared",
    "tripDistance": 65.3
  }
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 引用信息 -->
        <div class="card p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">引用情况</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">引用场景</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">引用类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">共享出行支付场景</td>
                            <td class="px-6 py-4 whitespace-nowrap">支付场景</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    启用中
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="#" class="text-blue-600 hover:text-blue-800">查看</a>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">乘客专享路由策略</td>
                            <td class="px-6 py-4 whitespace-nowrap">渠道路由</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    未启用
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="#" class="text-blue-600 hover:text-blue-800">查看</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 操作历史 -->
        <div class="card p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">操作历史</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作内容</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">2023-11-15 14:30:45</td>
                            <td class="px-6 py-4 whitespace-nowrap">admin</td>
                            <td class="px-6 py-4">执行条件测试</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">2023-11-10 09:15:22</td>
                            <td class="px-6 py-4 whitespace-nowrap">admin</td>
                            <td class="px-6 py-4">启用条件模块</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">2023-11-08 16:45:12</td>
                            <td class="px-6 py-4 whitespace-nowrap">admin</td>
                            <td class="px-6 py-4">创建条件模块</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 卡片折叠面板展开收起函数 - 移至全局作用域
        function toggleCardBody(header) {
            const cardBody = header.nextElementSibling;
            const expandIcon = header.querySelector('.expand-icon');
            
            if (header.classList.contains('collapsed')) {
                // 展开
                header.classList.remove('collapsed');
                cardBody.classList.remove('collapsed');
                expandIcon.classList.replace('fa-chevron-right', 'fa-chevron-down');
                
                // 恢复内容区的padding
                setTimeout(() => {
                    cardBody.style.paddingTop = '1rem';
                    cardBody.style.paddingBottom = '1rem';
                    cardBody.style.paddingLeft = '1rem';
                    cardBody.style.paddingRight = '1rem';
                }, 50);
            } else {
                // 折叠
                header.classList.add('collapsed');
                cardBody.classList.add('collapsed');
                expandIcon.classList.replace('fa-chevron-down', 'fa-chevron-right');
                
                // 移除内容区的padding
                cardBody.style.paddingTop = '0';
                cardBody.style.paddingBottom = '0';
                cardBody.style.paddingLeft = '0';
                cardBody.style.paddingRight = '0';
            }
        }

        // 条件模块编辑相关功能
        document.addEventListener('DOMContentLoaded', function() {
            // 添加条件组
            document.getElementById('btn-add-group').addEventListener('click', function() {
                const groupsContainer = document.getElementById('condition-groups');
                const newGroupId = 'g' + (groupsContainer.children.length + 1);
                const newGroupNum = groupsContainer.children.length + 1;
                
                const newGroup = document.createElement('div');
                newGroup.className = 'condition-group mb-8 border rounded-lg p-4 bg-white shadow-sm';
                newGroup.dataset.groupId = newGroupId;
                
                newGroup.innerHTML = `
                    <div class="flex justify-between items-center mb-3">
                        <div class="flex items-center">
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-2">组 ${newGroupNum}</span>
                            <input type="text" class="border-b border-dashed border-gray-300 bg-transparent px-2 py-1 text-sm" value="新条件组" placeholder="条件组名称">
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-gray-500 hover:text-gray-700 btn-copy-group">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="text-red-500 hover:text-red-700 btn-delete-group">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h5 class="text-sm font-medium mb-2">组内条件关系</h5>
                        <div class="flex items-center space-x-4 text-sm">
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-4 w-4 text-primary" name="conditionRelation_${newGroupId}" value="AND" checked>
                                <span class="ml-2">满足所有条件 (AND)</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" class="form-radio h-4 w-4 text-primary" name="conditionRelation_${newGroupId}" value="OR">
                                <span class="ml-2">满足任一条件 (OR)</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="conditions-list mb-3">
                    </div>
                    
                    <div class="flex space-x-2 mb-3">
                        <button class="btn btn-light btn-sm text-sm btn-add-condition flex-1">
                            <i class="fas fa-plus mr-1"></i>添加条件
                        </button>
                        <button class="btn btn-primary btn-sm text-sm btn-add-predefined-rule flex-1">
                            <i class="fas fa-list-check mr-1"></i>添加预定义规则
                        </button>
                    </div>
                `;
                
                groupsContainer.appendChild(newGroup);
                bindGroupEvents(newGroup);
            });
            
            // 保存为模板按钮
            document.getElementById('btn-save-template').addEventListener('click', function() {
                showSaveTemplateModal();
            });
            
            // 绑定已有条件组的事件
            document.querySelectorAll('.condition-group').forEach(group => {
                bindGroupEvents(group);
            });
            
            // 绑定条件组内事件函数
            function bindGroupEvents(group) {
                // 删除条件组
                group.querySelector('.btn-delete-group').addEventListener('click', function() {
                    if (document.querySelectorAll('.condition-group').length > 1) {
                        group.remove();
                        // 更新组序号
                        updateGroupNumbers();
                    } else {
                        alert('至少需要保留一个条件组');
                    }
                });
                
                // 复制条件组
                group.querySelector('.btn-copy-group').addEventListener('click', function() {
                    const groupsContainer = document.getElementById('condition-groups');
                    const newGroupId = 'g' + (groupsContainer.children.length + 1);
                    const newGroupNum = groupsContainer.children.length + 1;
                    
                    const newGroup = group.cloneNode(true);
                    newGroup.dataset.groupId = newGroupId;
                    
                    // 更新组号
                    newGroup.querySelector('.bg-blue-100').innerText = `组 ${newGroupNum}`;
                    
                    // 更新单选按钮name
                    newGroup.querySelectorAll('input[type="radio"]').forEach(radio => {
                        radio.name = `conditionRelation_${newGroupId}`;
                    });
                    
                    groupsContainer.appendChild(newGroup);
                    bindGroupEvents(newGroup);
                });
                
                // 添加条件
                group.querySelector('.btn-add-condition').addEventListener('click', function() {
                    const conditionsList = group.querySelector('.conditions-list');
                    const newCondition = document.createElement('div');
                    newCondition.className = 'condition-item bg-gray-50 rounded p-3 mb-2';
                    
                    newCondition.innerHTML = `
                        <div class="grid grid-cols-12 gap-2">
                            <div class="col-span-3">
                                <label class="block text-xs font-medium text-gray-700 mb-1">字段</label>
                                <select class="w-full text-sm">
                                    <option value="userRole">用户角色</option>
                                    <option value="userLevel">用户等级</option>
                                    <option value="userTag">用户标签</option>
                                    <option value="registerDays">注册天数</option>
                                    <option value="tripType">行程类型</option>
                                    <option value="tripDistance">行程距离</option>
                                    <option value="hasHighway">包含高速</option>
                                </select>
                            </div>
                            <div class="col-span-2">
                                <label class="block text-xs font-medium text-gray-700 mb-1">维度</label>
                                <select class="w-full text-sm">
                                    <option value="USER" selected>用户属性</option>
                                    <option value="TRIP">行程属性</option>
                                    <option value="ORDER">订单属性</option>
                                    <option value="ENVIRONMENT">环境条件</option>
                                    <option value="PAYMENT">支付相关</option>
                                </select>
                            </div>
                            <div class="col-span-2">
                                <label class="block text-xs font-medium text-gray-700 mb-1">操作符</label>
                                <select class="w-full text-sm">
                                    <option value="=" selected>=</option>
                                    <option value="!=">!=</option>
                                    <option value=">">></option>
                                    <option value=">=">>=</option>
                                    <option value="<"><</option>
                                    <option value="<="><=</option>
                                    <option value="IN">IN</option>
                                    <option value="NOT_IN">NOT IN</option>
                                </select>
                            </div>
                            <div class="col-span-3">
                                <label class="block text-xs font-medium text-gray-700 mb-1">值</label>
                                <input type="text" class="w-full text-sm" value="">
                            </div>
                            <div class="col-span-2 flex items-end pb-1 space-x-1">
                                <button class="text-red-500 hover:text-red-700 btn-delete-condition">
                                    <i class="fas fa-times"></i>
                                </button>
                                <button class="text-gray-500 hover:text-gray-700 btn-duplicate-condition">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    
                    conditionsList.appendChild(newCondition);
                    bindConditionEvents(newCondition);
                });
                
                // 添加预定义规则
                group.querySelector('.btn-add-predefined-rule').addEventListener('click', function() {
                    showPredefinedRulesModal(group);
                });
                
                // 绑定已有条件的事件
                group.querySelectorAll('.condition-item').forEach(condition => {
                    bindConditionEvents(condition);
                });
            }
            
            // 绑定条件项事件
            function bindConditionEvents(condition) {
                // 删除条件
                condition.querySelector('.btn-delete-condition').addEventListener('click', function() {
                    condition.remove();
                });
                
                // 复制条件
                condition.querySelector('.btn-duplicate-condition').addEventListener('click', function() {
                    const newCondition = condition.cloneNode(true);
                    condition.parentNode.appendChild(newCondition);
                    bindConditionEvents(newCondition);
                });
            }
            
            // 更新组序号
            function updateGroupNumbers() {
                document.querySelectorAll('.condition-group').forEach((group, index) => {
                    group.querySelector('.bg-blue-100').innerText = `组 ${index + 1}`;
                });
            }
            
            // 显示保存模板模态框
            function showSaveTemplateModal() {
                // 这里实现模态框逻辑
                alert('保存为模板功能 - 这里会弹出保存模板的对话框');
            }
            
            // 显示预定义规则选择模态框
            function showPredefinedRulesModal(group) {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                modal.id = 'predefined-rules-modal';
                
                // 添加内容
                modal.innerHTML = `
                    <div class="bg-white rounded-lg shadow-xl w-3/4 max-w-4xl max-h-3/4 flex flex-col">
                        <div class="px-6 py-4 border-b flex justify-between items-center">
                            <h3 class="text-lg font-bold">选择预定义条件规则</h3>
                            <button class="text-gray-500 hover:text-gray-700 btn-close-modal">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="p-6 overflow-auto flex-grow">
                            <div class="mb-4">
                                <input type="text" class="w-full p-2 border rounded" placeholder="搜索条件规则...">
                            </div>
                            <table class="w-full border-collapse">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="p-2 text-left">选择</th>
                                        <th class="p-2 text-left">规则名称</th>
                                        <th class="p-2 text-left">规则类型</th>
                                        <th class="p-2 text-left">规则内容</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="p-2">
                                            <input type="radio" name="rule_select" value="CR001">
                                        </td>
                                        <td class="p-2">高级会员规则</td>
                                        <td class="p-2">用户属性</td>
                                        <td class="p-2">用户等级 > 3</td>
                                    </tr>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="p-2">
                                            <input type="radio" name="rule_select" value="CR002">
                                        </td>
                                        <td class="p-2">乘客角色规则</td>
                                        <td class="p-2">用户属性</td>
                                        <td class="p-2">用户角色 = passenger</td>
                                    </tr>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="p-2">
                                            <input type="radio" name="rule_select" value="CR003">
                                        </td>
                                        <td class="p-2">长途行程规则</td>
                                        <td class="p-2">行程属性</td>
                                        <td class="p-2">行程距离 > 100</td>
                                    </tr>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="p-2">
                                            <input type="radio" name="rule_select" value="CR004">
                                        </td>
                                        <td class="p-2">新用户规则</td>
                                        <td class="p-2">用户属性</td>
                                        <td class="p-2">注册时间 < 30天</td>
                                    </tr>
                                    <tr class="border-b hover:bg-gray-50">
                                        <td class="p-2">
                                            <input type="radio" name="rule_select" value="CR005">
                                        </td>
                                        <td class="p-2">高峰期规则</td>
                                        <td class="p-2">环境条件</td>
                                        <td class="p-2">当前时段 IN [早高峰,晚高峰]</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-4 border-t flex justify-end">
                            <button class="btn btn-light mr-2 btn-cancel-modal">取消</button>
                            <button class="btn btn-primary btn-confirm-add-rule">添加选中规则</button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(modal);
                
                // 绑定关闭事件
                modal.querySelector('.btn-close-modal').addEventListener('click', function() {
                    modal.remove();
                });
                
                modal.querySelector('.btn-cancel-modal').addEventListener('click', function() {
                    modal.remove();
                });
                
                // 绑定确认添加事件
                modal.querySelector('.btn-confirm-add-rule').addEventListener('click', function() {
                    const selectedRule = modal.querySelector('input[name="rule_select"]:checked');
                    if (selectedRule) {
                        const ruleId = selectedRule.value;
                        const ruleName = selectedRule.closest('tr').cells[1].textContent;
                        const ruleContent = selectedRule.closest('tr').cells[3].textContent;
                        
                        // 添加预定义规则到条件组
                        const conditionsList = group.querySelector('.conditions-list');
                        const newCondition = document.createElement('div');
                        newCondition.className = 'condition-item bg-gray-50 rounded p-3 mb-2';
                        
                        newCondition.innerHTML = `
                            <div class="grid grid-cols-12 gap-2">
                                <div class="col-span-10">
                                    <div class="flex items-center">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs mr-2">预定义规则</span>
                                        <span class="font-medium">${ruleName}</span>
                                        <span class="text-gray-500 ml-2 text-xs">${ruleId}</span>
                                    </div>
                                    <div class="mt-1 text-xs text-gray-600">${ruleContent}</div>
                                    <input type="hidden" name="predefined_rule_id" value="${ruleId}">
                                </div>
                                <div class="col-span-2 flex items-center justify-end">
                                    <button class="text-red-500 hover:text-red-700 btn-delete-condition">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        `;
                        
                        conditionsList.appendChild(newCondition);
                        bindConditionEvents(newCondition);
                        
                        // 关闭模态框
                        modal.remove();
                    } else {
                        alert('请选择一个规则');
                    }
                });
            }

            // 添加条件分组标题栏的折叠功能
            function toggleConditionSection(header) {
                const contentSection = header.nextElementSibling;
                const arrow = header.querySelector('.text-xs.text-gray-500');
                
                if (contentSection.style.display === 'none') {
                    // 展开
                    contentSection.style.display = 'block';
                    arrow.textContent = '▼';
                } else {
                    // 折叠
                    contentSection.style.display = 'none';
                    arrow.textContent = '▶';
                }
            }

            // 在initConditionGroups函数中或document.addEventListener('DOMContentLoaded')中添加
            function initConditionSectionToggle() {
                document.querySelectorAll('.condition-group-section .bg-gray-100').forEach(header => {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', function() {
                        toggleConditionSection(this);
                    });
                });
            }

            // 初始化已有条件组，添加折叠功能
            document.querySelectorAll('.condition-group').forEach(group => {
                // 更新规则计数
                updateRuleCount(group);
            });
            
            // 更新规则计数函数
            function updateRuleCount(group) {
                const conditionCount = group.querySelectorAll('.condition-item').length;
                const countElement = group.querySelector('.card-header .text-xs.text-gray-500');
                if (countElement) {
                    countElement.textContent = `${conditionCount} 个规则`;
                }
            }

            // DOM加载完成后初始化条件分组的折叠功能
            initConditionSectionToggle();
        });
    </script>
</body>
</html> 
