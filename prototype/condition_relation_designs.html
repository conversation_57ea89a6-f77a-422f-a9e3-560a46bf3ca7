<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>条件组关系布局方案 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
        }
        
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

        /* 方案一：卡片选择器样式 */
        .group-relation-card.selected-and {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .group-relation-card.selected-or {
            border-color: #f97316;
            background-color: #fff7ed;
        }

        /* 方案二：切换开关样式 */
        .relation-toggle.selected-and {
            background-color: #3b82f6;
            color: white;
        }
        
        .relation-toggle.selected-or {
            background-color: #f97316;
            color: white;
        }

        /* 方案三：图示化逻辑关系样式 */
        .relation-diagram-card.selected-and {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .relation-diagram-card.selected-or {
            border-color: #f97316;
            background-color: #fff7ed;
        }

        /* 方案四：标签页样式 */
        .relation-tab.selected-and {
            background-color: #eff6ff;
            border-bottom: 3px solid #3b82f6;
        }
        
        .relation-tab.selected-or {
            background-color: #fff7ed;
            border-bottom: 3px solid #f97316;
        }

        /* 方案五：逻辑门样式 */
        .logic-gate-card.selected-and {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .logic-gate-card.selected-or {
            border-color: #f97316;
            background-color: #fff7ed;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统 - 布局方案选择</h1>
        </div>
        <div>
            <a href="condition_module_edit.html" class="text-white hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i>
                返回编辑页面
            </a>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4 active">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>
                
                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="#" class="block py-2 text-gray-400 hover:text-primary cursor-not-allowed">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="pt-20 pb-8 px-8">
        <div class="mb-6">
            <h2 class="text-2xl font-bold text-gray-800">条件组关系布局方案</h2>
            <p class="text-gray-500 mt-1">请选择您偏好的条件组关系布局设计，以下提供5种不同风格的设计方案</p>
        </div>

        <div class="mb-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 方案一：卡片式选择器 -->
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">方案一：卡片式选择器</h3>
                    <div class="mb-6 border-b pb-4">
                        <h4 class="text-md font-medium mb-3">条件组关系</h4>
                        <div class="flex space-x-4" id="design1-container">
                            <label class="flex-1 cursor-pointer">
                                <input type="radio" class="hidden design1-radio" name="groupRelation1" value="AND" checked>
                                <div class="border-2 border-gray-200 rounded-lg p-4 text-center hover:bg-gray-50 transition group-relation-card" id="card-and-1">
                                    <div class="flex justify-center mb-2">
                                        <div class="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-link text-blue-600"></i>
                                        </div>
                                    </div>
                                    <h5 class="font-medium">满足所有条件组</h5>
                                    <p class="text-xs text-gray-500 mt-1">AND 逻辑</p>
                                </div>
                            </label>
                            <label class="flex-1 cursor-pointer">
                                <input type="radio" class="hidden design1-radio" name="groupRelation1" value="OR">
                                <div class="border-2 border-gray-200 rounded-lg p-4 text-center hover:bg-gray-50 transition group-relation-card" id="card-or-1">
                                    <div class="flex justify-center mb-2">
                                        <div class="h-10 w-10 bg-orange-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-code-branch text-orange-600"></i>
                                        </div>
                                    </div>
                                    <h5 class="font-medium">满足任一条件组</h5>
                                    <p class="text-xs text-gray-500 mt-1">OR 逻辑</p>
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button class="btn btn-primary select-design" data-design="1">选择此方案</button>
                    </div>
                </div>

                <!-- 方案二：切换开关式设计 -->
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">方案二：切换开关式设计</h3>
                    <div class="mb-6 border-b pb-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-md font-medium">条件组关系</h4>
                            <div class="bg-gray-200 rounded-full p-1 flex w-72" id="design2-container">
                                <label class="w-1/2">
                                    <input type="radio" class="hidden design2-radio" name="groupRelation2" value="AND" checked>
                                    <div class="cursor-pointer py-2 px-4 rounded-full text-center text-sm font-medium relation-toggle" id="toggle-and-2">
                                        <i class="fas fa-link mr-2"></i>满足所有条件组 (AND)
                                    </div>
                                </label>
                                <label class="w-1/2">
                                    <input type="radio" class="hidden design2-radio" name="groupRelation2" value="OR">
                                    <div class="cursor-pointer py-2 px-4 rounded-full text-center text-sm font-medium relation-toggle" id="toggle-or-2">
                                        <i class="fas fa-code-branch mr-2"></i>满足任一条件组 (OR)
                                    </div>
                                </label>
                            </div>
                        </div>
                        <p class="text-sm text-gray-500 mt-3">选择条件组之间的关系，决定如何组合多个条件组的结果</p>
                    </div>
                    <div class="flex justify-end">
                        <button class="btn btn-primary select-design" data-design="2">选择此方案</button>
                    </div>
                </div>

                <!-- 方案三：图示化逻辑关系 -->
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">方案三：图示化逻辑关系</h3>
                    <div class="mb-6 border-b pb-4">
                        <h4 class="text-md font-medium mb-3">条件组关系</h4>
                        <div class="grid grid-cols-2 gap-4" id="design3-container">
                            <label class="cursor-pointer">
                                <input type="radio" class="hidden design3-radio" name="groupRelation3" value="AND" checked>
                                <div class="border-2 border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition relation-diagram-card" id="diagram-and-3">
                                    <div class="flex items-center mb-2">
                                        <div class="h-6 w-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mr-2">A</div>
                                        <span class="font-medium text-sm">AND</span>
                                        <div class="h-6 w-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-medium mx-2">B</div>
                                        <i class="fas fa-equals mx-2 text-gray-500"></i>
                                        <div class="px-2 py-1 rounded bg-green-100 text-green-800 text-xs">全部满足</div>
                                    </div>
                                    <div class="text-xs text-gray-600 bg-blue-50 p-2 rounded">
                                        所有条件组必须同时满足，任一条件组不满足则整体不满足
                                    </div>
                                </div>
                            </label>
                            <label class="cursor-pointer">
                                <input type="radio" class="hidden design3-radio" name="groupRelation3" value="OR">
                                <div class="border-2 border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition relation-diagram-card" id="diagram-or-3">
                                    <div class="flex items-center mb-2">
                                        <div class="h-6 w-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs font-medium mr-2">A</div>
                                        <span class="font-medium text-sm">OR</span>
                                        <div class="h-6 w-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs font-medium mx-2">B</div>
                                        <i class="fas fa-equals mx-2 text-gray-500"></i>
                                        <div class="px-2 py-1 rounded bg-green-100 text-green-800 text-xs">任一满足</div>
                                    </div>
                                    <div class="text-xs text-gray-600 bg-orange-50 p-2 rounded">
                                        只要任一条件组满足，整体就满足，所有条件组都不满足才整体不满足
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button class="btn btn-primary select-design" data-design="3">选择此方案</button>
                    </div>
                </div>

                <!-- 方案四：标签页式设计 -->
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">方案四：标签页式设计</h3>
                    <div class="mb-6 border-b pb-4">
                        <h4 class="text-md font-medium mb-3">条件组关系</h4>
                        <div class="border rounded-lg" id="design4-container">
                            <div class="flex">
                                <label class="flex-1 cursor-pointer border-r">
                                    <input type="radio" class="hidden design4-radio" name="groupRelation4" value="AND" checked>
                                    <div class="p-3 text-center relation-tab" id="tab-and-4">
                                        <div class="font-medium">AND 逻辑</div>
                                        <div class="text-xs text-gray-500 mt-1">满足所有条件组</div>
                                    </div>
                                </label>
                                <label class="flex-1 cursor-pointer">
                                    <input type="radio" class="hidden design4-radio" name="groupRelation4" value="OR">
                                    <div class="p-3 text-center relation-tab" id="tab-or-4">
                                        <div class="font-medium">OR 逻辑</div>
                                        <div class="text-xs text-gray-500 mt-1">满足任一条件组</div>
                                    </div>
                                </label>
                            </div>
                            <div class="p-4 bg-gray-50 border-t text-sm">
                                <div class="flex items-center text-gray-600">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <span id="relation-description">当前选择：满足所有条件组 - 所有条件组必须同时满足才能通过</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button class="btn btn-primary select-design" data-design="4">选择此方案</button>
                    </div>
                </div>

                <!-- 方案五：逻辑门电路式设计 -->
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">方案五：逻辑门电路式设计</h3>
                    <div class="mb-6 border-b pb-4">
                        <h4 class="text-md font-medium mb-3">条件组关系</h4>
                        <div class="flex space-x-6" id="design5-container">
                            <label class="cursor-pointer flex-1">
                                <input type="radio" class="hidden design5-radio" name="groupRelation5" value="AND" checked>
                                <div class="border-2 border-gray-200 rounded-lg p-4 hover:shadow-md transition logic-gate-card" id="gate-and-5">
                                    <div class="flex justify-center mb-3">
                                        <svg width="80" height="50" viewBox="0 0 80 50" class="mx-auto">
                                            <rect x="10" y="10" width="25" height="10" rx="3" fill="#c6e3fa" stroke="#3b82f6" />
                                            <rect x="10" y="30" width="25" height="10" rx="3" fill="#c6e3fa" stroke="#3b82f6" />
                                            <path d="M45 15 L55 15 L55 35 L45 35 Z" fill="#3b82f6" />
                                            <path d="M55 25 L65 25" stroke="#3b82f6" stroke-width="2" />
                                            <path d="M35 15 L45 15" stroke="#3b82f6" stroke-width="2" />
                                            <path d="M35 35 L45 35" stroke="#3b82f6" stroke-width="2" />
                                            <text x="45" y="28" font-size="10" fill="white" text-anchor="middle">AND</text>
                                        </svg>
                                    </div>
                                    <div class="text-center">
                                        <h5 class="font-medium">逻辑与 (AND)</h5>
                                        <p class="text-xs text-gray-500 mt-1">所有条件组必须同时满足</p>
                                    </div>
                                </div>
                            </label>
                            <label class="cursor-pointer flex-1">
                                <input type="radio" class="hidden design5-radio" name="groupRelation5" value="OR">
                                <div class="border-2 border-gray-200 rounded-lg p-4 hover:shadow-md transition logic-gate-card" id="gate-or-5">
                                    <div class="flex justify-center mb-3">
                                        <svg width="80" height="50" viewBox="0 0 80 50" class="mx-auto">
                                            <rect x="10" y="10" width="25" height="10" rx="3" fill="#fde8cc" stroke="#f97316" />
                                            <rect x="10" y="30" width="25" height="10" rx="3" fill="#fde8cc" stroke="#f97316" />
                                            <path d="M45 15 L55 15 L55 35 L45 35 Z" fill="#f97316" />
                                            <path d="M55 25 L65 25" stroke="#f97316" stroke-width="2" />
                                            <path d="M35 15 L45 15" stroke="#f97316" stroke-width="2" />
                                            <path d="M35 35 L45 35" stroke="#f97316" stroke-width="2" />
                                            <text x="45" y="28" font-size="10" fill="white" text-anchor="middle">OR</text>
                                        </svg>
                                    </div>
                                    <div class="text-center">
                                        <h5 class="font-medium">逻辑或 (OR)</h5>
                                        <p class="text-xs text-gray-500 mt-1">满足任一条件组即可</p>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button class="btn btn-primary select-design" data-design="5">选择此方案</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">选择结果</h3>
            <p id="selection-message">尚未选择任何方案，请点击"选择此方案"按钮选择您偏好的布局。</p>
            <div class="mt-4" id="selection-actions" style="display: none;">
                <button class="btn btn-primary" id="btn-confirm-selection">
                    确认并应用到项目中
                </button>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化各设计方案
            initDesign1();
            initDesign2();
            initDesign3();
            initDesign4();
            initDesign5();
            
            // 方案选择按钮事件
            document.querySelectorAll('.select-design').forEach(button => {
                button.addEventListener('click', function() {
                    const designNumber = this.getAttribute('data-design');
                    selectDesign(designNumber);
                });
            });
            
            // 确认选择按钮事件
            document.getElementById('btn-confirm-selection').addEventListener('click', function() {
                // 这里可以实现将选择保存到localStorage或发送到服务器的逻辑
                alert('已选择布局方案 ' + selectedDesign + '，即将应用到项目中');
                window.location.href = 'condition_module_edit.html';
            });
            
            // 全局变量，记录当前选择的设计方案
            let selectedDesign = null;
            
            // 方案一：卡片式选择器初始化
            function initDesign1() {
                const container = document.getElementById('design1-container');
                const andCard = document.getElementById('card-and-1');
                const orCard = document.getElementById('card-or-1');
                
                andCard.classList.add('selected-and');
                
                container.querySelectorAll('.design1-radio').forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'AND') {
                            andCard.classList.add('selected-and');
                            andCard.classList.remove('selected-or');
                            orCard.classList.remove('selected-or');
                            orCard.classList.remove('selected-and');
                        } else {
                            andCard.classList.remove('selected-and');
                            andCard.classList.remove('selected-or');
                            orCard.classList.add('selected-or');
                            orCard.classList.remove('selected-and');
                        }
                    });
                });
            }
            
            // 方案二：切换开关式设计初始化
            function initDesign2() {
                const andToggle = document.getElementById('toggle-and-2');
                const orToggle = document.getElementById('toggle-or-2');
                
                andToggle.classList.add('selected-and');
                
                document.querySelectorAll('.design2-radio').forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'AND') {
                            andToggle.classList.add('selected-and');
                            andToggle.classList.remove('selected-or');
                            orToggle.classList.remove('selected-or');
                            orToggle.classList.remove('selected-and');
                        } else {
                            andToggle.classList.remove('selected-and');
                            andToggle.classList.remove('selected-or');
                            orToggle.classList.add('selected-or');
                            orToggle.classList.remove('selected-and');
                        }
                    });
                });
            }
            
            // 方案三：图示化逻辑关系初始化
            function initDesign3() {
                const andDiagram = document.getElementById('diagram-and-3');
                const orDiagram = document.getElementById('diagram-or-3');
                
                andDiagram.classList.add('selected-and');
                
                document.querySelectorAll('.design3-radio').forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'AND') {
                            andDiagram.classList.add('selected-and');
                            andDiagram.classList.remove('selected-or');
                            orDiagram.classList.remove('selected-or');
                            orDiagram.classList.remove('selected-and');
                        } else {
                            andDiagram.classList.remove('selected-and');
                            andDiagram.classList.remove('selected-or');
                            orDiagram.classList.add('selected-or');
                            orDiagram.classList.remove('selected-and');
                        }
                    });
                });
            }
            
            // 方案四：标签页式设计初始化
            function initDesign4() {
                const andTab = document.getElementById('tab-and-4');
                const orTab = document.getElementById('tab-or-4');
                const description = document.getElementById('relation-description');
                
                andTab.classList.add('selected-and');
                
                document.querySelectorAll('.design4-radio').forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'AND') {
                            andTab.classList.add('selected-and');
                            andTab.classList.remove('selected-or');
                            orTab.classList.remove('selected-or');
                            orTab.classList.remove('selected-and');
                            description.textContent = '当前选择：满足所有条件组 - 所有条件组必须同时满足才能通过';
                        } else {
                            andTab.classList.remove('selected-and');
                            andTab.classList.remove('selected-or');
                            orTab.classList.add('selected-or');
                            orTab.classList.remove('selected-and');
                            description.textContent = '当前选择：满足任一条件组 - 只要有一个条件组满足即可通过';
                        }
                    });
                });
            }
            
            // 方案五：逻辑门电路式设计初始化
            function initDesign5() {
                const andGate = document.getElementById('gate-and-5');
                const orGate = document.getElementById('gate-or-5');
                
                andGate.classList.add('selected-and');
                
                document.querySelectorAll('.design5-radio').forEach(radio => {
                    radio.addEventListener('change', function() {
                        if (this.value === 'AND') {
                            andGate.classList.add('selected-and');
                            andGate.classList.remove('selected-or');
                            orGate.classList.remove('selected-or');
                            orGate.classList.remove('selected-and');
                        } else {
                            andGate.classList.remove('selected-and');
                            andGate.classList.remove('selected-or');
                            orGate.classList.add('selected-or');
                            orGate.classList.remove('selected-and');
                        }
                    });
                });
            }
            
            // 选择设计方案
            function selectDesign(designNumber) {
                selectedDesign = designNumber;
                document.getElementById('selection-message').innerHTML = `您已选择<strong>方案${designNumber}</strong>作为条件组关系的布局样式。`;
                document.getElementById('selection-actions').style.display = 'block';
                
                // 设置所有选择按钮为普通状态
                document.querySelectorAll('.select-design').forEach(button => {
                    button.classList.remove('bg-green-500');
                    button.classList.remove('hover:bg-green-600');
                    button.textContent = '选择此方案';
                });
                
                // 设置当前选择的按钮为特殊状态
                const selectedButton = document.querySelector(`.select-design[data-design="${designNumber}"]`);
                selectedButton.classList.add('bg-green-500');
                selectedButton.classList.add('hover:bg-green-600');
                selectedButton.textContent = '✓ 已选择';
            }
        });
    </script>
</body>
</html> 
