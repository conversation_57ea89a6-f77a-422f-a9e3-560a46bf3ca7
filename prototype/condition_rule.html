<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>条件规则管理 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 条件规则构建器样式 */
        .condition-builder {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            background-color: #f9fafb;
        }
        
        .condition-group {
            border: 1px dashed #d1d5db;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
        }
        
        .condition-rule {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background-color: #f3f4f6;
        }
        
        .operator-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            min-width: 2rem;
            text-align: center;
        }
        
        .operator-badge.and {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .operator-badge.or {
            background-color: #fdecde;
            color: #ed8936;
        }

        /* 规则内容提示 */
        .rule-content {
            position: relative;
            cursor: pointer;
        }

        .rule-content-tooltip {
            visibility: hidden;
            position: absolute;
            left: 0;
            top: 100%;
            width: 300px;
            background-color: #fff;
            color: #333;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s, visibility 0.3s;
        }

        .rule-content:hover .rule-content-tooltip {
            visibility: visible;
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4 active">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-primary font-semibold">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">基础条件规则</h2>
                <p class="text-gray-500 mt-1">管理可复用的基础条件规则，作为条件模块的构建元素</p>
            </div>
            <div>
                <a href="condition_rule_edit.html" class="btn btn-primary flex items-center">
                    <i class="fas fa-plus mr-2"></i>新建规则
                </a>
            </div>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="card p-5 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                    <input type="text" class="w-full" placeholder="输入规则名称">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">规则类型</label>
                    <select class="w-full">
                        <option value="">全部</option>
                        <option value="USER">用户属性</option>
                        <option value="TRIP">行程属性</option>
                        <option value="ORDER">订单属性</option>
                        <option value="ENVIRONMENT">环境条件</option>
                        <option value="PAYMENT">支付相关</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select class="w-full">
                        <option value="">全部</option>
                        <option value="enabled">启用</option>
                        <option value="disabled">禁用</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="btn btn-primary w-full">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 条件规则表格 -->
        <div class="card p-0 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-52">规则内容</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 条件规则1 -->
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CR001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">支付金额规则</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="tag tag-yellow">环境条件</span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="rule-content">
                                    <div class="flex items-center truncate">
                                        <i class="fas fa-info-circle mr-1 text-blue-500 flex-shrink-0"></i>
                                        <span class="truncate">当订单金额大于100元时...</span>
                                    </div>
                                    <div class="rule-content-tooltip">
                                        <p class="mb-2 font-medium">规则详情：</p>
                                        <p>当订单金额大于100元时，规则条件满足</p>
                                        <p class="mt-2 text-xs text-gray-500">适用于大额支付场景，可配合其他规则一起使用</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">已启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-15 10:00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="查看详情">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="编辑">
                                    <i class="fas fa-edit fa-lg"></i>
                                </a>
                                <button class="inline-block mx-1 text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer" title="禁用">
                                    <i class="fas fa-toggle-off fa-lg"></i>
                                </button>
                            </td>
                        </tr>

                        <!-- 条件规则2 -->
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CR002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">支付方式规则</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="tag tag-yellow">环境条件</span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="rule-content">
                                    <div class="flex items-center truncate">
                                        <i class="fas fa-info-circle mr-1 text-blue-500 flex-shrink-0"></i>
                                        <span class="truncate">当支付方式等于微信支付时...</span>
                                    </div>
                                    <div class="rule-content-tooltip">
                                        <p class="mb-2 font-medium">规则详情：</p>
                                        <p>当支付方式等于微信支付时，规则条件满足</p>
                                        <p class="mt-2 text-xs text-gray-500">用于微信支付专属场景筛选，可应用于特定优惠策略</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">已启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-14 14:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="查看详情">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="编辑">
                                    <i class="fas fa-edit fa-lg"></i>
                                </a>
                                <button class="inline-block mx-1 text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer" title="禁用">
                                    <i class="fas fa-toggle-off fa-lg"></i>
                                </button>
                            </td>
                        </tr>

                        <!-- 条件规则3 -->
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CR003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">支付时间段规则</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="tag tag-yellow">环境条件</span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="rule-content">
                                    <div class="flex items-center truncate">
                                        <i class="fas fa-info-circle mr-1 text-blue-500 flex-shrink-0"></i>
                                        <span class="truncate">当支付时间在18:00-22:00之间时...</span>
                                    </div>
                                    <div class="rule-content-tooltip">
                                        <p class="mb-2 font-medium">规则详情：</p>
                                        <p>当支付时间在18:00-22:00之间时，规则条件满足</p>
                                        <p class="mt-2 text-xs text-gray-500">用于晚间支付场景，可用于配置特定时段的支付优惠</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-red">已禁用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-13 16:45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="查看详情">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="编辑">
                                    <i class="fas fa-edit fa-lg"></i>
                                </a>
                                <button class="inline-block mx-1 text-green-600 hover:text-green-900 border-0 bg-transparent cursor-pointer" title="启用">
                                    <i class="fas fa-toggle-on fa-lg"></i>
                                </button>
                            </td>
                        </tr>

                        <!-- 条件规则4 -->
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CR004</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">节假日规则</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="tag tag-yellow">环境条件</span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="rule-content">
                                    <div class="flex items-center truncate">
                                        <i class="fas fa-info-circle mr-1 text-blue-500 flex-shrink-0"></i>
                                        <span class="truncate">当日期为法定节假日时...</span>
                                    </div>
                                    <div class="rule-content-tooltip">
                                        <p class="mb-2 font-medium">规则详情：</p>
                                        <p>当日期为法定节假日时，规则条件满足</p>
                                        <p class="mt-2 text-xs text-gray-500">用于节假日特殊支付场景，可搭配特定促销活动</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">已启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-12 09:20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="查看详情">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="编辑">
                                    <i class="fas fa-edit fa-lg"></i>
                                </a>
                                <button class="inline-block mx-1 text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer" title="禁用">
                                    <i class="fas fa-toggle-off fa-lg"></i>
                                </button>
                            </td>
                        </tr>
                        
                        <!-- 条件规则5 -->
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CR005</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">商品类型规则</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="tag tag-purple">订单属性</span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="rule-content">
                                    <div class="flex items-center truncate">
                                        <i class="fas fa-info-circle mr-1 text-blue-500 flex-shrink-0"></i>
                                        <span class="truncate">当商品类型包含于[数码,家电]时...</span>
                                    </div>
                                    <div class="rule-content-tooltip">
                                        <p class="mb-2 font-medium">规则详情：</p>
                                        <p>当商品类型包含于[数码,家电]时，规则条件满足</p>
                                        <p class="mt-2 text-xs text-gray-500">适用于数码家电类商品的支付场景，可配置分期支付方式</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">已启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-11 14:20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="查看详情">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="编辑">
                                    <i class="fas fa-edit fa-lg"></i>
                                </a>
                                <button class="inline-block mx-1 text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer" title="禁用">
                                    <i class="fas fa-toggle-off fa-lg"></i>
                                </button>
                            </td>
                        </tr>
                        
                        <!-- 条件规则6 -->
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CR006</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">终端设备规则</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="tag tag-red">支付相关</span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="rule-content">
                                    <div class="flex items-center truncate">
                                        <i class="fas fa-info-circle mr-1 text-blue-500 flex-shrink-0"></i>
                                        <span class="truncate">当设备类型等于移动端时...</span>
                                    </div>
                                    <div class="rule-content-tooltip">
                                        <p class="mb-2 font-medium">规则详情：</p>
                                        <p>当设备类型等于移动端时，规则条件满足</p>
                                        <p class="mt-2 text-xs text-gray-500">适用于手机端特定场景，可配置移动支付优先方案</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">已启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-10 11:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="查看详情">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="编辑">
                                    <i class="fas fa-edit fa-lg"></i>
                                </a>
                                <button class="inline-block mx-1 text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer" title="禁用">
                                    <i class="fas fa-toggle-off fa-lg"></i>
                                </button>
                            </td>
                        </tr>
                        
                        <!-- 条件规则7 -->
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CR007</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">支付地区规则</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="tag tag-yellow">环境条件</span>
                            </td>
                            <td class="px-6 py-4 text-sm">
                                <div class="rule-content">
                                    <div class="flex items-center truncate">
                                        <i class="fas fa-info-circle mr-1 text-blue-500 flex-shrink-0"></i>
                                        <span class="truncate">当用户所在地区包含于[北京,上海...]时...</span>
                                    </div>
                                    <div class="rule-content-tooltip">
                                        <p class="mb-2 font-medium">规则详情：</p>
                                        <p>当用户所在地区包含于[北京,上海,广州,深圳]时，规则条件满足</p>
                                        <p class="mt-2 text-xs text-gray-500">适用于一线城市特定场景，可配置地区性优惠政策</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="tag tag-green">已启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-09 09:50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="查看详情">
                                    <i class="fas fa-eye fa-lg"></i>
                                </a>
                                <a href="/prototype/condition_rule_edit.html" class="inline-block mx-1 text-blue-600 hover:text-blue-900" title="编辑">
                                    <i class="fas fa-edit fa-lg"></i>
                                </a>
                                <button class="inline-block mx-1 text-red-600 hover:text-red-900 border-0 bg-transparent cursor-pointer" title="禁用">
                                    <i class="fas fa-toggle-off fa-lg"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">
                <div class="text-sm text-gray-500">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">7</span> 条，共 <span class="font-medium">15</span> 条数据
                </div>
                <div class="flex space-x-2">
                    <button class="btn btn-light px-3 py-1 text-sm disabled:opacity-50" disabled>
                        <i class="fas fa-chevron-left mr-1"></i> 上一页
                    </button>
                    <button class="btn btn-light px-3 py-1 text-sm">
                        下一页 <i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 表格行悬停效果
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseover', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseout', function() {
                this.classList.remove('bg-gray-50');
            });
        });
    </script>
</body>
</html>
