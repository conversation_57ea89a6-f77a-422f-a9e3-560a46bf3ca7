<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑条件规则 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 条件规则构建器样式 */
        .condition-builder {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1rem;
            background-color: #f9fafb;
        }
        
        .condition-group {
            border: 1px dashed #d1d5db;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #fff;
        }
        
        .condition-rule {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.5rem;
            border-radius: 0.375rem;
            background-color: #f3f4f6;
        }
        
        .operator-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 0.75rem;
            margin-right: 0.5rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            min-width: 2rem;
            text-align: center;
        }
        
        .operator-badge.and {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .operator-badge.or {
            background-color: #fdecde;
            color: #ed8936;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300" style="transform: rotate(180deg);"></i>
                    </div>
                    <ul class="submenu active pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-primary font-medium">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">编辑基础条件规则</h2>
                <p class="text-gray-500 mt-1">创建和管理可重复使用的基础条件规则，作为条件模块的基础构建单元</p>
            </div>
            <div class="flex space-x-2">
                <a href="condition_rule.html" class="btn btn-light flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>返回规则列表
                </a>
            </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="card p-5 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">基本信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                    <input type="text" class="w-full" value="高级会员规则" placeholder="输入规则名称">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">规则编码</label>
                    <input type="text" class="w-full" value="MEMBER_LEVEL_HIGH" placeholder="输入唯一标识符">
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">规则类型</label>
                    <select class="w-full">
                        <option value="USER" selected>用户属性</option>
                        <option value="TRIP">行程属性</option>
                        <option value="ORDER">订单属性</option>
                        <option value="ENVIRONMENT">环境条件</option>
                        <option value="PAYMENT">支付相关</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">规则状态</label>
                    <select class="w-full">
                        <option value="enabled" selected>启用</option>
                        <option value="disabled">禁用</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 条件规则构建 -->
        <div class="card p-5 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800">规则定义</h3>
                <div class="text-sm text-blue-600">
                    <i class="fas fa-info-circle mr-1"></i>
                    <span>定义单一条件判断逻辑</span>
                </div>
            </div>
            
            <div class="condition-builder grid grid-cols-12 gap-4 mb-6 items-end">
                <div class="col-span-12 md:col-span-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">字段</label>
                    <select class="w-full" id="field-select">
                        <option value="user.memberLevel" selected>用户.会员等级</option>
                        <option value="user.registerDays">用户.注册天数</option>
                        <option value="user.tag">用户.标签</option>
                        <option value="user.orderCount">用户.历史订单数</option>
                        <option value="user.area">用户.所在地区</option>
                        <option value="user.age">用户.年龄段</option>
                        <option value="trip.type">行程.类型</option>
                        <option value="trip.distance">行程.距离</option>
                        <option value="order.amount">订单.金额</option>
                        <option value="payment.channel">支付.渠道</option>
                    </select>
                </div>
                
                <div class="col-span-6 md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">操作符</label>
                    <select class="w-full" id="operator-select">
                        <option value="=">=</option>
                        <option value="!=">!=</option>
                        <option value="<"><</option>
                        <option value="<="><=</option>
                        <option value=">" selected>></option>
                        <option value=">=">>=</option>
                        <option value="IN">包含于</option>
                        <option value="NOT_IN">不包含于</option>
                    </select>
                </div>
                
                <div class="col-span-6 md:col-span-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">值</label>
                    <input type="number" class="w-full" id="value-input" value="3">
                </div>
                
                <div class="col-span-12 md:col-span-2">
                    <select class="w-full" id="value-type-select">
                        <option value="integer" selected>整数</option>
                        <option value="string">字符串</option>
                        <option value="boolean">布尔值</option>
                        <option value="list">列表</option>
                    </select>
                </div>
            </div>
            
            <!-- 规则预览 -->
            <div class="border-t border-dashed border-gray-300 pt-4 mt-4">
                <h4 class="text-md font-medium mb-2">规则预览</h4>
                <div class="bg-gray-100 p-4 rounded-lg">
                    <div class="text-sm text-gray-600" id="rule-description">
                        <i class="fas fa-info-circle mr-1 text-blue-500"></i>
                        当用户会员等级大于3（适用于铂金会员和钻石会员）时，规则条件满足
                    </div>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="border-t border-dashed border-gray-300 pt-4 mt-6">
                <h4 class="text-md font-medium mb-2">规则使用说明</h4>
                <div class="bg-yellow-50 p-4 rounded-lg text-sm">
                    <p class="mb-2">
                        <i class="fas fa-lightbulb mr-1 text-yellow-500"></i>
                        <span class="font-medium">基础条件规则的用途:</span>
                    </p>
                    <ul class="list-disc list-inside pl-4 text-gray-700">
                        <li>创建后可在<a href="condition_module_edit.html" class="text-blue-600 hover:underline">条件模块</a>中被引用和组合</li>
                        <li>多个基础规则可组合成复杂的条件逻辑</li>
                        <li>规则一经创建即可在多个条件模块中重复使用</li>
                        <li>修改此规则将影响所有引用该规则的条件模块</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 按钮组 -->
        <div class="flex justify-end space-x-4">
            <button class="btn btn-light px-6">取消</button>
            <button class="btn btn-primary px-6">保存</button>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });
        
        // 根据操作符更新可用的值类型选项
        function updateValueTypeOptions() {
            const operatorSelect = document.getElementById('operator-select');
            const valueTypeSelect = document.getElementById('value-type-select');
            const operator = operatorSelect.value;
            
            // 保存当前选中的值类型（如果可能的话）
            const currentValueType = valueTypeSelect.value;
            
            // 清空现有选项
            valueTypeSelect.innerHTML = '';
            
            // 根据操作符添加合适的值类型选项
            const options = [];
            
            switch(operator) {
                case '>':
                case '<':
                case '>=':
                case '<=':
                    // 比较运算符只适用于数值类型
                    options.push({value: 'integer', text: '整数'});
                    break;
                    
                case '=':
                case '!=':
                    // 等于/不等于可用于所有类型
                    options.push({value: 'integer', text: '整数'});
                    options.push({value: 'string', text: '字符串'});
                    options.push({value: 'boolean', text: '布尔值'});
                    break;
                    
                case 'IN':
                case 'NOT_IN':
                    // 包含/不包含适用于列表和字符串
                    options.push({value: 'list', text: '列表'});
                    options.push({value: 'string', text: '字符串'});
                    break;
            }
            
            // 添加选项到选择框
            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                valueTypeSelect.appendChild(optionElement);
            });
            
            // 尝试恢复之前选中的值类型（如果它在新选项中可用）
            let valueTypeExists = false;
            for(let i = 0; i < valueTypeSelect.options.length; i++) {
                if(valueTypeSelect.options[i].value === currentValueType) {
                    valueTypeSelect.value = currentValueType;
                    valueTypeExists = true;
                    break;
                }
            }
            
            // 如果之前的值类型不可用，选择第一个选项
            if(!valueTypeExists && valueTypeSelect.options.length > 0) {
                valueTypeSelect.selectedIndex = 0;
            }
            
            // 强制更新输入框类型，不管值类型是否改变
            // 这样可以确保当操作符变化导致值类型约束变化时，输入框会正确更新
            updateValueInputType(true);
        }

        // 根据选择的值类型更新输入框类型
        function updateValueInputType(forceUpdate = false) {
            const valueTypeSelect = document.getElementById('value-type-select');
            const valueType = valueTypeSelect.value;
            
            // 获取当前的值输入元素（可能是input或select）
            let valueInput = document.getElementById('value-input');
            const valueInputParent = valueInput ? valueInput.parentNode : null;
            const currentValue = valueInput ? valueInput.value : '';
            
            // 如果当前是SELECT且需要非布尔值，或者当前是INPUT且需要布尔值，或者强制更新
            const needsReplacement = (valueInput && 
                ((valueType === 'boolean' && valueInput.tagName !== 'SELECT') || 
                (valueType !== 'boolean' && valueInput.tagName === 'SELECT')) || 
                forceUpdate);
            
            if (needsReplacement) {
                // 删除旧元素
                if (valueInput && valueInputParent) {
                    valueInputParent.removeChild(valueInput);
                }
                
                // 创建新元素
                if (valueType === 'boolean') {
                    // 创建SELECT元素用于布尔值
                    const select = document.createElement('select');
                    select.className = 'w-full';
                    select.id = 'value-input';
                    
                    const optionTrue = document.createElement('option');
                    optionTrue.value = 'true';
                    optionTrue.textContent = '是';
                    
                    const optionFalse = document.createElement('option');
                    optionFalse.value = 'false';
                    optionFalse.textContent = '否';
                    
                    select.appendChild(optionTrue);
                    select.appendChild(optionFalse);
                    
                    // 添加事件监听
                    select.addEventListener('change', updateRulePreview);
                    
                    // 添加到父元素
                    if (valueInputParent) {
                        valueInputParent.appendChild(select);
                    }
                    
                    valueInput = select;
                } else {
                    // 创建INPUT元素用于其他类型
                    const input = document.createElement('input');
                    input.className = 'w-full';
                    input.id = 'value-input';
                    
                    // 根据值类型设置input属性
                    switch(valueType) {
                        case 'integer':
                            input.type = 'number';
                            input.step = '1';
                            input.placeholder = '输入整数值';
                            // 尝试保留之前的值，如果是数字
                            if (!isNaN(currentValue)) {
                                input.value = currentValue;
                            } else {
                                input.value = '0';
                            }
                            break;
                        case 'string':
                            input.type = 'text';
                            input.placeholder = '输入字符串值';
                            input.value = currentValue;
                            break;
                        case 'list':
                            input.type = 'text';
                            input.placeholder = '使用逗号分隔多个值';
                            input.value = currentValue;
                            break;
                    }
                    
                    // 添加事件监听
                    input.addEventListener('input', updateRulePreview);
                    input.addEventListener('change', updateRulePreview);
                    
                    // 添加到父元素
                    if (valueInputParent) {
                        valueInputParent.appendChild(input);
                    }
                    
                    valueInput = input;
                }
            } else if (valueInput) {
                // 只更新现有输入框的属性，不替换元素
                if (valueType !== 'boolean') {
                    switch(valueType) {
                        case 'integer':
                            valueInput.type = 'number';
                            valueInput.step = '1';
                            valueInput.placeholder = '输入整数值';
                            break;
                        case 'string':
                            valueInput.type = 'text';
                            valueInput.placeholder = '输入字符串值';
                            break;
                        case 'list':
                            valueInput.type = 'text';
                            valueInput.placeholder = '使用逗号分隔多个值';
                            break;
                    }
                }
            }
            
            // 更新规则预览
            updateRulePreview();
        }

        // 自动生成规则预览
        function updateRulePreview() {
            const fieldSelect = document.getElementById('field-select');
            const operatorSelect = document.getElementById('operator-select');
            const valueInput = document.getElementById('value-input');
            const valueTypeSelect = document.getElementById('value-type-select');
            
            const ruleDescription = document.getElementById('rule-description');
            
            const fieldText = fieldSelect.options[fieldSelect.selectedIndex].text;
            const operatorValue = operatorSelect.value;
            const value = valueInput.value;
            const valueType = valueTypeSelect.options[valueTypeSelect.selectedIndex].value;
            
            // 操作符转换为中文
            let operatorText = '';
            switch(operatorValue) {
                case '=': operatorText = '等于'; break;
                case '!=': operatorText = '不等于'; break;
                case '>': operatorText = '大于'; break;
                case '>=': operatorText = '大于等于'; break;
                case '<': operatorText = '小于'; break;
                case '<=': operatorText = '小于等于'; break;
                case 'IN': operatorText = '包含于'; break;
                case 'NOT_IN': operatorText = '不包含于'; break;
                default: operatorText = operatorValue;
            }
            
            // 生成规则描述
            let description = '<i class="fas fa-info-circle mr-1 text-blue-500"></i> 当';
            
            // 根据不同字段和操作符生成不同的描述
            if (fieldText === '用户.会员等级') {
                if (operatorValue === '>') {
                    let memberLevelDesc = '';
                    switch(parseInt(value)) {
                        case 0: memberLevelDesc = '（适用于青铜及以上会员）'; break;
                        case 1: memberLevelDesc = '（适用于白银及以上会员）'; break;
                        case 2: memberLevelDesc = '（适用于黄金及以上会员）'; break;
                        case 3: memberLevelDesc = '（适用于铂金会员和钻石会员）'; break;
                        case 4: memberLevelDesc = '（适用于钻石会员）'; break;
                        default: memberLevelDesc = '';
                    }
                    description += `用户会员等级${operatorText}${value}${memberLevelDesc}时，规则条件满足`;
                } else if (operatorValue === '>=') {
                    description += `用户会员等级${operatorText}${value}时，规则条件满足`;
                } else if (operatorValue === '=') {
                    let memberLevelDesc = '';
                    switch(parseInt(value)) {
                        case 0: memberLevelDesc = '（普通用户）'; break;
                        case 1: memberLevelDesc = '（青铜会员）'; break;
                        case 2: memberLevelDesc = '（白银会员）'; break;
                        case 3: memberLevelDesc = '（黄金会员）'; break;
                        case 4: memberLevelDesc = '（铂金会员）'; break;
                        case 5: memberLevelDesc = '（钻石会员）'; break;
                        default: memberLevelDesc = '';
                    }
                    description += `用户会员等级${operatorText}${value}${memberLevelDesc}时，规则条件满足`;
                } else if (operatorValue === '<') {
                    description += `用户会员等级${operatorText}${value}时，规则条件满足`;
                } else {
                    description += `用户会员等级${operatorText}${value}时，规则条件满足`;
                }
            } else if (fieldText === '用户.注册天数') {
                if (operatorValue === '>') {
                    description += `用户注册天数${operatorText}${value}天时，规则条件满足`;
                } else if (operatorValue === '>=') {
                    description += `用户注册天数${operatorText}${value}天时，规则条件满足`;
                } else if (operatorValue === '<') {
                    description += `用户注册天数${operatorText}${value}天时，规则条件满足`;
                } else {
                    description += `用户注册天数${operatorText}${value}天时，规则条件满足`;
                }
            } else if (fieldText === '订单.金额') {
                if (operatorValue === '>') {
                    description += `订单金额${operatorText}${value}元时，规则条件满足`;
                } else if (operatorValue === '>=') {
                    description += `订单金额${operatorText}${value}元时，规则条件满足`;
                } else if (operatorValue === '<') {
                    description += `订单金额${operatorText}${value}元时，规则条件满足`;
                } else {
                    description += `订单金额${operatorText}${value}元时，规则条件满足`;
                }
            } else if (fieldText === '用户.历史订单数') {
                if (operatorValue === '>') {
                    description += `用户历史订单数${operatorText}${value}时，规则条件满足`;
                } else if (operatorValue === '>=') {
                    description += `用户历史订单数${operatorText}${value}时，规则条件满足`;
                } else if (operatorValue === '<') {
                    description += `用户历史订单数${operatorText}${value}时，规则条件满足`;
                } else {
                    description += `用户历史订单数${operatorText}${value}时，规则条件满足`;
                }
            } else {
                // 其他字段的通用描述
                description += `${fieldText}${operatorText}${value}时，规则条件满足`;
            }
            
            ruleDescription.innerHTML = description;
        }
        
        // 为相关字段添加事件监听
        document.getElementById('field-select').addEventListener('change', updateRulePreview);
        
        // 为操作符添加事件监听
        const operatorSelect = document.getElementById('operator-select');
        operatorSelect.addEventListener('change', updateValueTypeOptions);
        operatorSelect.addEventListener('change', updateRulePreview);
        
        document.getElementById('value-input').addEventListener('input', updateRulePreview);
        document.getElementById('value-input').addEventListener('change', updateRulePreview);
        
        // 为值类型选择添加事件监听
        const valueTypeSelect = document.getElementById('value-type-select');
        valueTypeSelect.addEventListener('change', updateValueInputType);
        valueTypeSelect.addEventListener('change', updateRulePreview);
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化值类型选项
            updateValueTypeOptions();
            // 初始化输入框类型
            updateValueInputType();
            // 初始化规则预览
            updateRulePreview();
        });
    </script>
</body>
</html> 
