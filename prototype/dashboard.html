<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收银台及渠道管理系统 - 仪表盘</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }
        
        .card-stats {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card-stats:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* Tab激活状态 */
        .tab-active {
            border-bottom: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        /* 自定义徽章样式 */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-success {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .status-failed {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .chart-container {
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .chart-container:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item active">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-primary rounded-lg bg-primary-light">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>
                
                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>

            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="mt-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">仪表盘</h2>
                <button class="btn-gradient px-4 py-2 rounded-lg flex items-center shadow-md hover:shadow-lg">
                    <i class="fas fa-sync-alt mr-2"></i>刷新数据
                </button>
            </div>

            <!-- 统计卡片区域 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- 卡片 1: 支付总额 -->
                <div class="card-stats p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">支付总额</p>
                            <p class="text-2xl font-bold text-gray-800 mt-1">¥256,389.00</p>
                            <p class="text-sm text-green-500 mt-2 flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i>
                                <span>12.5% 与上月相比</span>
                            </p>
                        </div>
                        <div class="bg-blue-100 rounded-full p-3 h-12 w-12 flex items-center justify-center">
                            <i class="fas fa-money-bill-wave text-blue-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div class="bg-blue-500 h-1 rounded-full" style="width: 75%"></div>
                    </div>
                </div>

                <!-- 卡片 2: 交易笔数 -->
                <div class="card-stats p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">交易笔数</p>
                            <p class="text-2xl font-bold text-gray-800 mt-1">10,289</p>
                            <p class="text-sm text-green-500 mt-2 flex items-center">
                                <i class="fas fa-arrow-up mr-1"></i>
                                <span>8.3% 与上月相比</span>
                            </p>
                        </div>
                        <div class="bg-green-100 rounded-full p-3 h-12 w-12 flex items-center justify-center">
                            <i class="fas fa-chart-line text-green-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div class="bg-green-500 h-1 rounded-full" style="width: 65%"></div>
                    </div>
                </div>

                <!-- 卡片 3: 成功率 -->
                <div class="card-stats p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">交易成功率</p>
                            <p class="text-2xl font-bold text-gray-800 mt-1">99.2%</p>
                            <p class="text-sm text-red-500 mt-2 flex items-center">
                                <i class="fas fa-arrow-down mr-1"></i>
                                <span>0.3% 与上月相比</span>
                            </p>
                        </div>
                        <div class="bg-purple-100 rounded-full p-3 h-12 w-12 flex items-center justify-center">
                            <i class="fas fa-check-circle text-purple-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div class="bg-purple-500 h-1 rounded-full" style="width: 92%"></div>
                    </div>
                </div>

                <!-- 卡片 4: 平均处理时间 -->
                <div class="card-stats p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500">平均处理时间</p>
                            <p class="text-2xl font-bold text-gray-800 mt-1">1.5秒</p>
                            <p class="text-sm text-green-500 mt-2 flex items-center">
                                <i class="fas fa-arrow-down mr-1"></i>
                                <span>0.2秒 与上月相比</span>
                            </p>
                        </div>
                        <div class="bg-yellow-100 rounded-full p-3 h-12 w-12 flex items-center justify-center">
                            <i class="fas fa-clock text-yellow-500 text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div class="bg-yellow-500 h-1 rounded-full" style="width: 45%"></div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- 支付趋势图 -->
                <div class="card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="font-semibold text-gray-800 text-lg">支付趋势</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-lg font-medium">日</button>
                            <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">周</button>
                            <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg">月</button>
                        </div>
                    </div>
                    <!-- 图表占位符 -->
                    <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                        <p class="text-gray-400 flex flex-col items-center">
                            <i class="fas fa-chart-line text-3xl mb-2 text-gray-300"></i>
                            此处将显示支付趋势图表
                        </p>
                    </div>
                </div>

                <!-- 支付方式分布 -->
                <div class="card p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="font-semibold text-gray-800 text-lg">支付方式分布</h3>
                        <div>
                            <button class="btn btn-light flex items-center text-sm">
                                <i class="fas fa-download mr-1"></i> 导出
                            </button>
                        </div>
                    </div>
                    <!-- 图表占位符 -->
                    <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                        <p class="text-gray-400 flex flex-col items-center">
                            <i class="fas fa-chart-pie text-3xl mb-2 text-gray-300"></i>
                            此处将显示支付方式分布图表
                        </p>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="card overflow-hidden">
                <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                    <h3 class="font-semibold text-gray-800 text-lg">最近支付记录</h3>
                    <a href="#" class="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center">
                        查看全部 <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
                <div class="table-container">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="table-header">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">交易ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">支付场景</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">支付方式</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">渠道</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">金额</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">时间</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-100">
                            <!-- 表格行 -->
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#PAY2023112100001</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">电商购物</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">支付宝</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">直连-支付宝</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥128.50</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">成功</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-21 15:30:22</td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#PAY2023112100002</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">会员充值</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">微信支付</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">直连-微信</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥88.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">成功</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-21 15:28:07</td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#PAY2023112100003</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">电商购物</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">银联</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">汇聚-银联</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥299.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-red">失败</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-21 15:25:18</td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#PAY2023112100004</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">课程购买</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">支付宝</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">直连-支付宝</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥199.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">成功</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-21 15:20:45</td>
                            </tr>
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#PAY2023112100005</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">会员充值</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">微信支付</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">汇聚-微信</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥50.00</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">成功</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-21 15:18:32</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="px-6 py-4 flex items-center justify-between border-t">
                    <div class="text-sm text-gray-600">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">8</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border rounded-md text-sm font-medium text-gray-500 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            上一页
                        </button>
                        <button class="px-3 py-1 border rounded-md text-sm font-medium text-white bg-primary hover:bg-primary-dark">
                            1
                        </button>
                        <button class="px-3 py-1 border rounded-md text-sm font-medium text-gray-500 bg-white hover:bg-gray-50">
                            2
                        </button>
                        <button class="px-3 py-1 border rounded-md text-sm font-medium text-gray-500 bg-white hover:bg-gray-50">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 动态设置当前导航项的激活状态
        const currentPath = window.location.pathname.split('/').pop();
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active','bg-primary-light', 'text-primary'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
        document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');
        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a:not(.submenu a)'); // 获取顶层链接
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            let isCurrentParent = false;
            let isCurrentPage = false;

            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                 isCurrentPage = true;
                 // 直接给顶层<a>标签添加高亮样式，而不是给<li>
                 link.classList.add('bg-primary-light', 'text-primary'); 
                 navItem.classList.add('active'); // 依然给li添加active用于样式控制如右边框
            } 
            else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary');
                    }
                });
            }
            
            // 处理父菜单展开和高亮
             if (isCurrentParent) {
                 navItem.classList.add('active'); // 给父级li添加active
                 const parentToggle = navItem.querySelector('.menu-toggle');
                 if(parentToggle) parentToggle.classList.add('text-primary'); // 高亮父菜单文字
                 
                const submenu = navItem.querySelector('.submenu');
                const icon = navItem.querySelector('.submenu-icon');
                if (submenu) {
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            }
            // 确保仪表盘在当前页面时，其li有active类，<a>有高亮类
             if (currentPath === 'dashboard.html' && link && link.getAttribute('href') === 'dashboard.html') {
                  link.classList.add('bg-primary-light', 'text-primary'); 
                  navItem.classList.add('active');
             }
        });

        // 添加表格行悬停效果
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseover', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseout', function() {
                this.classList.remove('bg-gray-50');
            });
        });

        // 动态设置页面主题颜色
        document.documentElement.style.setProperty('--primary-color', '#3b7cfe');
        document.documentElement.style.setProperty('--primary-dark', '#2a5cb9');
        document.documentElement.style.setProperty('--primary-light', '#e8f0ff');
        document.documentElement.style.setProperty('--secondary-color', '#6c5ce7');
    </script>
</body>
</html> 
