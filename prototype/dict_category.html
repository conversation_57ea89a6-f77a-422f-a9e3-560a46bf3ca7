<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据字典分类管理 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* Tab激活状态 */
        .tab-active {
            border-bottom: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        /* 自定义徽章样式 */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        /* 模态框样式 */
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.5);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }
        .modal {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            max-width: 600px;
            width: 100%;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="mt-4">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-800">字典分类管理</h2>
                <button id="addCategoryBtn" class="btn-gradient px-4 py-2 rounded-lg flex items-center shadow-md hover:shadow-lg">
                    <i class="fas fa-plus mr-2"></i>添加分类
                </button>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card p-5 mb-6">
                <div class="flex flex-wrap items-center">
                    <div class="w-full md:w-1/3 md:pr-2 mb-4 md:mb-0">
                        <div class="relative">
                            <input id="keywordSearch" type="text" placeholder="输入分类名称或编码搜索" class="w-full pl-10 pr-4 py-2">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>
                    <div class="w-full md:w-1/3 md:px-2 mb-4 md:mb-0">
                        <select id="statusFilter" class="w-full px-4 py-2">
                            <option value="">状态（全部）</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="w-full md:w-1/3 md:pl-2 flex">
                        <button id="searchBtn" class="btn btn-primary flex items-center justify-center mr-2 flex-1">
                            <i class="fas fa-filter mr-2"></i>筛选
                        </button>
                        <button id="resetBtn" class="btn btn-light flex items-center justify-center flex-1">
                            <i class="fas fa-sync-alt mr-2"></i>重置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 字典分类列表 -->
            <div class="card overflow-hidden">
                <!-- 页签 -->
                <div class="flex border-b">
                    <button class="px-6 py-3 text-base font-medium tab-active">字典分类</button>
                </div>

                <!-- 数据表格 -->
                <div class="table-container">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="table-header">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">ID</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">分类名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">分类编码</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">字典项数量</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">创建时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-100">
                            <!-- 表格行 1 -->
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">系统参数</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">SYS_PARAM</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-10 09:30:15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-3">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 edit-btn">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="#" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-ban"></i> 禁用
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 表格行 2 -->
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">支付方式</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">PAY_METHOD</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-11 10:15:20</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-3">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 edit-btn">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="#" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-ban"></i> 禁用
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 表格行 3 -->
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">3</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">支付渠道</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">PAY_CHANNEL</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">15</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-12 11:20:30</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-3">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 edit-btn">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="#" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-ban"></i> 禁用
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 表格行 4 -->
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">4</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">终端类型</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TERMINAL_TYPE</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-13 13:25:40</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-3">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 edit-btn">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="#" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-ban"></i> 禁用
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 表格行 5 -->
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">5</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">商户类型</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">MERCHANT_TYPE</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-green">启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-14 14:35:50</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-3">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 edit-btn">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="#" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-ban"></i> 禁用
                                        </a>
                                    </div>
                                </td>
                            </tr>

                            <!-- 表格行 6 -->
                            <tr class="hover:bg-gray-50 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">6</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">交易状态</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">TRANS_STATUS</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">6</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="tag tag-red">禁用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-15 15:40:55</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-3">
                                        <a href="#" class="text-indigo-600 hover:text-indigo-900 edit-btn">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="#" class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-check"></i> 启用
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="px-6 py-4 flex items-center justify-between border-t">
                    <div class="text-sm text-gray-600">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">6</span> 条，共 <span class="font-medium">6</span> 条
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border rounded-md text-sm font-medium text-gray-500 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            上一页
                        </button>
                        <button class="px-3 py-1 border rounded-md text-sm font-medium text-white bg-primary hover:bg-primary-dark">
                            1
                        </button>
                        <button class="px-3 py-1 border rounded-md text-sm font-medium text-gray-500 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 添加/编辑字典分类模态框 -->
    <div id="dictionaryCategoryModal" class="modal-overlay hidden" style="display: none;">
        <div class="modal max-w-md mx-auto p-5">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800" id="dictionaryCategoryModalLabel">添加字典分类</h3>
                <button type="button" class="text-gray-500 hover:text-gray-700" id="closeModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="dictionaryCategoryForm">
                <div class="mb-4">
                    <label for="categoryName" class="block text-sm font-medium text-gray-700 mb-1">分类名称 <span class="text-red-500">*</span></label>
                    <input type="text" id="categoryName" name="categoryName" class="w-full" placeholder="如：支付方式" required>
                </div>
                <div class="mb-4">
                    <label for="categoryCode" class="block text-sm font-medium text-gray-700 mb-1">分类编码 <span class="text-red-500">*</span></label>
                    <input type="text" id="categoryCode" name="categoryCode" class="w-full" placeholder="如：PAY_METHOD" required>
                    <p class="mt-1 text-xs text-gray-500">编码一旦创建不可修改，请使用大写字母和下划线，如：PAY_METHOD</p>
                </div>
                <div class="mb-4">
                    <label for="categoryDescription" class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                    <textarea id="categoryDescription" name="categoryDescription" rows="3" class="w-full" placeholder="请输入描述信息"></textarea>
                </div>
                <div class="mb-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="categoryStatus" name="categoryStatus" class="h-4 w-4 text-primary" checked>
                        <label for="categoryStatus" class="ml-2 block text-sm text-gray-700">启用</label>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" id="cancelBtn" class="btn btn-light px-4 py-2">取消</button>
                    <button type="button" id="saveCategoryBtn" class="btn btn-primary px-4 py-2">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入 JavaScript 库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 使用DOMContentLoaded确保文档加载完成后再执行脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏子菜单折叠展开
            document.querySelectorAll('.menu-toggle').forEach(item => {
                item.addEventListener('click', function() {
                    const submenu = this.nextElementSibling;
                    const icon = this.querySelector('.submenu-icon');
                    
                    if (submenu.classList.contains('active')) {
                        submenu.classList.remove('active');
                        icon.style.transform = 'rotate(0deg)';
                    } else {
                        submenu.classList.add('active');
                        icon.style.transform = 'rotate(180deg)';
                    }
                });
            });

            // 用户下拉菜单
            document.getElementById('userDropdown').addEventListener('click', function(e) {
                e.stopPropagation();
                document.getElementById('userMenu').classList.toggle('hidden');
            });

            // 点击页面其他位置关闭用户菜单
            document.addEventListener('click', function(event) {
                const userDropdown = document.getElementById('userDropdown');
                const userMenu = document.getElementById('userMenu');
                if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                    userMenu.classList.add('hidden');
                }
            });

            // 添加表格行悬停效果
            document.querySelectorAll('tbody tr').forEach(row => {
                row.addEventListener('mouseover', function() {
                    this.classList.add('bg-gray-50');
                });
                row.addEventListener('mouseout', function() {
                    this.classList.remove('bg-gray-50');
                });
            });

            // 打开添加分类模态框
            document.getElementById('addCategoryBtn').addEventListener('click', function() {
                const modalElement = document.getElementById('dictionaryCategoryModal');
                document.getElementById('dictionaryCategoryModalLabel').textContent = '添加字典分类';
                document.getElementById('dictionaryCategoryForm').reset();
                document.getElementById('categoryCode').removeAttribute('disabled');
                modalElement.classList.remove('hidden');
                modalElement.style.display = 'flex';
            });

            // 关闭模态框
            function closeModal() {
                const modalElement = document.getElementById('dictionaryCategoryModal');
                modalElement.classList.add('hidden');
                modalElement.style.display = 'none';
            }

            document.getElementById('closeModalBtn').addEventListener('click', closeModal);
            document.getElementById('cancelBtn').addEventListener('click', function() {
                console.log('Cancel button clicked!');
                closeModal();
            });

            // 编辑按钮点击事件
            document.querySelectorAll('.edit-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const modalElement = document.getElementById('dictionaryCategoryModal');
                    const row = this.closest('tr');
                    const id = row.cells[0].textContent;
                    const name = row.cells[1].textContent;
                    const code = row.cells[2].textContent;
                    
                    document.getElementById('dictionaryCategoryModalLabel').textContent = '编辑字典分类';
                    
                    // 填充表单数据
                    document.getElementById('categoryName').value = name;
                    document.getElementById('categoryCode').value = code;
                    
                    // 编辑时禁用分类编码
                    document.getElementById('categoryCode').setAttribute('disabled', 'disabled');
                    
                    // 根据状态设置复选框
                    const status = row.querySelector('.tag').textContent;
                    document.getElementById('categoryStatus').checked = (status === '启用');
                    
                    modalElement.classList.remove('hidden');
                    modalElement.style.display = 'flex';
                });
            });

            // 保存分类
            document.getElementById('saveCategoryBtn').addEventListener('click', function() {
                const form = document.getElementById('dictionaryCategoryForm');
                
                // 简单的表单验证
                if (!form.checkValidity()) {
                    alert('请填写必填字段');
                    return;
                }
                
                // 模拟保存成功
                alert('保存成功！');
                closeModal();
            });

            // 搜索和重置按钮功能
            document.getElementById('searchBtn').addEventListener('click', function() {
                alert('触发搜索，实际应用中应调用API并刷新表格数据');
            });
            
            document.getElementById('resetBtn').addEventListener('click', function() {
                document.getElementById('keywordSearch').value = '';
                document.getElementById('statusFilter').value = '';
                console.log('重置筛选条件');
                // TODO: 添加实际的重置逻辑，通常是重新加载数据
            });
            
            // 动态设置页面主题颜色
            document.documentElement.style.setProperty('--primary-color', '#3b7cfe');
            document.documentElement.style.setProperty('--primary-dark', '#2a5cb9');
            document.documentElement.style.setProperty('--primary-light', '#e8f0ff');
            document.documentElement.style.setProperty('--secondary-color', '#6c5ce7');
        });
    </script>
</body>
</html> 
