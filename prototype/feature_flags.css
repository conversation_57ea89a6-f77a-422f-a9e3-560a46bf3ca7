/* 全局样式 - 注释掉，使用 Tailwind */
/*
body {
    font-family: sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f7f6;
    color: #333;
}

.container {
    display: flex;
    min-height: 100vh;
}
*/

/* 侧边栏样式 - 注释掉，使用 Tailwind .sidebar */
/*
.sidebar {
    width: 20%;
    min-width: 200px; 
    background-color: #ffffff;
    padding: 20px;
    border-right: 1px solid #e0e0e0;
    box-shadow: 2px 0 5px rgba(0,0,0,0.05);
}

.sidebar h2 {
    margin-top: 0;
    color: #007bff; 
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}
*/

/* 保留 #hierarchy-tree 的 margin，但具体样式由内联或 Tailwind 控制 */
#hierarchy-tree {
    margin-top: 15px;
}

/* 主内容区样式 - 注释掉，使用 Tailwind .content */
/*
.main-content {
    width: 80%;
    padding: 20px;
    overflow-y: auto; 
}

#main-title {
    margin-top: 0;
    color: #343a40; 
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    font-size: 1.8em;
}
*/

/* 控制区域样式 - 注释掉，使用 Tailwind .card */
/*
.controls-area {
    background-color: #ffffff;
    padding: 15px;
    margin-top: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 20px;
}

.filter-bar, .bulk-actions {
    margin-bottom: 15px; 
}

.filter-bar:last-child, .bulk-actions:last-child {
    margin-bottom: 0; 
}
*/

/* 开关列表区域样式 - 注释掉，使用 Tailwind .card */
/*
.switch-list-area {
    background-color: #ffffff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.switch-list-area h2 {
     margin-top: 0;
     color: #343a40;
     border-bottom: 1px solid #eee;
     padding-bottom: 10px;
}
*/

/* 表格样式 - 注释掉，使用 Tailwind 表格类和自定义样式 */
/*
#switch-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: #fff; 
    font-size: 0.95em; 
}

#switch-table th,
#switch-table td {
    border: 1px solid #e0e0e0; 
    padding: 10px 12px; 
    text-align: left; 
    vertical-align: middle; 
}

#switch-table th {
    background-color: #f8f9fa; 
    font-weight: 600; 
    color: #495057;
}

#switch-table tbody tr:nth-child(even) {
    background-color: #fdfdfd; 
}

#switch-table tbody tr:hover {
    background-color: #f1f3f5; 
}

#switch-table th:first-child,
#switch-table td:first-child {
    width: 40px; 
    text-align: center;
}
*/

/* 复选框容器样式 - 注释掉，使用 Tailwind */
/*
.header-checkbox, .row-checkbox {
    margin-right: 0; 
    padding-left: 20px; 
}
.header-checkbox .checkmark,
.row-checkbox .checkmark {
     left: 10px; 
}
.checkbox-container {
    display: block;
    position: relative;
    padding-left: 35px; 
    margin-bottom: 12px; 
    cursor: pointer;
    font-size: 1em;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    line-height: 20px; 
}

.checkbox-container input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.checkbox-container:hover input ~ .checkmark {
    background-color: #ccc;
}

.checkbox-container input:checked ~ .checkmark {
    background-color: #007bff;
    border-color: #007bff;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}
*/

/* --- Hierarchy Tree Styles (保留) --- */
.tree {
    list-style-type: none;
    padding-left: 0;
}

.tree-node {
    margin-bottom: 5px;
}

.tree-node ul.subtree {
    list-style-type: none;
    padding-left: 20px; /* 子节点缩进 */
    margin-top: 5px;
    border-left: 1px dashed #ccc; /* 可选：添加连接线 */
    margin-left: 7px; /* 为连接线留出空间 */
}

.tree-node > span { /* 直接子span: toggle 和 name */
    cursor: pointer;
    padding: 3px 5px;
    border-radius: 4px;
    display: inline-block; /* 允许内边距和交互 */
    transition: background-color 0.2s ease; /* 平滑背景过渡 */
}

.tree-node > span.toggle {
    width: 15px; /* 固定宽度以便对齐 */
    text-align: center;
    margin-right: 3px;
    font-size: 0.8em;
    color: #666;
    font-family: monospace; /* 让箭头更清晰 */
}

.tree-node > span.node-name:hover {
    background-color: #e9ecef; /* 悬停效果 */
}

/* 使用 Tailwind 类 (e.g., bg-primary text-white font-semibold) 控制选中样式 */
.tree-node > span.node-name.selected {
    background-color: var(--primary-color); /* 使用变量 */
    color: white;
    font-weight: bold;
}

/* --- Controls Area Styles - 注释掉，使用 Tailwind --- */
/*
.filter-bar {
    display: flex;
    align-items: center;
    gap: 10px; 
    flex-wrap: wrap; 
}

#search-input {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    flex-grow: 1; 
    min-width: 200px; 
}

#add-filter-btn,
.bulk-actions button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

#add-filter-btn {
    background-color: #6c757d; 
    color: white;
}
#add-filter-btn:hover {
    background-color: #5a6268;
}

.active-filters {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.filter-tag {
    background-color: #e0e0e0;
    color: #333;
    padding: 3px 8px;
    border-radius: 12px; 
    font-size: 0.9em;
    display: inline-flex; 
    align-items: center;
}

.remove-filter-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    margin-left: 5px;
    padding: 0 3px;
    font-size: 1.1em;
    line-height: 1; 
}
.remove-filter-btn:hover {
    color: #000;
}

#status-filter {
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
}

.bulk-actions {
    display: flex;
    align-items: center;
    gap: 10px; 
}

.bulk-actions button {
    background-color: #007bff;
    color: white;
}
.bulk-actions button:hover:not(:disabled) {
    background-color: #0056b3;
}
.bulk-actions button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    opacity: 0.6;
}
*/


/* --- Toggle Switch Styles - 注释掉，使用 Tailwind 版本 --- */
/*
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px; 
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
    background-color: #28a745; 
}

input:focus + .slider {
    box-shadow: 0 0 1px #28a745;
}

input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}
*/

/* --- Tags Cell Styles - 注释掉，使用 Tailwind .tag --- */
/*
.tags-cell {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.tags-cell .tag {
    background-color: #f0f0f0;
    color: #555;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
    white-space: nowrap; 
}
*/


/* --- List Header Styles - 注释掉 --- */
/*
.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.list-header h2 {
     margin-bottom: 0; 
     border-bottom: none; 
}

#add-switch-btn {
    background-color: #28a745; 
    color: white;
    padding: 8px 15px; 
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.95em;
    transition: background-color 0.2s ease;
}

#add-switch-btn:hover {
    background-color: #218838;
}
*/

/* --- Modal Styles - 注释掉，使用 Tailwind --- */
/*
.modal {
    display: none; 
    position: fixed; 
    z-index: 1000; 
    left: 0;
    top: 0;
    width: 100%; 
    height: 100%; 
    overflow: auto; 
    background-color: rgba(0,0,0,0.4); 
    padding-top: 60px; 
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto; 
    padding: 25px;
    border: 1px solid #888;
    width: 60%; 
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    line-height: 1;
}

.close-btn:hover,
.close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.modal h2 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#add-switch-form .form-group {
    margin-bottom: 15px;
}

#add-switch-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

#add-switch-form label.toggle-switch {
     display: inline-block; 
     font-weight: normal; 
     margin-bottom: 0;
}

#add-switch-form .switch-state-label {
     margin-left: 10px;
     font-weight: normal;
     vertical-align: middle;
}

#add-switch-form input[type="text"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

#add-switch-form input[type="text"]#switch-flag-key + small {
    display: block;
    margin-top: 5px;
    font-size: 0.8em;
    color: #666;
}


#add-switch-form button[type="submit"] {
    background-color: #007bff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    float: right; 
    transition: background-color 0.2s ease;
}

#add-switch-form button[type="submit"]:hover {
    background-color: #0056b3;
}

#switch-hierarchy-path {
    font-family: monospace;
    background-color: #f0f0f0;
    padding: 5px 8px;
    border-radius: 3px;
    color: #555;
    display: inline-block;
}

.add-filter-container {
    position: relative; 
}

.tag-filter-popup {
    display: none;
    position: absolute;
    background-color: white;
    border: 1px solid #ccc;
    padding: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    z-index: 10;
    border-radius: 4px;
    min-width: 250px; 
    right: 0; 
    margin-top: 5px;
}

.tag-filter-popup h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.1em;
    color: #333;
}

.tag-filter-popup .form-group {
    margin-bottom: 10px;
}
.tag-filter-popup label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 0.9em;
}

.tag-filter-popup select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.tag-filter-popup select:disabled {
    background-color: #f0f0f0;
    cursor: not-allowed;
}

.tag-filter-popup button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    margin-right: 5px; 
}
.tag-filter-popup button:last-child {
    margin-right: 0;
}

#apply-tag-filter-btn {
    background-color: #007bff;
    color: white;
}
#apply-tag-filter-btn:hover {
    background-color: #0056b3;
}
#cancel-tag-filter-btn {
    background-color: #6c757d;
    color: white;
}
#cancel-tag-filter-btn:hover {
    background-color: #5a6268;
}

.tag-selection-group label:first-of-type { /* 主标签label */
     margin-bottom: 10px !important;
}

#modal-tag-selection-area {
     border: 1px solid #eee;
     padding: 10px;
     max-height: 150px;
     overflow-y: auto;
     border-radius: 4px;
     background-color: #fdfdfd;
}

#modal-tag-selection-area .dimension-group {
     margin-bottom: 10px;
}
#modal-tag-selection-area .dimension-group:last-child {
     margin-bottom: 0;
}

#modal-tag-selection-area strong {
     display: block;
     margin-bottom: 5px;
     font-size: 0.9em;
     color: #555;
}

/* 保留模态框内的复选框样式，因为可能比全局的好 */
#modal-tag-selection-area .checkbox-container {
    display: inline-block; /* 让标签并排 */
    margin-right: 15px;
    margin-bottom: 5px;
    position: relative;
    padding-left: 25px; /* 调整以适应稍小的复选框 */
    cursor: pointer;
    font-size: 0.9em;
}
#modal-tag-selection-area input[type="checkbox"] {
     position: absolute; opacity: 0; cursor: pointer; height: 0; width: 0;
}
#modal-tag-selection-area .checkmark {
    position: absolute;
    top: 2px;
    left: 0;
    height: 16px;
    width: 16px;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 3px;
}
#modal-tag-selection-area input:checked ~ .checkmark {
    background-color: #007bff;
    border-color: #007bff;
}
#modal-tag-selection-area .checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}
#modal-tag-selection-area input:checked ~ .checkmark:after {
    display: block;
}
*/

.feature-flag-details-row {
}