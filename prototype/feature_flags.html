<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能开关管理 - 收银台及渠道管理系统II</title>
    <!-- 使用国内CDN资源 (来自 dashboard.html) -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 引入层级导航/主内容特定样式 (来自支付开关) -->
    <link rel="stylesheet" href="feature_flags.css">
    <style>
        /* 全局样式 (完全来自 dashboard.html) */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --main-nav-width: 260px; /* Adjusted width from dashboard */
            --hierarchy-nav-width: 200px; /* Reduced from 250px */
            --header-height: 4rem; /* 64px */
        }

        /* 基础风格 (来自 dashboard.html) */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
            padding-top: var(--header-height);
            min-height: 100vh;
            display: flex;
        }

        /* 头部样式 (来自 dashboard.html) */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: var(--header-height);
            z-index: 30;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
        }

        /* 主导航侧边栏 (第一列 - 完全来自 dashboard.html) */
        .sidebar {
            width: var(--main-nav-width);
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
            position: fixed;
            top: var(--header-height);
            bottom: 0;
            left: 0;
            overflow-y: auto;
            flex-shrink: 0;
        }
        .nav-item { margin-bottom: 0.5rem; border-radius: 0.5rem; transition: all 0.3s; }
        .nav-item:hover { background-color: var(--primary-light); }
        .nav-item.active { background-color: var(--primary-light); color: var(--primary-color); border-right: 3px solid var(--primary-color); }
        .nav-item.active > a { color: var(--primary-color); } /* Target direct link for active color */
        .nav-item.active > .menu-toggle { color: var(--primary-color); } /* Target toggle text */
        .nav-item a {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem; /* Adjusted from dashboard for consistency */
            color: #333;
            text-decoration: none;
            border-radius: 0.5rem; /* Match item radius */
        }
        .nav-item a:hover { color: var(--primary-color); }
        .submenu { max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; padding-left: calc(1rem + 20px + 0.75rem); /* Align with icon+margin */ }
        .submenu.active { max-height: 500px; }
        .submenu a { padding: 0.5rem 1rem; font-size: 0.9rem; color: #555; }
        .submenu a:hover { color: var(--primary-color); }
        .submenu a.active { color: var(--primary-color); font-weight: 600; background-color: transparent; /* Sub item active has no bg */ }
        .menu-toggle { cursor: pointer; display: flex; align-items: center; justify-content: space-between; padding: 0.75rem 1rem; width: 100%; }
        .submenu-icon { transition: transform 0.3s; font-size: 0.75rem; }

        /* 层级导航侧边栏 (第二列 - 样式微调) */
        #hierarchy-sidebar {
            width: var(--hierarchy-nav-width);
            background-color: #ffffff;
            border-right: 1px solid var(--border-color);
            padding: 1rem;
            overflow-y: auto;
            flex-shrink: 0;
            z-index: 10;
        }
        #hierarchy-sidebar h2 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
         /* Styles for hierarchy tree (from feature_flags.css) should apply */


        /* 主内容区域 (第三列 - 使用 dashboard.html 的 content 类) */
        .content {
             margin-left: var(--main-nav-width);
             flex-grow: 1;
             height: calc(100vh - var(--header-height));
             display: flex;
             position: relative;
             overflow: auto;
        }

        /* NEW style for the actual content area inside the main wrapper */
        .feature-flags-main-area {
            flex-grow: 1;
            overflow-y: auto;
            padding: 1.5rem 2rem;
        }

        /* 继承 dashboard 的卡片、按钮、表单、表格、标签样式 */
         .card { background: white; border-radius: 1rem; box-shadow: 0 5px 15px rgba(0,0,0,0.05); transition: all 0.3s; margin-bottom: 1.5rem; }
         .card:hover { box-shadow: 0 8px 25px rgba(0,0,0,0.1); transform: translateY(-2px); }
         .btn { border-radius: 0.5rem; padding: 0.5rem 1rem; transition: all 0.3s; font-weight: 500; display: inline-flex; align-items: center; justify-content: center; border: none; }
         .btn-primary { background-color: var(--primary-color); color: white; }
         .btn-primary:hover { background-color: var(--primary-dark); }
         .btn-secondary { background-color: var(--secondary-color); color: white; }
         .btn-success { background-color: var(--success-color); color: white; }
         .btn-warning { background-color: var(--warning-color); color: #555; }
         .btn-danger { background-color: var(--danger-color); color: white; }
         .btn-light { background-color: #f1f5f9; color: #64748b; border: 1px solid var(--border-color); }
         .btn-light:hover { background-color: #e2e8f0; }
         .btn-sm { padding: 0.25rem 0.75rem; font-size: 0.875rem; }
         .btn-outline-secondary { border: 1px solid #6c757d; color: #6c757d; background-color: transparent; }
         .btn-outline-secondary:hover { background-color: #6c757d; color: white; }
         .btn-outline-success { border: 1px solid var(--success-color); color: var(--success-color); background-color: transparent; }
         .btn-outline-success:hover { background-color: var(--success-color); color: white; }
         .btn-outline-warning { border: 1px solid var(--warning-color); color: #b48c40; background-color: transparent; }
         .btn-outline-warning:hover { background-color: var(--warning-color); color: #555; }
         .btn:disabled { opacity: 0.5; cursor: not-allowed; }

         input, select { border-radius: 0.5rem !important; border: 1px solid #e0e6ed !important; padding: 0.5rem 1rem !important; transition: all 0.3s !important; }
         input:focus, select:focus { border-color: var(--primary-color) !important; box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important; outline: none !important; }
         .form-select { appearance: none; background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"%3E%3Cpath fill="none" stroke="%23343a40" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m2 5 6 6 6-6"/%3E%3C/svg%3E'); background-repeat: no-repeat; background-position: right 0.75rem center; background-size: 16px 12px; padding-right: 2.5rem !important; }
         .form-check-input { height: 1em; width: 1em; margin-top: 0.25em; vertical-align: top; border-radius: 0.25em; border: 1px solid rgba(0,0,0,.25); } /* Basic checkbox */
         .form-check-input:checked { background-color: var(--primary-color); border-color: var(--primary-color); }
         .form-check-input:focus { border-color: var(--primary-color); box-shadow: 0 0 0 0.25rem rgba(59, 124, 254, 0.25); }

         .table-container { border-radius: 0.5rem; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.05); }
         .table-header { background: linear-gradient(to right, #f6f8fb, #eef2f7); }
         th, td { padding: 0.75rem 1.5rem; /* Match dashboard px-6 approx */ white-space: nowrap; }
         thead th { text-align: left; font-size: 0.75rem; font-weight: 500; color: #64748b; text-transform: uppercase; letter-spacing: 0.05em; }
         tbody tr:hover { background-color: #f8fafc; }
         tbody td { font-size: 0.875rem; color: #475569; }
         tbody td.font-medium { font-weight: 500; color: #1e293b; }

         .tag { display: inline-flex; align-items: center; padding: 0.2rem 0.6rem; border-radius: 1rem; font-size: 0.75rem; font-weight: 500; margin-right: 0.25rem; margin-bottom: 0.25rem; white-space: nowrap; }
         .tag-blue { background-color: rgba(59,124,254,0.15); color: var(--primary-color); }
         .tag-green { background-color: rgba(0,184,148,0.15); color: var(--success-color); }
         .tag-purple { background-color: rgba(108,92,231,0.15); color: var(--secondary-color); }
         .tag-yellow { background-color: rgba(253,203,110,0.15); color: #d6a100; }
         .tag-red { background-color: rgba(225,112,85,0.15); color: var(--danger-color); }
         .tag-gray { background-color: rgba(45,52,54,0.1); color: #636e72; }

        /* Tailwind Modal Styles (Inherited from previous step, slightly adjusted) */
        .modal-backdrop { position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background-color: rgba(0, 0, 0, 0.5); z-index: 40; }
        .modal-container { position: fixed; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; z-index: 50; padding: 1rem; }
        .modal-content { background-color: white; border-radius: 0.5rem; /* match card radius */ box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); max-width: 500px; width: 100%; overflow: hidden; }
        .modal-header { padding: 1rem 1.5rem; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; }
        .modal-title { font-size: 1.125rem; font-weight: 600; }
        .modal-close-btn { background: none; border: none; font-size: 1.5rem; line-height: 1; color: #666; cursor: pointer; }
        .modal-body { padding: 1.5rem; max-height: 70vh; overflow-y: auto; }
        .modal-footer { padding: 1rem 1.5rem; border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end; gap: 0.5rem; }
        /* Ensure form elements inside modal use correct styles */
        .modal-body input, .modal-body select, .modal-body textarea { /* Apply standard styles */ }

        /* Toggle Switch Style (Using Tailwind-like structure, styled with variables) */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 3rem; /* 48px */
            height: 1.5rem; /* 24px */
            cursor: pointer;
        }
        .toggle-switch input { opacity: 0; width: 0; height: 0; }
        .slider {
            position: absolute;
            inset: 0;
            background-color: #e5e7eb; /* gray-200 */
            border-radius: 9999px;
            transition: background-color 0.2s ease-in-out;
        }
        .slider::before {
            content: "";
            position: absolute;
            top: 2px;
            left: 2px;
            width: 1.25rem; /* 20px */
            height: 1.25rem; /* 20px */
            background-color: white;
            border-radius: 50%;
            transition: transform 0.2s ease-in-out;
        }
        input:checked + .slider {
            background-color: var(--success-color);
        }
        input:checked + .slider::before {
            transform: translateX(1.5rem); /* 24px */
        }
         /* Hide original checkbox visually but keep accessible */
         .sr-only { position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border-width: 0; }

    </style>
</head>
<body class="bg-gray-100">

    <!-- 页面头部 (来自 dashboard.html) -->
    <header class="header flex justify-between items-center px-6">
        <div class="flex items-center space-x-4">
             <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 主导航侧边栏 (第一列 - 使用 dashboard.html 结构和样式) -->
    <aside class="sidebar">
        <nav class="mt-6 px-4">
             <ul>
                <!-- Dashboard -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>
                 <!-- Dict -->
                <li class="nav-item">
                     <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>
                <!-- Payment Scene -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                         <div class="flex items-center">
                             <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                             <span class="ml-3">支付场景管理</span>
                         </div>
                         <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">场景列表</a></li>
                        <li class="my-2"><a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">版本历史</a></li>
                    </ul>
                </li>
                <!-- Condition Filter -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                          <div class="flex items-center">
                             <i class="fas fa-filter w-5 h-5 text-center"></i>
                             <span class="ml-3">条件筛选管理</span>
                          </div>
                         <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">条件模块</a></li>
                        <li class="my-2"><a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">条件规则</a></li>
                    </ul>
                </li>
                 <!-- Payment Method -->
                 <li class="nav-item">
                     <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                         <span class="ml-3">支付方式管理</span>
                     </a>
                 </li>
                <!-- Channel Routing -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                         <div class="flex items-center">
                             <i class="fas fa-random w-5 h-5 text-center"></i>
                             <span class="ml-3">渠道路由管理</span>
                         </div>
                         <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">路由规则</a></li>
                        <li class="my-2"><a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">版本管理</a></li>
                    </ul>
                </li>
                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>
                 <!-- Merchant -->
                 <li class="nav-item">
                     <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-store w-5 h-5 text-center"></i>
                         <span class="ml-3">商户号管理</span>
                     </a>
                 </li>
                <!-- Application -->
                <li class="nav-item">
                     <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                 <!-- Channel Account -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">渠道账号</a></li>
                        <li class="my-2"><a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">渠道员工</a></li>
                    </ul>
                </li>
                <!-- Operation Log -->
                 <li class="nav-item">
                     <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                     </a>
                 </li>
            </ul>
        </nav>
    </aside>

    <!-- NEW Main Wrapper for content -->
    <main class="content"> 
        <!-- 层级导航侧边栏 (第二列) -->
        <aside id="hierarchy-sidebar">
            <h2>层级导航</h2>
            <div id="hierarchy-tree">
                 <!-- Content from 支付开关/index.html's hierarchy tree -->
                 <!-- Styling will come from feature_flags.css -->
                <ul class="tree">
                    <li class="tree-node expanded active" data-key-path="features">
                        <span class="toggle">▼</span> <span class="node-name">功能开关</span>
                        <ul class="subtree">
                            <li class="tree-node" data-key-path="features.payment">
                                <span class="toggle">►</span> <span class="node-name">支付相关</span>
                                <ul class="subtree" style="display: none;">
                                    <li class="tree-node" data-key-path="features.payment.alipay"><span class="node-name">支付宝</span></li>
                                    <li class="tree-node" data-key-path="features.payment.wechatpay"><span class="node-name">微信支付</span></li>
                                    <li class="tree-node" data-key-path="features.payment.unionpay">
                                         <span class="toggle">►</span> <span class="node-name">银联支付</span>
                                         <ul class="subtree" style="display: none;">
                                            <li class="tree-node" data-key-path="features.payment.unionpay.debitcard"><span class="node-name">借记卡</span></li>
                                            <li class="tree-node" data-key-path="features.payment.unionpay.creditcard"><span class="node-name">信用卡</span></li>
                                         </ul>
                                    </li>
                                </ul>
                            </li>
                            <li class="tree-node" data-key-path="features.usermanagement">
                                <span class="toggle">►</span> <span class="node-name">用户管理</span>
                                 <ul class="subtree" style="display: none;">
                                    <li class="tree-node" data-key-path="features.usermanagement.registration"><span class="node-name">注册流程</span></li>
                                    <li class="tree-node" data-key-path="features.usermanagement.login"><span class="node-name">登录流程</span></li>
                                 </ul>
                            </li>
                             <li class="tree-node" data-key-path="features.notifications"><span class="node-name">通知服务</span></li>
                        </ul>
                    </li>
                    <li class="tree-node" data-key-path="system">
                        <span class="toggle">►</span> <span class="node-name">系统配置</span>
                        <ul class="subtree" style="display: none;">
                             <li class="tree-node" data-key-path="system.logging"><span class="node-name">日志记录</span></li>
                             <li class="tree-node" data-key-path="system.monitoring"><span class="node-name">系统监控</span></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- 主内容区域 (第三列 - Renamed from main to div, new class) -->
        <div class="feature-flags-main-area"> <!-- WAS: <main class="content"> -->
             <div class="main-content"> <!-- Original container class from feature_flags -->
                 <h1 id="main-title" class="text-xl font-semibold text-gray-800 mb-6">开关管理 (路径: 功能开关)</h1> <!-- Adjusted margin -->

                 <!-- Controls Area - Styled like dashboard cards/forms -->
                  <div class="card p-6 mb-6">
                     <div class="filter-bar flex flex-wrap items-center gap-4 mb-4">
                         <input type="text" id="search-input" placeholder="按 Flag Key 或名称搜索..." class="flex-grow">
                         <div class="add-filter-container relative">
                             <button id="add-filter-btn" class="btn btn-light">
                                 <i class="fas fa-tag mr-1"></i> 添加标签筛选
                             </button>
                             <!-- Tag Filter Popup - structure refined -->
                             <div id="tag-filter-popup" class="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg p-4 z-20 hidden"> <!-- Increased z-index -->
                                  <h4 class="text-md font-semibold mb-3">选择筛选标签</h4>
                                  <div class="mb-3">
                                      <label for="filter-dimension" class="block text-sm font-medium mb-1">维度:</label>
                                      <select id="filter-dimension" class="w-full text-sm"></select>
                                  </div>
                                  <div class="mb-3">
                                      <label for="filter-value" class="block text-sm font-medium mb-1">值:</label>
                                      <select id="filter-value" class="w-full text-sm" disabled></select>
                                  </div>
                                  <div class="flex justify-end gap-2">
                                      <button type="button" id="apply-tag-filter-btn" class="btn btn-primary btn-sm">应用</button>
                                      <button type="button" id="cancel-tag-filter-btn" class="btn btn-light btn-sm">取消</button>
                                  </div>
                             </div>
                         </div>
                         <select id="status-filter" class="form-select w-auto">
                             <option value="all">状态: 全部</option>
                             <option value="enabled">状态: 已启用</option>
                             <option value="disabled">状态: 已禁用</option>
                         </select>
                     </div>
                      <div class="active-filters flex flex-wrap gap-2 mb-4" style="min-height: 28px;">
                         <!-- Active filters added by JS, styled with .tag class -->
                     </div>
                     <div class="bulk-actions flex items-center gap-3">
                         <label class="flex items-center text-sm cursor-pointer">
                             <input type="checkbox" id="select-all-checkbox" class="form-check-input mr-2">
                             全选
                         </label>
                         <button id="enable-selected-btn" class="btn btn-sm btn-outline-success" disabled>
                             <i class="fas fa-check-circle mr-1"></i>启用选中
                         </button>
                         <button id="disable-selected-btn" class="btn btn-sm btn-outline-warning" disabled>
                             <i class="fas fa-ban mr-1"></i>禁用选中
                         </button>
                     </div>
                 </div>

                  <!-- Switch List Area - Styled like dashboard tables -->
                 <div class="card overflow-hidden">
                      <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
                          <h3 class="font-semibold text-gray-800 text-lg">开关列表</h3>
                          <button id="add-switch-btn" class="btn btn-success btn-sm">
                              <i class="fas fa-plus mr-1"></i> 添加开关
                          </button>
                      </div>
                      <div class="table-container">
                          <table id="switch-table" class="min-w-full divide-y divide-gray-200">
                              <thead class="table-header">
                                  <tr>
                                      <th class="px-6 py-3 text-center w-12">
                                         <input type="checkbox" id="select-all-table-checkbox" class="form-check-input header-checkbox">
                                      </th>
                                      <th class="px-6 py-3">名称 / 描述</th>
                                      <th class="px-6 py-3">状态</th>
                                      <th class="px-6 py-3">Flag Key</th>
                                      <th class="px-6 py-3">关联标签</th>
                                  </tr>
                              </thead>
                              <tbody class="bg-white divide-y divide-gray-100">
                                  <!-- Example Row (JS will populate) -->
                                  <tr data-path="功能开关/支付相关/支付宝" class="hover:bg-gray-50">
                                      <td class="px-6 py-4 text-center">
                                         <input type="checkbox" class="form-check-input row-select-checkbox">
                                      </td>
                                      <td class="px-6 py-4 text-gray-800">启用支付宝新版UI</td>
                                      <td class="px-6 py-4">
                                          <label class="toggle-switch">
                                             <input type="checkbox" class="sr-only" checked>
                                             <span class="slider"></span>
                                         </label>
                                      </td>
                                      <td class="px-6 py-4 text-gray-500 font-mono">feature.payment.alipay.new_ui.enabled</td>
                                      <td class="px-6 py-4 tags-cell">
                                         <span class="tag tag-blue">Platform:ios</span>
                                         <span class="tag tag-purple">Scenario:vip</span>
                                         <span class="tag tag-green">Region:shanghai</span>
                                      </td>
                                  </tr>
                                   <tr data-path="功能开关/支付相关/微信支付" class="hover:bg-gray-50">
                                       <td class="px-6 py-4 text-center">
                                          <input type="checkbox" class="form-check-input row-select-checkbox">
                                       </td>
                                       <td class="px-6 py-4 text-gray-800">微信支付风险提示</td>
                                       <td class="px-6 py-4">
                                           <label class="toggle-switch">
                                              <input type="checkbox" class="sr-only">
                                              <span class="slider"></span>
                                          </label>
                                       </td>
                                       <td class="px-6 py-4 text-gray-500 font-mono">feature.payment.wechat.risk_warning</td>
                                       <td class="px-6 py-4 tags-cell">
                                          <span class="tag tag-blue">Platform:android</span>
                                       </td>
                                   </tr>
                                  <!-- More rows here -->
                              </tbody>
                          </table>
                      </div>
                      <!-- Pagination can be added here if needed, styled like dashboard -->
                 </div>
             </div> <!-- End main-content -->
        </div> <!-- END: feature-flags-main-area -->
    </main> <!-- END: New Main Wrapper -->

     <!-- Modal (Styled with Tailwind) -->
     <div id="add-switch-modal" class="modal-container fixed inset-0 items-center justify-center" style="display: none;"> <!-- Initially hidden via style -->
         <div class="modal-backdrop fixed inset-0 bg-gray-600 bg-opacity-50"></div> <!-- Removed onclick -->
         <div class="modal-content relative mx-auto my-auto w-full max-w-2xl bg-white rounded-lg shadow-xl"> <!-- Changed max-w-lg to max-w-2xl -->
             <div class="modal-header flex justify-between items-center p-4 border-b">
                 <h5 class="modal-title text-lg font-semibold">添加新开关</h5>
                 <button type="button" class="modal-close-btn text-gray-400 hover:text-gray-600"> <!-- Removed onclick -->
                    <span class="sr-only">Close</span>&times;
                 </button>
             </div>
             <div class="modal-body p-6">
                 <form id="add-switch-form">
                     <div class="mb-4">
                         <label for="switch-name" class="block text-sm font-medium mb-1">名称 / 描述:</label>
                         <input type="text" id="switch-name" name="switch-name" required class="w-full">
                     </div>
                     <div class="mb-4">
                         <label for="switch-flag-key" class="block text-sm font-medium mb-1">Flag Key:</label>
                         <input type="text" id="switch-flag-key" name="switch-flag-key" required class="w-full font-mono">
                         <p class="text-xs text-gray-500 mt-1">已根据层级自动生成，可修改。格式建议: domain.subdomain.feature_name</p>
                      </div>
                     <div class="mb-4 flex items-center">
                          <label class="text-sm font-medium mr-4">初始状态:</label>
                          <label class="toggle-switch">
                             <input type="checkbox" id="switch-initial-state" class="sr-only">
                             <span class="slider"></span>
                          </label>
                          <span class="switch-state-label ml-3 text-sm font-medium">禁用</span>
                      </div>
                      <div class="mb-4 tag-selection-group">
                          <label class="block text-sm font-medium mb-2">关联标签 (可选):</label>
                          <div id="modal-tag-selection-area" class="border p-2 rounded bg-gray-50 h-32 overflow-y-auto space-y-1">
                              <!-- JS fills this -->
                          </div>
                      </div>
                      <div class="mb-4">
                          <label class="block text-sm font-medium mb-1">所属层级:</label>
                          <p id="switch-hierarchy-path" class="p-2 rounded bg-gray-100 text-gray-700 text-sm font-mono"></p>
                      </div>
                      <!-- Hidden submit button allows form submission via footer button -->
                      <button type="submit" class="hidden"></button>
                 </form>
             </div>
             <div class="modal-footer flex justify-end gap-3 p-4 border-t">
                  <button type="button" class="btn btn-light">取消</button> <!-- Removed onclick -->
                 <button type="submit" form="add-switch-form" class="btn btn-primary">确认添加</button>
             </div>
         </div>
     </div>

    <!-- JS from dashboard (Sidebar, User Dropdown) + feature_flags.js -->
    <script>
        // 侧边栏子菜单折叠展开 (From dashboard.html)
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');

                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    if(icon) icon.style.transform = 'rotate(0deg)';
                } else {
                    // 关闭其他已打开的子菜单 (可选)
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                         if(openSubmenu !== submenu) {
                             openSubmenu.classList.remove('active');
                             const openIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                             if(openIcon) openIcon.style.transform = 'rotate(0deg)';
                         }
                     });
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单 (From dashboard.html)
        const userDropdownButton = document.getElementById('userDropdown');
        const userMenuDiv = document.getElementById('userMenu');
        if (userDropdownButton && userMenuDiv) {
            userDropdownButton.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenuDiv.classList.toggle('hidden');
            });
            document.addEventListener('click', function(event) {
                if (!userDropdownButton.contains(event.target) && !userMenuDiv.contains(event.target)) {
                    userMenuDiv.classList.add('hidden');
                }
            });
        }

        // 动态设置当前导航项的激活状态 (From dashboard.html)
        const currentPath = window.location.pathname.split('/').pop();
        // 清除旧的激活状态 (重要)
        document.querySelectorAll('.nav-item.active').forEach(item => {
            item.classList.remove('active');
            // Also remove potential direct styling on link/toggle if applied previously
            const link = item.querySelector('a:not(.submenu a)');
            const toggle = item.querySelector('.menu-toggle');
            link?.classList.remove('bg-primary-light', 'text-primary');
            toggle?.classList.remove('text-primary');
         });
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
        document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');

        // 设置新的激活状态
        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a:not(.submenu a)'); // 顶层链接
            const submenuLinks = navItem.querySelectorAll('.submenu a'); // 子菜单链接
            let isCurrentPage = false;
            let isCurrentParent = false;

            // 检查顶层链接是否匹配
            if (link && (link.getAttribute('href').split('/').pop() === currentPath)) {
                isCurrentPage = true;
            }

            // 检查子菜单链接是否匹配
            if (!isCurrentPage && submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary'); // 高亮子菜单项
                    }
                });
            }

            // 应用激活状态
            if (isCurrentPage) {
                navItem.classList.add('active');
                link?.classList.add('bg-primary-light', 'text-primary'); // 高亮顶层链接
            } else if (isCurrentParent) {
                navItem.classList.add('active');
                const toggle = navItem.querySelector('.menu-toggle');
                const icon = navItem.querySelector('.submenu-icon');
                const submenu = navItem.querySelector('.submenu');
                if(toggle) toggle.classList.add('text-primary'); // 高亮父菜单文字
                if(submenu) submenu.classList.add('active'); // 展开子菜单
                if(icon) icon.style.transform = 'rotate(180deg)'; // 旋转图标
            }
        });
         // 如果没有匹配项，默认高亮仪表盘 (可选，如果dashboard.html是默认页)
         if (!document.querySelector('.nav-item.active') && currentPath === 'dashboard.html') {
              const dashboardLink = document.querySelector('.sidebar a[href="dashboard.html"]');
              dashboardLink?.classList.add('bg-primary-light', 'text-primary');
              dashboardLink?.closest('.nav-item')?.classList.add('active');
         }

    </script>
    <!-- Feature Flags Specific JS (Handles hierarchy tree, filtering, table actions, modal content logic) -->
    <script src="feature_flags.js"></script>

</body>
</html> 