console.log("--- feature_flags.js script started ---"); // <-- ADDED FOR DEBUGGING

// 初始为空，后续步骤添加交互逻辑
console.log("Switch management prototype script loaded. Initializing tree...");

document.addEventListener('DOMContentLoaded', () => {
    console.log("--- DOMContentLoaded event fired ---"); // <-- ADDED FOR DEBUGGING
    const treeContainer = document.getElementById('hierarchy-tree');
    const mainTitle = document.getElementById('main-title');
    let selectedNodeNameElement = null; // 跟踪当前选中的节点名称元素
    const switchTable = document.getElementById('switch-table');
    const tableBody = switchTable?.querySelector('tbody');
    const selectAllCheckbox = document.getElementById('select-all-table-checkbox');
    const rowCheckboxes = tableBody?.querySelectorAll('.row-select-checkbox');
    const enableSelectedBtn = document.getElementById('enable-selected-btn');
    const disableSelectedBtn = document.getElementById('disable-selected-btn');
    const addSwitchBtn = document.getElementById('add-switch-btn');
    const addSwitchModal = document.getElementById('add-switch-modal');
    const closeModalBtn = addSwitchModal?.querySelector('.close-btn');
    const addSwitchForm = document.getElementById('add-switch-form');
    const hierarchyPathSpan = document.getElementById('switch-hierarchy-path');
    const switchInitialStateCheckbox = document.getElementById('switch-initial-state');
    const switchStateLabel = addSwitchForm?.querySelector('.switch-state-label');

    if (treeContainer) {
        treeContainer.addEventListener('click', (event) => {
            const target = event.target;
            const nodeLi = target.closest('.tree-node'); // 获取点击的节点li

            if (!nodeLi) return; // 如果没点在节点内，则忽略

            // --- 处理节点名称点击 (选择节点) ---
            if (target.classList.contains('node-name')) {
                // 移除上一个选中项的高亮
                if (selectedNodeNameElement) {
                    selectedNodeNameElement.classList.remove('selected');
                }
                // 高亮当前选中项
                target.classList.add('selected');
                selectedNodeNameElement = target;

                // --- 更新主标题 (模拟数据加载) ---
                const path = getPath(target);
                mainTitle.textContent = `开关管理 (路径: ${path})`;
                console.log(`Node selected: ${path}`);
                filterTableRows(path);

                event.stopPropagation(); // 阻止事件冒泡到li触发展开/折叠
            }
            // --- 处理展开/折叠图标点击 ---
            else if (target.classList.contains('toggle')) {
                 toggleNode(nodeLi);
            }
            // --- 处理点击节点其他区域 (也触发展开/折叠) ---
            // 如果点击的是 li 本身或者 toggle 旁边的空白区域，也触发 toggle
            else if (!target.classList.contains('node-name')) {
                 toggleNode(nodeLi);
            }
        });
    } else {
        console.error("Hierarchy tree container not found!");
    }

    // --- 切换节点展开/折叠状态的函数 ---
    function toggleNode(nodeLi) {
        const subtree = nodeLi.querySelector(':scope > .subtree'); // 查找直接子级 subtree
        const toggle = nodeLi.querySelector(':scope > .toggle'); // 查找直接子级 toggle

        if (subtree && toggle) { // 只有同时存在子树和切换图标的节点才能切换
            const isExpanded = nodeLi.classList.toggle('expanded');
            subtree.style.display = isExpanded ? 'block' : 'none';
            toggle.textContent = isExpanded ? '▼' : '►';
            console.log(`Node ${nodeLi.querySelector(':scope > .node-name').textContent} ${isExpanded ? 'expanded' : 'collapsed'}`);
        }
    }


    // --- 辅助函数：获取节点的层级路径 ---
    function getPath(nodeNameElement) {
        const pathParts = [];
        let current = nodeNameElement.closest('.tree-node'); // 从包含名称的节点开始
        while (current) {
            const nameSpan = current.querySelector(':scope > .node-name'); // 获取当前节点的名称
            if (nameSpan) {
                pathParts.unshift(nameSpan.textContent); // 加到路径数组的开头
            }
            // 向上查找：找到包含当前节点的父级 ul.subtree，再找到包含这个 subtree 的父级 li.tree-node
            const parentSubtree = current.closest('ul.subtree');
            current = parentSubtree ? parentSubtree.closest('li.tree-node') : null; // 如果找到父subtree，则获取它的父node，否则到达根部
        }
        return pathParts.join('/'); // 用 / 连接路径部分
    }

     // 可选：初始时选中并展开第一个根节点（如果需要）
     const firstRootNodeName = treeContainer?.querySelector('.tree > .tree-node > .node-name');
     if (firstRootNodeName) {
         firstRootNodeName.classList.add('selected');
         selectedNodeNameElement = firstRootNodeName;
         const initialPath = getPath(firstRootNodeName);
         mainTitle.textContent = `开关管理 (路径: ${initialPath})`;
         console.log(`Initial node selected: ${initialPath}`);
         filterTableRows(initialPath);
     } else {
         filterTableRows('');
     }

    // --- 新增：过滤表格行的函数 ---
    function filterTableRows(selectedPath) {
        if (!tableBody) return;
        const rows = tableBody.querySelectorAll('tr');
        let visibleRowCount = 0;

        rows.forEach(row => {
            const rowPath = row.dataset.path;
            if (rowPath && rowPath.startsWith(selectedPath)) {
                row.style.display = '';
                visibleRowCount++;
            } else {
                row.style.display = 'none';
            }
        });

        console.log(`Filtering table for path: "${selectedPath}". Visible rows: ${visibleRowCount}`);
        // 可选：处理无数据情况

        updateBulkActionButtons(); // 过滤后更新按钮状态
    }

    // --- 更新批量操作按钮状态的函数 ---
    function updateBulkActionButtons() {
        if (!tableBody) return;

        // Filter rowCheckboxes based on visibility
        const visibleRowCheckboxes = tableBody.querySelectorAll('tr:not([style*="display: none"]) .row-select-checkbox');
        const selectedVisibleCheckboxes = tableBody.querySelectorAll('tr:not([style*="display: none"]) .row-select-checkbox:checked');
        const hasSelection = selectedVisibleCheckboxes.length > 0;

        if (enableSelectedBtn) enableSelectedBtn.disabled = !hasSelection;
        if (disableSelectedBtn) disableSelectedBtn.disabled = !hasSelection;

        if (selectAllCheckbox && visibleRowCheckboxes) {
             selectAllCheckbox.checked = visibleRowCheckboxes.length > 0 && selectedVisibleCheckboxes.length === visibleRowCheckboxes.length;
             selectAllCheckbox.indeterminate = hasSelection && selectedVisibleCheckboxes.length < visibleRowCheckboxes.length;
        }
    }

    // --- 为表格中的滑动开关添加事件监听 (事件委托) ---
    if (tableBody) {
        tableBody.addEventListener('change', (event) => {
            if (event.target.type === 'checkbox' && event.target.closest('.toggle-switch')) {
                const switchElement = event.target;
                const row = switchElement.closest('tr');
                const flagKey = row?.querySelector('td:nth-child(4)')?.textContent; // 第4列是Flag Key
                console.log(`Toggle switch changed for ${flagKey}. New state: ${switchElement.checked ? 'enabled' : 'disabled'}`);
                // 在实际应用中，这里会触发API调用来更新开关状态
            } else if (event.target.classList.contains('row-select-checkbox')) {
                 // 如果是行内复选框状态改变，更新按钮状态
                 updateBulkActionButtons();
            }
        });
    }

    // --- 为全选复选框添加事件监听 ---
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', () => {
            const isChecked = selectAllCheckbox.checked;
            // Only affect visible rows
            const visibleRowCheckboxes = tableBody?.querySelectorAll('tr:not([style*="display: none"]) .row-select-checkbox');
            visibleRowCheckboxes?.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateBulkActionButtons();
        });
    }

    // --- 为批量操作按钮添加点击事件 (模拟) ---
    if (enableSelectedBtn) {
        enableSelectedBtn.addEventListener('click', () => {
            if (!enableSelectedBtn.disabled) {
                const selectedKeys = getSelectedFlagKeys();
                console.log("批量启用:", selectedKeys);
                alert(`模拟批量启用以下 Flag Keys:\n${selectedKeys.join('\n')}`);
                // 在实际应用中，这里会触发API调用
                // 可能需要取消选中并更新UI
            }
        });
    }

    if (disableSelectedBtn) {
        disableSelectedBtn.addEventListener('click', () => {
            if (!disableSelectedBtn.disabled) {
                const selectedKeys = getSelectedFlagKeys();
                console.log("批量禁用:", selectedKeys);
                 alert(`模拟批量禁用以下 Flag Keys:\n${selectedKeys.join('\n')}`);
                // 在实际应用中，这里会触发API调用
                 // 可能需要取消选中并更新UI
            }
        });
    }

    // --- 辅助函数：获取选中的Flag Key列表 (Only visible selected rows) ---
    function getSelectedFlagKeys() {
        const selectedKeys = [];
        if (tableBody) {
            const selectedVisibleCheckboxes = tableBody.querySelectorAll('tr:not([style*="display: none"]) .row-select-checkbox:checked');
            selectedVisibleCheckboxes.forEach(checkbox => {
                const row = checkbox.closest('tr');
                const flagKeyCell = row?.querySelector('td:nth-child(4)');
                if (flagKeyCell) {
                    selectedKeys.push(flagKeyCell.textContent);
                }
            });
        }
        return selectedKeys;
    }

    // --- 更新 Tag Dimensions Data (使用中文) ---
    const tagDimensionsData = {
        '平台': ['网页', 'App', 'iOS', '安卓', '小程序'],
        '场景': ['全部', '新用户', '老用户', 'VIP', '内部'],
        '区域': ['全部', '上海', '北京', '全局', '美东'],
        '功能点': ['登录按钮', '支付成功页', '用户信息编辑'] // 示例
    };

    // --- 新增：填充模态框中的标签选择区域 ---
    function populateModalTagSelection() {
        const area = document.getElementById('modal-tag-selection-area');
        if (!area) return;
        area.innerHTML = ''; // 清空旧内容

        for (const dimension in tagDimensionsData) {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'dimension-group';

            const strong = document.createElement('strong');
            strong.textContent = `${dimension}:`; // 显示维度名称
            groupDiv.appendChild(strong);

            tagDimensionsData[dimension].forEach(value => {
                const label = document.createElement('label');
                label.className = 'checkbox-container'; // 复用样式

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = `tag_${dimension}`; // name 用于分组 (虽然这里没用到)
                checkbox.value = value; // 值使用中文

                const checkmark = document.createElement('span');
                checkmark.className = 'checkmark';

                label.appendChild(checkbox);
                label.appendChild(checkmark);
                label.appendChild(document.createTextNode(` ${value}`)); // 在复选框后添加文本

                groupDiv.appendChild(label);
            });
            area.appendChild(groupDiv);
        }
        console.log("Modal tag selection area populated.");
    }

    // --- 修改：打开模态框的逻辑 ---
    if (addSwitchBtn) {
        addSwitchBtn.addEventListener('click', () => {
            if (addSwitchModal && selectedNodeNameElement) {
                const displayPath = getPath(selectedNodeNameElement);
                const keyPath = getKeyPath(selectedNodeNameElement);
                const flagKeyInput = document.getElementById('switch-flag-key');

                // --- !! 调用填充标签选择区域 !! ---
                populateModalTagSelection(); // 确保在 reset 前或后都行，因为它会重建

                addSwitchForm?.reset(); // 重置表单

                // 重新填充 reset 清掉的内容
                if (hierarchyPathSpan) {
                    hierarchyPathSpan.textContent = displayPath || '根路径';
                }
                if (keyPath && flagKeyInput) {
                    const suggestedKey = keyPath + '.new_switch_name';
                    flagKeyInput.value = suggestedKey;
                } else if (flagKeyInput) {
                    flagKeyInput.value = '';
                }
                if (switchStateLabel) switchStateLabel.textContent = '禁用';
                if (switchInitialStateCheckbox) switchInitialStateCheckbox.checked = false;

                // --- Use style.display ---
                addSwitchModal.style.display = 'flex'; // Change from hidden class to style
                // --- End Use style.display ---
            } else if (!selectedNodeNameElement) {
                alert("请先在左侧层级导航中选择一个要添加开关的层级。");
            }
        });
    }

    // --- 修改：表单提交逻辑 ---
    if (addSwitchForm && tableBody) {
        addSwitchForm.addEventListener('submit', (event) => {
            event.preventDefault();

            const switchName = addSwitchForm['switch-name'].value;
            const switchFlagKey = addSwitchForm['switch-flag-key'].value;
            const isEnabled = addSwitchForm['switch-initial-state'].checked;
            const hierarchyPath = hierarchyPathSpan?.textContent || '';

            // --- !! 修改：收集选中的标签 !! ---
            const selectedTags = [];
            const tagCheckboxes = addSwitchForm.querySelectorAll('#modal-tag-selection-area input[type="checkbox"]:checked');
            tagCheckboxes.forEach(checkbox => {
                const dimension = checkbox.name.replace('tag_', ''); // 从 name 获取维度
                selectedTags.push(`${dimension}:${checkbox.value}`); // 格式化为 "维度:值"
            });
            const tagsString = selectedTags.join(', '); // 用于日志和可能的传递
            // --- 结束修改 ---

            if (!switchName || !switchFlagKey || !hierarchyPath || hierarchyPath === '根路径') {
                alert('名称、Flag Key 不能为空，且不能在根路径添加开关！');
                return;
            }

            console.log('Adding new switch:', {
                name: switchName,
                flagKey: switchFlagKey,
                enabled: isEnabled,
                tags: tagsString, // 使用字符串格式记录
                path: hierarchyPath
            });

            const newRow = document.createElement('tr');
            newRow.dataset.path = hierarchyPath;

            // Checkbox Cell, Name Cell, Status Cell, Flag Key Cell (不变)
            const cellCheckbox = document.createElement('td');
            cellCheckbox.innerHTML = `<label class="checkbox-container row-checkbox"><input type="checkbox" class="row-select-checkbox"><span class="checkmark"></span></label>`;
            newRow.appendChild(cellCheckbox);
            const cellName = document.createElement('td');
            cellName.textContent = switchName;
            newRow.appendChild(cellName);
            const cellStatus = document.createElement('td');
            cellStatus.innerHTML = `<label class="toggle-switch"><input type="checkbox" ${isEnabled ? 'checked' : ''}><span class="slider round"></span></label>`;
            newRow.appendChild(cellStatus);
            const cellFlagKey = document.createElement('td');
            cellFlagKey.textContent = switchFlagKey;
            newRow.appendChild(cellFlagKey);

            // --- !! 修改：创建标签列内容 !! ---
            const cellTags = document.createElement('td');
            cellTags.className = 'tags-cell';
            selectedTags.forEach(tagText => { // 使用收集到的 selectedTags 数组
                const tagSpan = document.createElement('span');
                tagSpan.className = 'tag';
                tagSpan.textContent = tagText; // 直接使用 "维度:值" 格式
                cellTags.appendChild(tagSpan);
            });
            newRow.appendChild(cellTags);
            // --- 结束修改 ---

            tableBody.appendChild(newRow);

            // 检查新行是否应该根据当前过滤显示 (逻辑不变)
            const currentSelectedNodeElem = treeContainer.querySelector('.node-name.selected');
            const currentFilterPath = currentSelectedNodeElem ? getPath(currentSelectedNodeElem) : '';
            if (!hierarchyPath.startsWith(currentFilterPath)) {
                 newRow.style.display = 'none';
             } else {
                 newRow.style.display = '';
             }

            if (addSwitchModal) addSwitchModal.style.display = 'none';
            updateBulkActionButtons();
        });
    }

    // --- !! 新增/确保存在：获取节点的 Key 路径 !! ---
    function getKeyPath(nodeElement) {
        const treeNode = nodeElement.closest('.tree-node');
        return treeNode?.dataset.keyPath || ''; // 从 li 获取 data-key-path
    }

    // --- Tag Filter Popup Logic ---
    const addFilterContainer = document.querySelector('.add-filter-container');
    const addFilterBtnPopup = document.getElementById('add-filter-btn');
    const tagFilterPopup = document.getElementById('tag-filter-popup');
    const filterDimensionSelect = document.getElementById('filter-dimension');
    const filterValueSelect = document.getElementById('filter-value');
    const applyTagFilterBtn = document.getElementById('apply-tag-filter-btn');
    const cancelTagFilterBtn = document.getElementById('cancel-tag-filter-btn');
    const activeFiltersDiv = document.querySelector('.active-filters'); // Ensure this is selected

    // --- Populate Dimension Select (使用中文 tagDimensionsData) ---
    if (filterDimensionSelect) {
        Object.keys(tagDimensionsData).forEach(dimension => {
            const option = document.createElement('option');
            option.value = dimension;
            option.textContent = dimension;
            filterDimensionSelect.appendChild(option);
        });
        console.log("Tag dimensions populated.");
    } else {
        console.error("Filter dimension select element not found.");
    }

    // --- Show/Hide Popup (逻辑不变) ---
    if (addFilterBtnPopup && tagFilterPopup) {
        addFilterBtnPopup.addEventListener('click', (event) => {
            event.stopPropagation(); // Prevent click outside logic from closing immediately
            const isVisible = tagFilterPopup.style.display === 'block';
            tagFilterPopup.style.display = isVisible ? 'none' : 'block';
            console.log(`Tag filter popup ${isVisible ? 'hidden' : 'shown'}`);
            // Reset form when opening
            if (!isVisible && filterDimensionSelect && filterValueSelect) {
                filterDimensionSelect.value = '';
                filterValueSelect.innerHTML = '<option value="">--先选维度--</option>';
                filterValueSelect.disabled = true;
                console.log("Tag filter popup reset.");
            }
        });
    } else {
         console.error("Add filter button or tag filter popup not found.");
    }

    // --- Close Popup when clicking outside (逻辑不变) ---
    document.addEventListener('click', (event) => {
        if (tagFilterPopup && tagFilterPopup.style.display === 'block') {
            if (!addFilterContainer?.contains(event.target)) {
                 tagFilterPopup.style.display = 'none';
                 console.log("Clicked outside, tag filter popup hidden.");
            }
        }
    });

    // --- Populate Value Select based on Dimension Select (使用中文 tagDimensionsData) ---
    if (filterDimensionSelect && filterValueSelect) {
        filterDimensionSelect.addEventListener('change', () => {
            const selectedDimension = filterDimensionSelect.value;
            filterValueSelect.innerHTML = '';
            console.log(`Dimension changed to: ${selectedDimension}`);
            if (selectedDimension && tagDimensionsData[selectedDimension]) {
                const defaultOption = document.createElement('option');
                defaultOption.value = "";
                defaultOption.textContent = "--选择值--";
                filterValueSelect.appendChild(defaultOption);
                tagDimensionsData[selectedDimension].forEach(value => {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = value; // 值已经是中文
                    filterValueSelect.appendChild(option);
                });
                filterValueSelect.disabled = false;
            } else {
                const defaultOption = document.createElement('option');
                defaultOption.value = "";
                defaultOption.textContent = selectedDimension ? "--无可用值--" : "--先选维度--";
                filterValueSelect.appendChild(defaultOption);
                filterValueSelect.disabled = true;
                 console.log("Value select reset/disabled.");
            }
        });
    } else {
        console.error("Filter dimension or value select element not found for change listener.");
    }

    // --- Cancel Button (逻辑不变) ---
    if (cancelTagFilterBtn && tagFilterPopup) {
        cancelTagFilterBtn.addEventListener('click', () => {
             tagFilterPopup.style.display = 'none';
             console.log("Cancel button clicked, popup hidden.");
        });
    } else {
         console.error("Cancel filter button not found.");
    }

    // --- !! 新增：创建并添加激活的筛选标签到 UI !! ---
    function addActiveFilterTag(dimension, value) {
        if (!activeFiltersDiv) {
             console.error("Active filters container not found.");
             return;
        }

        // 创建标签文本，例如 "平台:App"
        const tagText = `${dimension}: ${value}`;

        // 检查是否已存在相同的标签 (避免重复添加)
        const existingTags = activeFiltersDiv.querySelectorAll('.filter-tag');
        let tagExists = false;
        existingTags.forEach(tag => {
            // 检查标签文本内容是否匹配 (忽略移除按钮部分)
             if (tag.textContent.trim().startsWith(tagText)) {
                 tagExists = true;
             }
        });

        if (tagExists) {
            console.log(`Filter tag "${tagText}" already exists.`);
            alert(`筛选标签 "${tagText}" 已存在！`);
            return; // 如果已存在，则不添加
        }

        // 创建 span 元素
        const tagSpan = document.createElement('span');
        tagSpan.className = 'filter-tag';
        tagSpan.textContent = tagText + ' '; // 添加空格以便和按钮分开

        // 创建移除按钮
        const removeBtn = document.createElement('button');
        removeBtn.className = 'remove-filter-btn';
        removeBtn.innerHTML = '&times;'; // 使用 HTML 实体 '×'
        removeBtn.type = 'button'; // Ensure it's not a submit button

        // 将移除按钮附加到 span
        tagSpan.appendChild(removeBtn);

        // 将 tagSpan 附加到 activeFiltersDiv
        activeFiltersDiv.appendChild(tagSpan);
        console.log(`Added filter tag: ${tagText}`);

        // 在这里可以触发实际的表格过滤逻辑（如果需要的话）
        // applyTableFilters(); // 例如调用一个总的过滤函数
    }

    // --- !! 修改：Apply Button 事件监听器 !! ---
    if (applyTagFilterBtn && filterDimensionSelect && filterValueSelect) {
        applyTagFilterBtn.addEventListener('click', () => {
            const dimension = filterDimensionSelect.value;
            const value = filterValueSelect.value;
            if (dimension && value) {
                console.log(`Apply filter button clicked - Dimension: ${dimension}, Value: ${value}`);
                // --- !! 调用添加标签函数 !! ---
                addActiveFilterTag(dimension, value);
                 if(tagFilterPopup) tagFilterPopup.style.display = 'none'; // Hide after applying
            } else {
                alert('请选择维度和值。');
            }
        });
    } else {
         console.error("Apply filter button or selects not found for listener.");
    }

    // --- !! 新增：为 activeFiltersDiv 添加事件委托，处理移除按钮点击 !! ---
    if (activeFiltersDiv) {
        activeFiltersDiv.addEventListener('click', (event) => {
            // 检查点击的是否是移除按钮
            if (event.target.classList.contains('remove-filter-btn')) {
                const tagToRemove = event.target.closest('.filter-tag'); // 找到包含按钮的标签 span
                if (tagToRemove) {
                    console.log(`Removing filter tag: ${tagToRemove.textContent.trim().slice(0,-1)}`); // Log removed tag text (excluding ' ×')
                    tagToRemove.remove(); // 从 DOM 中移除标签
                    // 在这里可以触发实际的表格过滤逻辑（如果需要的话）
                    // applyTableFilters(); // 例如调用一个总的过滤函数
                }
            }
        });
    } else {
        console.error("Active filters container not found for delegation.");
    }

    // --- 关闭模态框 (通过按钮) ---
    // Find the close button using a more specific selector if needed, e.g., within the modal header
    const modalHeaderCloseButton = addSwitchModal?.querySelector('.modal-header .modal-close-btn');
    if (modalHeaderCloseButton) {
         console.log("Attaching click listener to modal header close button.");
        modalHeaderCloseButton.addEventListener('click', () => {
             console.log("Modal header close button clicked!");
            if (addSwitchModal) {
                 // --- Use style.display ---
                 addSwitchModal.style.display = 'none'; // Change from hidden class to style
                 // --- End Use style.display ---
                 console.log("Modal display set to 'none'.");
            } else {
                 console.error("Tried to close modal via header button, but modal element not found.");
            }
        });
    } else {
         console.log("Modal header close button not found, listener not attached.");
    }

     // --- 关闭模态框 (通过取消按钮) ---
     const modalFooterCancelButton = addSwitchModal?.querySelector('.modal-footer .btn-light');
      if (modalFooterCancelButton) {
          console.log("Attaching click listener to modal footer cancel button.");
          modalFooterCancelButton.addEventListener('click', () => {
              console.log("Modal footer cancel button clicked!");
              if (addSwitchModal) {
                  // --- Use style.display ---
                  addSwitchModal.style.display = 'none'; // Change from hidden class to style
                  // --- End Use style.display ---
                  console.log("Modal display set to 'none'.");
              } else {
                   console.error("Tried to close modal via footer cancel, but modal element not found.");
              }
          });
      } else {
           console.log("Modal footer cancel button not found, listener not attached.");
      }


    // --- 关闭模态框 (通过点击外部区域/遮罩) ---
    const modalBackdrop = addSwitchModal?.querySelector('.modal-backdrop');
    if (addSwitchModal && modalBackdrop) {
        console.log("Attaching click listener to modal backdrop.");
         modalBackdrop.addEventListener('click', (event) => {
             // Ensure the click is directly on the backdrop, not on the modal content propagating up
             if (event.target === modalBackdrop) {
                  console.log("Modal backdrop clicked!");
                 // --- Use style.display ---
                 addSwitchModal.style.display = 'none'; // Change from hidden class to style
                 // --- End Use style.display ---
                 console.log("Modal display set to 'none'.");
             }
         });
     } else {
          console.log("Modal or backdrop not found for backdrop close listener.");
     }

    // --- 表单提交成功后关闭模态框 ---
    if (addSwitchForm && tableBody) {
        addSwitchForm.addEventListener('submit', (event) => {
            event.preventDefault();

            // ... (existing form processing logic) ...

            // --- Use style.display ---
            if (addSwitchModal) addSwitchModal.style.display = 'none'; // Change from hidden class to style
            // --- End Use style.display ---
            updateBulkActionButtons();
        });
    }

    // --- Remove the old generic closeModalBtn listener setup if it exists ---
    // This might be slightly different based on previous edits, ensure the old listener is gone.
    // Example: Check if closeModalBtn was defined and remove its listener if found.
    // This part might need adjustment based on the exact state of the code before this edit.

    // --- 更新开关状态标签文本 ---
    if (switchInitialStateCheckbox && switchStateLabel) {
        // ... (existing code) ...
    }

    // --- 表格事件监听 (保持不变) ---
    if (tableBody) {
        // ... (existing code) ...
    }

    // --- 全选复选框事件监听 (保持不变) ---
    if (selectAllCheckbox) {
        // ... (existing code) ...
    }

    // --- 批量按钮点击事件监听 (保持不变) ---
    if (enableSelectedBtn) {
        // ... (existing code) ...
    }
    if (disableSelectedBtn) {
        // ... (existing code) ...
    }

    // --- Tag Filter Popup Logic (保持不变) ---
    /* ... */

    // --- 初始化加载 (保持不变) ---
    /* ... */
}); 