<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商户号详情 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
         .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        /* 详情列表样式 */
        .detail-list dt {
            font-weight: 500;
            color: #64748b; /* Tailwind gray-500 */
        }
        .detail-list dd {
            color: #334155; /* Tailwind gray-700 */
        }
        .detail-list > div {
            padding-top: 0.75rem;
            padding-bottom: 0.75rem;
        }
        .detail-list > div:not(:last-child) {
            border-bottom: 1px dashed #e5e7eb; /* Tailwind gray-200 */
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        
         /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
         .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
         .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
     <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 (当前页面父级，需要高亮) -->
                <li class="nav-item active">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>

                
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="merchant.html" class="hover:text-primary">商户号管理</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">商户号详情</span>
        </div>

        <div class="flex justify-between items-center mb-6">
             <h2 class="text-2xl font-bold text-gray-800">商户号详情 (MCH001)</h2>
             <div>
                 <a href="merchant_edit.html?id=MCH001" class="btn btn-primary">
                     <i class="fas fa-edit mr-2"></i>编辑
                 </a>
                 <a href="merchant.html" class="btn btn-light ml-2">
                     <i class="fas fa-arrow-left mr-2"></i>返回列表
                 </a>
            </div>
        </div>
       

        <div class="card p-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">基本信息</h3>
            <dl class="detail-list grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-1 text-sm">
                <div>
                    <dt>商户名称</dt>
                    <dd>示例商户A</dd>
                </div>
                <div>
                    <dt>商户号</dt>
                    <dd>1234567890</dd>
                </div>
                 <div>
                    <dt>商户简称</dt>
                    <dd>示例A</dd>
                </div>
                <div>
                    <dt>状态</dt>
                    <dd><span class="tag tag-green">启用</span></dd>
                </div>
                 <div>
                    <dt>商户类型</dt>
                    <dd>服务商</dd>
                </div>
                <div>
                    <dt>渠道类型</dt>
                    <dd><span class="tag tag-green">微信支付</span></dd>
                </div>
                 <div class="md:col-span-3">
                    <dt>创建时间</dt>
                    <dd>2023-10-26 10:00:00</dd>
                </div>
                 <div class="md:col-span-3">
                    <dt>最后更新时间</dt>
                    <dd>2023-10-27 14:20:05</dd>
                </div>
                <div class="md:col-span-3">
                    <dt>描述</dt>
                    <dd class="whitespace-pre-wrap">这是一个用于测试的示例商户，关联了电商平台和官网。</dd>
                </div>
            </dl>

             <h3 class="text-xl font-semibold text-gray-900 mt-8 mb-6 border-b pb-3">关联信息</h3>
             <dl class="detail-list grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-1 text-sm">
                 <div class="md:col-span-3">
                     <dt>关联应用</dt>
                     <dd class="mt-1">
                         <a href="application_detail.html?id=APP001" class="tag tag-blue hover:underline">电商平台App</a>
                         <a href="application_detail.html?id=APP003" class="tag tag-purple hover:underline">官方网站</a>
                    </dd>
                 </div>
                 <div class="md:col-span-3">
                     <dt>关联账号</dt>
                     <dd class="mt-1">
                         <a href="channel_account.html?id=CHNACC001" class="tag tag-blue hover:underline">官方微信支付账号</a>
                    </dd>
                 </div>
                 <div class="md:col-span-3">
                     <dt>关联商户号</dt>
                     <dd class="mt-1">
                         <a href="merchant_detail.html?id=MCH003" class="tag tag-blue hover:underline">MCH003 (示例商户C)</a>
                    </dd>
                 </div>
                <!-- 可以添加其他关联信息，如关联的支付方式、渠道账号等 -->
            </dl>

            <h3 class="text-xl font-semibold text-gray-900 mt-8 mb-6 border-b pb-3">配置信息</h3>
            <dl class="detail-list grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-1 text-sm">
                 <div>
                    <dt>配置项 Key 1</dt>
                    <dd>value1</dd>
                </div>
                <div>
                    <dt>配置项 Key 2</dt>
                    <dd>******** <span class="text-xs text-gray-400">(已隐藏)</span></dd>
                </div>
                <div class="md:col-span-3">
                    <dt>其他配置 (JSON)</dt>
                    <dd>
                        <pre class="bg-gray-50 p-3 rounded-md text-xs font-mono overflow-x-auto"><code>{
    "feature_A": true,
    "limit": 100
}</code></pre>
                    </dd>
                </div>
                 <!-- 可以根据实际需要展示更多配置项 -->
            </dl>
        </div>
    </main>

    <script>
        // --- 以下是模板的通用JS --- 
        
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            // 确保元素存在
            if(!submenu || !icon) return; 
            
            let isActive = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.href === window.location.href) {
                    isActive = true;
                    link.classList.add('font-semibold', 'text-primary'); 
                }
            });

            // 如果子菜单包含活动项 或 父菜单是active，则展开
             if (isActive || item.closest('.nav-item').classList.contains('active')) {
                 if(!item.closest('.nav-item').querySelector('a[href]') && isActive) {
                     submenu.classList.add('active');
                     icon.style.transform = 'rotate(180deg)';
                 } else if (item.closest('.nav-item').classList.contains('active')) {
                      submenu.classList.add('active');
                      icon.style.transform = 'rotate(180deg)';
                 }
            }
            
            // 添加点击事件
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 动态设置当前导航项的激活状态
        const currentPath = window.location.pathname.split('/').pop();
        // 移除所有激活状态以重新计算
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
         document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
         document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');

        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a');
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            let isCurrentParent = false;
            let isCurrentPage = false;

            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                 isCurrentPage = true;
            } else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary');
                    }
                });
            }
            
             // 检查是否当前详情页对应的列表页父菜单
            const parentLink = 'merchant.html'; // 定义父菜单链接
            if (currentPath === 'merchant_edit.html' || currentPath === 'merchant_detail.html') {
                 if (link && link.getAttribute('href') === parentLink) {
                     isCurrentParent = true; // 将父菜单标记为当前
                 }
            }

            if (isCurrentPage || isCurrentParent) {
                 navItem.classList.add('active');
                const submenu = navItem.querySelector('.submenu');
                const icon = navItem.querySelector('.submenu-icon');
                if (submenu && isCurrentParent) { // 只有父菜单才展开子菜单
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            }
        });

    </script>
</body>
</html> 
