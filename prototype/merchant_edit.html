<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑商户号 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 添加 Select2 CSS -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 标签页样式 - 从 payment_method_detail.html 复制 */
        .tab-nav {
            border-bottom: 1px solid var(--border-color);
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .tab-link {
            padding: 1rem 1.5rem; /* 使用 payment_method_detail 的 padding */
            color: #64748b;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
            text-decoration: none; /* 确保a标签没有下划线 */
            transition: color 0.3s; /* 平滑颜色过渡 */
        }
        
        .tab-link:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color);
            transform: scaleX(0);
            transition: transform 0.3s;
        }
        
        .tab-link.active {
            color: var(--primary-color);
        }
        
        .tab-link.active:after {
            transform: scaleX(1);
        }
        /* 结束 标签页样式 */

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
         .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* Select2 样式调整 */
        .select2-container .select2-selection--single {
            height: calc(2.25rem + 2px) !important; /* Tailwind form-input height */
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 1.5rem !important; /* Adjust vertical alignment */
            padding-left: 0 !important;
             padding-right: 2rem !important; /* Space for arrow */
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: calc(2.25rem + 2px) !important;
            right: 0.5rem !important;
        }
        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--primary-color) !important;
            color: white !important;
        }
         .select2-dropdown {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
        }
        .select2-container--default .select2-search--dropdown .select2-search__field {
            border-radius: 0.375rem !important;
             border: 1px solid #e0e6ed !important;
             padding: 0.5rem 1rem !important;
        }
         .select2-container .select2-selection--multiple {
            min-height: calc(2.25rem + 2px) !important;
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.25rem 0.5rem !important; /* Adjust padding for multiple */
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: var(--primary-light) !important;
            border: 1px solid var(--primary-color) !important;
            color: var(--primary-dark) !important;
            border-radius: 0.375rem !important;
            padding: 0.2rem 0.5rem !important;
            margin-top: 0.2rem !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: var(--primary-dark) !important;
            margin-right: 0.3rem !important;
        }
         .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
             color: var(--danger-color) !important;
        }

    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
     <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="#" class="block py-2 text-gray-400 hover:text-primary cursor-not-allowed">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item active">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-primary rounded-lg bg-primary-light">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="merchant.html" class="hover:text-primary">商户号管理</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">编辑商户号</span> <!-- 或者 "创建商户号" -->
        </div>

        <h2 class="text-2xl font-bold text-gray-800 mb-6">编辑商户号</h2> <!-- 或者 "创建商户号" -->

        <div class="card"> <!-- Remove p-8 from card -->
            <!-- Tab Navigation (Matching reference structure) -->
            <div class="tab-nav"> <!-- Removed mb-6 px-8 pt-8 -->
                 <a href="#tab-basic" id="tab-basic-link" class="tab-link active" data-tab="tab-content-basic">基础信息</a> 
                 <a href="#tab-associations" id="tab-associations-link" class="tab-link" data-tab="tab-content-associations">关联信息</a>
             </div>

            <!-- Content wrapper with p-6, matching reference -->
            <div class="p-6"> 
                <!-- Basic Information Tab Content -->
                 <div id="tab-content-basic" class="tab-content active"> <!-- Add tab-content and active class -->
                     <form action="#" method="POST"> <!-- Form wraps only basic info -->
                         <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                             <!-- 商户名称 -->
                             <div>
                                <label for="merchant-name" class="block text-sm font-medium text-gray-700 mb-1">商户名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="merchant-name" name="merchant-name" value="示例商户A" required class="w-full" placeholder="请输入商户的法定名称或常用名">
                             </div>

                             <!-- 商户号 -->
                             <div>
                                <label for="merchant-id" class="block text-sm font-medium text-gray-700 mb-1">商户号 <span class="text-red-500">*</span></label>
                                <input type="text" id="merchant-id" name="merchant-id" value="1234567890" required class="w-full" placeholder="请输入商户在支付平台的唯一标识">
                             </div>

                              <!-- 商户简称 -->
                             <div>
                                <label for="merchant-shortname" class="block text-sm font-medium text-gray-700 mb-1">商户简称</label>
                                <input type="text" id="merchant-shortname" name="merchant-shortname" value="示例A" class="w-full" placeholder="用于系统内部显示">
                             </div>

                             <!-- 状态 -->
                             <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态 <span class="text-red-500">*</span></label>
                                <select id="status" name="status" required class="w-full">
                                    <option value="active" selected>启用</option>
                                    <option value="inactive">禁用</option>
                                </select>
                             </div>

                             <!-- 商户类型 -->
                             <div>
                                <label for="merchant-type" class="block text-sm font-medium text-gray-700 mb-1">商户类型</label>
                                <select id="merchant-type" name="merchant-type" class="w-full select2-basic">
                                    <option value="">请选择</option>
                                    <option value="normal">普通商户</option>
                                    <option value="service" selected>服务商</option>
                                    <option value="special">特约商户</option>
                                </select>
                             </div>
                             
                             <!-- 渠道类型 -->
                             <div>
                                <label for="channel-type" class="block text-sm font-medium text-gray-700 mb-1">渠道类型</label>
                                <select id="channel-type" name="channel-type" class="w-full select2-basic">
                                    <option value="">请选择</option>
                                    <option value="wechat" selected>微信支付</option>
                                    <option value="alipay">支付宝</option>
                                    <option value="unionpay">云闪付</option>
                                    <!-- 更多渠道类型选项 -->
                                </select>
                             </div>
                         </div>
                     </form> <!-- End of form for basic info -->
                 </div> <!-- End of Basic Information Tab Content -->

                 <!-- Association Information Tab Content -->
                 <div id="tab-content-associations" class="tab-content hidden"> <!-- Add tab-content and hidden class -->
                     <div class="grid grid-cols-1 gap-6">
                         <!-- 关联账号 -->
                         <div>
                            <label for="linked-accounts" class="block text-sm font-medium text-gray-700 mb-1">关联账号</label>
                            <select id="linked-accounts" name="linked-accounts[]" class="w-full select2-multiple" multiple="multiple">
                                <option value="CHNACC001" selected>官方微信支付账号</option>
                                <option value="CHNACC002">官方支付宝账号</option>
                                <option value="CHNACC003">测试云闪付账号</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">选择此商户号关联的渠道账号</p>
                         </div>

                         <!-- 关联商户号 -->
                         <div>
                            <label for="linked-merchants" class="block text-sm font-medium text-gray-700 mb-1">关联商户号</label>
                            <select id="linked-merchants" name="linked-merchants[]" class="w-full select2-multiple" multiple="multiple">
                                <option value="MCH003" selected>MCH003 (示例商户C)</option>
                                <option value="MCH004">MCH004 (示例商户D)</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">设置关联的其他商户号</p>
                         </div>

                              <!-- 关联应用 -->
                             <div>
                                <label for="linked-apps" class="block text-sm font-medium text-gray-700 mb-1">关联应用</label>
                                <select id="linked-apps" name="linked-apps[]" class="w-full select2-multiple" multiple="multiple">
                                    <option value="APP001" selected>电商平台App</option>
                                    <option value="APP002">游戏中心</option>
                                    <option value="APP003" selected>官方网站</option>
                                     <option value="APP004">小程序商城</option>
                                </select>
                                <p class="text-xs text-gray-500 mt-1">选择此商户号可被哪些应用使用。</p>
                             </div>
                         </div>
                     </div> <!-- End of Association Information Tab Content -->

                 <!-- 操作按钮 (Moved outside form, inside p-6 wrapper) -->
                 <div class="mt-8 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                     <a href="merchant.html" class="btn btn-light">取消</a>
                     <button type="submit" form="basic-info-form" class="btn btn-primary">保存</button> <!-- Add form attribute if needed, or handle via JS -->
                     <!-- Note: The submit button might need JS to handle submission if multiple forms exist or if fields outside the form tag need processing -->
                 </div>
            </div> <!-- End of p-6 content wrapper div -->
        </div>
    </main>

    <!-- 引入 jQuery 和 Select2 JS -->
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/select2/4.0.13/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // 初始化基础 Select2
            $('.select2-basic').select2({
                 placeholder: "请选择",
                 allowClear: true,
                 width: '100%' // 确保宽度适应父容器
            });

            // 初始化多选 Select2
            $('.select2-multiple').select2({
                 placeholder: "选择关联的应用",
                 allowClear: true,
                 width: '100%', // 确保宽度适应父容器
                 closeOnSelect: false // 允许多选时下拉框保持打开
            });
        });

        // --- 以下是模板的通用JS --- 
        
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
             // 确保元素存在
            if(!submenu || !icon) return; 
            
            let isActive = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.href === window.location.href) {
                    isActive = true;
                    link.classList.add('font-semibold', 'text-primary'); 
                }
            });

            // 如果子菜单包含活动项 或 父菜单是active，则展开
             if (isActive || item.closest('.nav-item').classList.contains('active')) {
                 if(!item.closest('.nav-item').querySelector('a[href]') && isActive) {
                     submenu.classList.add('active');
                     icon.style.transform = 'rotate(180deg)';
                 } else if (item.closest('.nav-item').classList.contains('active')) {
                      submenu.classList.add('active');
                      icon.style.transform = 'rotate(180deg)';
                 }
            }
            
             // 添加点击事件
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 动态设置当前导航项的激活状态
        const currentPath = window.location.pathname.split('/').pop();
        // 移除所有激活状态以重新计算
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
         document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
         document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');

        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a');
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            let isCurrentParent = false;
            let isCurrentPage = false;

            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                 isCurrentPage = true;
            } else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary');
                    }
                });
            }
            
            // 检查是否当前编辑页对应的列表页父菜单
            const parentLink = 'merchant.html'; // 定义父菜单链接
            if (currentPath === 'merchant_edit.html' || currentPath === 'merchant_detail.html') {
                 if (link && link.getAttribute('href') === parentLink) {
                     isCurrentParent = true; // 将父菜单标记为当前
                 }
            }
            

            if (isCurrentPage || isCurrentParent) {
                 navItem.classList.add('active');
                const submenu = navItem.querySelector('.submenu');
                const icon = navItem.querySelector('.submenu-icon');
                if (submenu && isCurrentParent) { // 只有父菜单才展开子菜单
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            }
        });

        // Tab switching logic (Updated to match payment_method_detail.html logic)
        const tabLinks = document.querySelectorAll('.tab-link');
        const tabContents = document.querySelectorAll('.tab-content-container > div'); // May need adjustment if container class changes
        const allTabContentDivs = document.querySelectorAll('.p-6 > .tab-content'); // More specific selector based on new structure

        tabLinks.forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Deactivate all tab links
                tabLinks.forEach(t => t.classList.remove('active'));
                
                // Hide all tab content divs
                allTabContentDivs.forEach(content => content.classList.add('hidden'));
                // Also remove 'active' class if it's used for visibility control (redundant if 'hidden' is used)
                allTabContentDivs.forEach(content => content.classList.remove('active')); 

                // Activate clicked tab link
                this.classList.add('active');
                
                // Show corresponding content div
                const tabId = this.getAttribute('data-tab');
                const contentToShow = document.getElementById(tabId);
                if (contentToShow) {
                    contentToShow.classList.remove('hidden');
                    contentToShow.classList.add('active'); // Add active class to the content as well
                }
            });
        });

        // Ensure initial state is correct (JS redundant if HTML sets initial active/hidden correctly)
        // const initialActiveTab = document.querySelector('.tab-link.active');
        // if (initialActiveTab) {
        //     const initialContentId = initialActiveTab.getAttribute('data-tab');
        //     const initialContent = document.getElementById(initialContentId);
        //     allTabContentDivs.forEach(content => content.classList.add('hidden'));
        //     if (initialContent) initialContent.classList.remove('hidden');
        // }

    </script>
</body>
</html> 
