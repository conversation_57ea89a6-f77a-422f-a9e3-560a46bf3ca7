<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>操作日志 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Flatpickr CSS (日期选择器) -->
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/flatpickr/4.6.13/flatpickr.min.css">
     <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
        .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }


        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px; 
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
            color: #64748b; 
            font-weight: 500; 
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

         .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-red {
             background-color: rgba(225, 112, 85, 0.1);
            color: var(--danger-color);
        }

        /* 分页样式 */
        .pagination a, .pagination span {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border-radius: 0.375rem;
            color: #64748b;
            transition: all 0.2s;
        }
        .pagination a:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        .pagination .active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 5px rgba(59, 124, 254, 0.3);
        }
        .pagination .disabled {
            color: #cbd5e1;
            cursor: not-allowed;
        }
        /* 日期选择器 */
        .flatpickr-input {
            background-color: white !important;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
         <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">个人信息</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">系统设置</a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
             <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 (当前页面) -->
                <li class="nav-item active">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>

            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">操作日志</span>
        </div>

        <h2 class="text-2xl font-bold text-gray-800 mb-6">操作日志</h2>

        <!-- 筛选区域 -->
        <div class="card mb-6 p-6">
             <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                 <div>
                     <label for="operator" class="block text-sm font-medium text-gray-700 mb-1">操作人</label>
                     <input type="text" id="operator" placeholder="输入操作人用户名" class="w-full">
                 </div>
                 <div>
                     <label for="module" class="block text-sm font-medium text-gray-700 mb-1">操作模块</label>
                     <select id="module" class="w-full">
                         <option value="">全部模块</option>
                         <option value="login">登录/登出</option>
                         <option value="payment_scene">支付场景管理</option>
                         <option value="channel_account">渠道账号管理</option>
                         <option value="merchant">商户号管理</option>
                         <option value="application">应用管理</option>
                         <option value="user">用户管理</option>
                         <!-- 更多模块 -->
                     </select>
                 </div>
                 <div>
                     <label for="action_type" class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
                     <select id="action_type" class="w-full">
                         <option value="">全部类型</option>
                         <option value="create">创建</option>
                         <option value="update">更新</option>
                         <option value="delete">删除</option>
                         <option value="enable">启用</option>
                         <option value="disable">禁用</option>
                         <option value="login">登录</option>
                         <option value="logout">登出</option>
                         <!-- 更多类型 -->
                     </select>
                 </div>
                 <div>
                     <label for="ip_address" class="block text-sm font-medium text-gray-700 mb-1">IP 地址</label>
                     <input type="text" id="ip_address" placeholder="输入IP地址" class="w-full">
                 </div>
                 <div class="lg:col-span-2">
                     <label for="log_time" class="block text-sm font-medium text-gray-700 mb-1">操作时间</label>
                     <input type="text" id="log_time" placeholder="选择日期范围" class="w-full flatpickr-range">
                 </div>
                  <div>
                     <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                     <select id="status" class="w-full">
                         <option value="">全部状态</option>
                         <option value="success">成功</option>
                         <option value="failure">失败</option>
                     </select>
                 </div>
                 <div class="flex items-end space-x-3">
                     <button class="btn btn-primary w-full">
                         <i class="fas fa-search mr-2"></i>搜索
                     </button>
                     <button class="btn btn-light w-full">
                         <i class="fas fa-undo mr-2"></i>重置
                     </button>
                 </div>
             </div>
        </div>

        <!-- 操作日志列表 -->
        <div class="card">
            <div class="table-container overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="table-header">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">操作时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">操作人</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">IP 地址</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">操作模块</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">操作类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider min-w-[300px]">操作内容</th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">状态</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 示例数据行 1 -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">LOG00001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29 10:30:15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">管理员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.101</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">登录/登出</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">登录</td>
                            <td class="px-6 py-4 text-sm text-gray-500">用户 '管理员' 登录成功</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="tag tag-green">成功</span>
                            </td>
                        </tr>
                        <!-- 示例数据行 2 -->
                         <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">LOG00002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29 10:35:22</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">管理员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.101</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">商户号管理</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">创建</td>
                            <td class="px-6 py-4 text-sm text-gray-500">创建了新的商户号: '测试商户A' (MCH004)</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="tag tag-green">成功</span>
                            </td>
                        </tr>
                         <!-- 示例数据行 3 (失败) -->
                         <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">LOG00003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29 10:40:05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">zhangsan_wx</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10.0.0.5</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">渠道账号管理</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">更新</td>
                            <td class="px-6 py-4 text-sm text-gray-500">尝试更新渠道账号 '官方微信支付账号' (CHNACC001) 失败: 权限不足</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="tag tag-red">失败</span>
                            </td>
                        </tr>
                        <!-- 示例数据行 4 -->
                         <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">LOG00004</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29 11:05:18</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">管理员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">192.168.1.101</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">应用管理</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">禁用</td>
                            <td class="px-6 py-4 text-sm text-gray-500">禁用了应用: '测试应用' (AppID: test_app_001)</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="tag tag-green">成功</span>
                            </td>
                        </tr>
                        <!-- 更多数据行... -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">4</span> 条记录
                </div>
                <nav class="pagination" aria-label="Pagination">
                    <span class="disabled">&laquo;</span>
                    <span class="active">1</span>
                    <!-- <a href="#">2</a> -->
                    <span class="disabled">&raquo;</span>
                </nav>
            </div>
        </div>
    </main>

     <!-- 使用国内CDN的 Flatpickr JS -->
    <script src="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/flatpickr/4.6.13/flatpickr.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/flatpickr/4.6.13/l10n/zh.js"></script>
    <script>
         // 初始化日期范围选择器
        flatpickr(".flatpickr-range", {
            mode: "range",
            dateFormat: "Y-m-d",
            locale: "zh" // 使用中文语言包
        });

        // 侧边栏子菜单折叠展开 (与列表页相同逻辑)
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            if(!submenu || !icon) return;
             let isParentActive = item.closest('.nav-item').classList.contains('active');
             let hasActiveChild = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.classList.contains('font-semibold')) {
                     hasActiveChild = true;
                }
            });
            if (isParentActive || hasActiveChild) {
                 submenu.classList.add('active');
                 icon.style.transform = 'rotate(180deg)';
            } else {
                 submenu.classList.remove('active');
                 icon.style.transform = 'rotate(0deg)';
            }
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单 (与列表页相同逻辑)
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

       // 动态设置当前导航项的激活状态 (与列表页相同逻辑，适配当前页面)
        const currentPath = window.location.pathname.split('/').pop();
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
        document.querySelectorAll('.menu-toggle.text-primary').forEach(item => item.classList.remove('text-primary')); // Remove text-primary from toggles initially
        document.querySelectorAll('a.bg-primary-light.text-primary').forEach(item => item.classList.remove('bg-primary-light', 'text-primary')); // Remove highlight from direct links
        document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');

        // Parent map for related pages (Keep empty or add mappings if needed later)
        const parentMap = {
            // Example: 'edit_page.html': 'list_page.html'
        };
        const parentPathForMenuHighlight = parentMap[currentPath];

        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a:not(.submenu a)'); // Get top-level link
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            const menuToggle = navItem.querySelector('.menu-toggle');
            const submenu = navItem.querySelector('.submenu');
            const icon = navItem.querySelector('.submenu-icon');

            let isCurrentPage = false;
            let isCurrentParent = false;
            let activeSubLink = null;

            // Check direct link match
            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                isCurrentPage = true;
            }
            // Check submenu link match
            else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        activeSubLink = subLink; // Store the active sublink
                    }
                });
            }

            // Check if the current page belongs to a parent via parentMap
            if (!isCurrentPage && !isCurrentParent && parentPathForMenuHighlight) {
                 let potentialParentLinkHref = null;
                 // Check if this navItem contains the target parent link (either direct or submenu)
                 if (link && link.getAttribute('href') === parentPathForMenuHighlight) {
                      potentialParentLinkHref = link.getAttribute('href');
                 } else {
                     submenuLinks.forEach(subLink => {
                          if(subLink.getAttribute('href') === parentPathForMenuHighlight) {
                               potentialParentLinkHref = subLink.getAttribute('href');
                          }
                     });
                 }

                 // If this navItem corresponds to the mapped parent
                 if(potentialParentLinkHref) {
                     isCurrentParent = true; // Treat as parent active
                     // Find the actual sublink corresponding to the current page to highlight it
                     submenuLinks.forEach(subLink => {
                          if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                               activeSubLink = subLink;
                          }
                     });
                 }
            }


            // Apply styles based on findings
            if (isCurrentPage) {
                 navItem.classList.add('active');
                 if(link) link.classList.add('bg-primary-light', 'text-primary'); // Highlight direct link
            } else if (isCurrentParent) {
                 navItem.classList.add('active');
                 if (menuToggle) menuToggle.classList.add('text-primary'); // Highlight parent toggle text
                 if (activeSubLink) activeSubLink.classList.add('font-semibold', 'text-primary'); // Highlight specific sublink
                 if (submenu) {
                     submenu.classList.add('active');
                     if(icon) icon.style.transform = 'rotate(180deg)';
                 }
            }
        });
        
         // 示例：表格行悬停效果 (可选)
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseover', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseout', function() {
                this.classList.remove('bg-gray-50');
            });
        });
    </script>
</body>
</html> 
