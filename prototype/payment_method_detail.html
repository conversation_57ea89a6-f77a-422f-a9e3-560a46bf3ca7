<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付方式详情 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 标签页样式 */
        .tab-nav {
            border-bottom: 1px solid var(--border-color);
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .tab-link {
            padding: 1rem 1.5rem;
            color: #64748b;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
        }
        
        .tab-link:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color);
            transform: scaleX(0);
            transition: transform 0.3s;
        }
        
        .tab-link.active {
            color: var(--primary-color);
        }
        
        .tab-link.active:after {
            transform: scaleX(1);
        }

        /* 上传区域样式 */
        .upload-area {
            border: 2px dashed #e0e6ed;
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }
        
        /* 图像预览 */
        .image-preview {
            width: 120px;
            height: 120px;
            border-radius: 0.5rem;
            overflow: hidden;
            position: relative;
            border: 1px solid #e0e6ed;
        }
        
        .image-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .image-actions {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            padding: 0.25rem;
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .image-preview:hover .image-actions {
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- 支付方式管理 -->
                <li class="nav-item active">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-primary rounded-lg bg-primary-light">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 页面标题和操作按钮 -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
                <a href="payment_method_list.html" class="text-gray-500 hover:text-primary mr-4">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h2 class="text-2xl font-bold text-gray-800">支付方式详情</h2>
            </div>
            <div class="flex items-center space-x-3">
                <button class="btn btn-light">
                    <i class="fas fa-times mr-2"></i>取消
                </button>
                <button class="btn btn-primary">
                    <i class="fas fa-save mr-2"></i>保存
                </button>
            </div>
        </div>

        <!-- 标签页导航 -->
        <div class="card">
            <div class="tab-nav">
                <a href="#tab-basic" class="tab-link active" data-tab="tab-basic">基础信息</a>
                <a href="#tab-products" class="tab-link" data-tab="tab-products">关联支付产品</a>
                <a href="#tab-scenes" class="tab-link" data-tab="tab-scenes">关联支付场景</a>
                <a href="#tab-channels" class="tab-link" data-tab="tab-channels">支付渠道</a>
                <a href="#tab-stats" class="tab-link" data-tab="tab-stats">数据统计</a>
            </div>

            <!-- 标签页内容 -->
            <div class="p-6">
                <!-- 基础信息标签页 -->
                <div id="tab-basic" class="tab-content active">
                    <form>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 左侧表单 -->
                            <div>
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">支付方式名称 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full" placeholder="请输入支付方式名称" value="微信支付">
                                    <p class="mt-1 text-xs text-gray-500">支付方式在用户端展示的名称</p>
                                </div>
                                
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">支付方式编码 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full" placeholder="请输入支付方式编码" value="WECHAT_PAY">
                                    <p class="mt-1 text-xs text-gray-500">支付方式的唯一编码，建议使用英文和下划线</p>
                                </div>
                                
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                                    <div class="flex items-center space-x-4">
                                        <label class="inline-flex items-center">
                                            <input type="radio" class="form-radio" name="status" value="1" checked>
                                            <span class="ml-2">启用</span>
                                        </label>
                                        <label class="inline-flex items-center">
                                            <input type="radio" class="form-radio" name="status" value="0">
                                            <span class="ml-2">停用</span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">标题文案</label>
                                    <input type="text" class="w-full" placeholder="请输入标题文案" value="微信支付">
                                    <p class="mt-1 text-xs text-gray-500">展示给用户的标题，如不填则使用支付方式名称</p>
                                </div>
                                
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">富文本信息</label>
                                    <div class="border border-gray-300 rounded-md p-2 bg-white">
                                        <div class="flex items-center space-x-1 border-b border-gray-200 pb-2 mb-2">
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-bold"></i></button>
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-italic"></i></button>
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-underline"></i></button>
                                            <span class="border-r border-gray-300 h-6 mx-1"></span>
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-align-left"></i></button>
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-align-center"></i></button>
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-align-right"></i></button>
                                            <span class="border-r border-gray-300 h-6 mx-1"></span>
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-link"></i></button>
                                            <button type="button" class="p-1 rounded hover:bg-gray-100"><i class="fas fa-image"></i></button>
                                        </div>
                                        <textarea class="w-full h-32 border-0 focus:ring-0 p-0" placeholder="请输入富文本信息，支持HTML格式">微信安全支付，快捷又方便</textarea>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">可选的富文本描述，支持HTML格式</p>
                                </div>
                            </div>
                            
                            <!-- 右侧图标和品牌上传 -->
                            <div>
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-4">图标 <span class="text-red-500">*</span></label>
                                    <div class="flex items-start space-x-4">
                                        <div class="image-preview">
                                            <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/wechatpay.png" alt="Icon Preview">
                                            <div class="image-actions">
                                                <button type="button" class="text-white p-1 hover:text-blue-300"><i class="fas fa-search-plus"></i></button>
                                                <button type="button" class="text-white p-1 hover:text-red-300"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <div class="upload-area cursor-pointer mb-2">
                                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                                                <p class="text-sm text-gray-500">点击或拖拽文件上传</p>
                                                <p class="text-xs text-gray-400 mt-1">支持jpg、png、svg格式，建议尺寸120x120px</p>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="mt-2 text-xs text-gray-500">该图标将在支付列表中显示</p>
                                </div>
                                
                                <!-- <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-4">品牌图片</label>
                                    <div class="flex items-start space-x-4">
                                        <div class="image-preview">
                                            <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/wechatpay-logo.png" alt="Brand Preview">
                                            <div class="image-actions">
                                                <button type="button" class="text-white p-1 hover:text-blue-300"><i class="fas fa-search-plus"></i></button>
                                                <button type="button" class="text-white p-1 hover:text-red-300"><i class="fas fa-trash-alt"></i></button>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <div class="upload-area cursor-pointer mb-2">
                                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                                                <p class="text-sm text-gray-500">点击或拖拽文件上传</p>
                                                <p class="text-xs text-gray-400 mt-1">支持jpg、png、svg格式，建议尺寸240x80px</p>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="mt-2 text-xs text-gray-500">品牌图片可用于详情页或推广展示</p>
                                </div> -->
                                
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                                    <textarea class="w-full h-32" placeholder="请输入备注信息">微信官方支付产品，需要进行微信商户号配置才能使用。</textarea>
                                    <p class="mt-1 text-xs text-gray-500">内部备注信息，不会对外展示</p>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 关联支付产品标签页 -->
                <div id="tab-products" class="tab-content hidden">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">关联支付产品</h3>
                        <p class="text-sm text-gray-500 mb-4">从数据字典中选择支付产品与当前支付方式进行关联，一个支付方式可以关联多个支付产品</p>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">支付产品选择 <span class="text-red-500">*</span></label>
                            <div class="border border-gray-300 rounded-md p-4 bg-white">
                                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                    <label class="flex items-center p-2 border border-gray-200 rounded-md hover:border-primary">
                                        <input type="checkbox" class="form-checkbox text-primary mr-2" checked>
                                        <span>JSAPI支付</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 rounded-md hover:border-primary">
                                        <input type="checkbox" class="form-checkbox text-primary mr-2" checked>
                                        <span>APP支付</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 rounded-md hover:border-primary">
                                        <input type="checkbox" class="form-checkbox text-primary mr-2" checked>
                                        <span>小程序支付</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 rounded-md hover:border-primary">
                                        <input type="checkbox" class="form-checkbox text-primary mr-2">
                                        <span>H5支付</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 rounded-md hover:border-primary">
                                        <input type="checkbox" class="form-checkbox text-primary mr-2">
                                        <span>付款码支付</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 rounded-md hover:border-primary">
                                        <input type="checkbox" class="form-checkbox text-primary mr-2">
                                        <span>Native支付</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">已关联的支付产品</label>
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200 rounded-md">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品名称</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品编码</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联时间</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <tr>
                                            <td class="py-3 px-4">JSAPI支付</td>
                                            <td class="py-3 px-4">JSAPI</td>
                                            <td class="py-3 px-4">2023-06-15 10:35:22</td>
                                            <td class="py-3 px-4">
                                                <button class="text-red-500 hover:text-red-700">
                                                    <i class="fas fa-unlink"></i> 解除关联
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">APP支付</td>
                                            <td class="py-3 px-4">APP</td>
                                            <td class="py-3 px-4">2023-06-15 10:35:22</td>
                                            <td class="py-3 px-4">
                                                <button class="text-red-500 hover:text-red-700">
                                                    <i class="fas fa-unlink"></i> 解除关联
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">小程序支付</td>
                                            <td class="py-3 px-4">MINI</td>
                                            <td class="py-3 px-4">2023-06-15 10:35:22</td>
                                            <td class="py-3 px-4">
                                                <button class="text-red-500 hover:text-red-700">
                                                    <i class="fas fa-unlink"></i> 解除关联
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关联支付场景标签页 -->
                <div id="tab-scenes" class="tab-content hidden">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">关联支付场景</h3>
                        <p class="text-sm text-gray-500 mb-4">查看所有使用该支付方式的支付场景</p>
                        
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-4">
                                <div class="flex items-center space-x-2">
                                    <input type="text" class="border-gray-300 rounded-md" placeholder="搜索场景名称">
                                    <button class="btn btn-primary py-1 px-3">
                                        <i class="fas fa-search mr-1"></i> 搜索
                                    </button>
                                </div>
                                <span class="text-gray-500 text-sm">共 <strong class="text-primary">5</strong> 个场景</span>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="min-w-full bg-white border border-gray-200 rounded-md">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">场景名称</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">场景编码</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前版本</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否默认</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排序位置</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联时间</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <tr>
                                            <td class="py-3 px-4">APP购物场景</td>
                                            <td class="py-3 px-4">APP_SHOPPING</td>
                                            <td class="py-3 px-4">v1.2</td>
                                            <td class="py-3 px-4"><span class="tag tag-green">是</span></td>
                                            <td class="py-3 px-4">1</td>
                                            <td class="py-3 px-4">2023-06-15 15:30:40</td>
                                            <td class="py-3 px-4">
                                                <a href="#" class="text-primary hover:text-primary-dark">
                                                    <i class="fas fa-external-link-alt"></i> 查看场景
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">小程序购物场景</td>
                                            <td class="py-3 px-4">MINI_SHOPPING</td>
                                            <td class="py-3 px-4">v1.1</td>
                                            <td class="py-3 px-4"><span class="tag tag-green">是</span></td>
                                            <td class="py-3 px-4">1</td>
                                            <td class="py-3 px-4">2023-06-20 09:15:22</td>
                                            <td class="py-3 px-4">
                                                <a href="#" class="text-primary hover:text-primary-dark">
                                                    <i class="fas fa-external-link-alt"></i> 查看场景
                                                </a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">H5购物场景</td>
                                            <td class="py-3 px-4">H5_SHOPPING</td>
                                            <td class="py-3 px-4">v1.0</td>
                                            <td class="py-3 px-4"><span class="tag tag-red">否</span></td>
                                            <td class="py-3 px-4">2</td>
                                            <td class="py-3 px-4">2023-06-22 16:33:10</td>
                                            <td class="py-3 px-4">
                                                <a href="#" class="text-primary hover:text-primary-dark">
                                                    <i class="fas fa-external-link-alt"></i> 查看场景
                                                </a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-4 flex justify-between items-center">
                                <span class="text-sm text-gray-500">注：场景关联需要在支付场景管理中配置</span>
                                <div class="flex items-center space-x-1">
                                    <button class="btn btn-light px-3 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-primary px-3 py-1 text-sm">1</button>
                                    <button class="btn btn-light px-3 py-1 text-sm">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 支付渠道标签页 -->
                <div id="tab-channels" class="tab-content hidden">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">支付渠道配置</h3>
                        <p class="text-sm text-gray-500 mb-4">以下为当前支付方式（微信支付）关联的支付渠道及其路由规则。此信息为只读，如需修改请点击对应渠道的 <strong class="text-primary">编辑规则</strong> 链接。</p>
                        
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="font-medium text-gray-700">渠道列表</h4>
                            </div>
                            
                            <div class="overflow-x-auto mb-6">
                                <table class="min-w-full bg-white border border-gray-200 rounded-md">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">渠道名称</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">渠道编码</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">优先级</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权重</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联商户数</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用条件</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <tr>
                                            <td class="py-3 px-4">微信支付官方</td>
                                            <td class="py-3 px-4">WECHAT_OFFICIAL</td>
                                            <td class="py-3 px-4">1</td>
                                            <td class="py-3 px-4">80</td>
                                            <td class="py-3 px-4"><span class="tag tag-green">启用</span></td>
                                            <td class="py-3 px-4">3</td>
                                            <td class="py-3 px-4">
                                                <span class="tag tag-blue">APP支付</span>
                                                <span class="tag tag-blue">小程序支付</span>
                                            </td>
                                            <td class="py-3 px-4">
                                                <div class="flex space-x-2">
                                                    <a href="channel_route_rule_edit.html" class="text-primary hover:text-primary-dark">
                                                        <i class="fas fa-edit"></i> 编辑规则
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">微信支付服务商</td>
                                            <td class="py-3 px-4">WECHAT_SERVICE</td>
                                            <td class="py-3 px-4">2</td>
                                            <td class="py-3 px-4">60</td>
                                            <td class="py-3 px-4"><span class="tag tag-green">启用</span></td>
                                            <td class="py-3 px-4">2</td>
                                            <td class="py-3 px-4">
                                                <span class="tag tag-blue">JSAPI支付</span>
                                            </td>
                                            <td class="py-3 px-4">
                                                <div class="flex space-x-2">
                                                    <a href="channel_route_rule_edit.html" class="text-primary hover:text-primary-dark">
                                                        <i class="fas fa-edit"></i> 编辑规则
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">模拟支付渠道</td>
                                            <td class="py-3 px-4">MOCK_PAY</td>
                                            <td class="py-3 px-4">99</td>
                                            <td class="py-3 px-4">10</td>
                                            <td class="py-3 px-4"><span class="tag tag-gray">测试</span></td>
                                            <td class="py-3 px-4">1</td>
                                            <td class="py-3 px-4">
                                                <span class="tag tag-gray">所有支付产品</span>
                                            </td>
                                            <td class="py-3 px-4">
                                                <div class="flex space-x-2">
                                                    <a href="channel_route_rule_edit.html" class="text-primary hover:text-primary-dark">
                                                        <i class="fas fa-edit"></i> 编辑规则
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据统计标签页 -->
                <div id="tab-stats" class="tab-content hidden">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">数据统计分析</h3>
                        <p class="text-sm text-gray-500 mb-4">查看支付方式的使用频率、成功率和交易数据</p>
                        
                        <!-- 统计过滤器 -->
                        <div class="flex items-center space-x-4 mb-6">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-700">时间范围:</span>
                                <select class="border-gray-300 rounded-md py-1">
                                    <option value="7">近7天</option>
                                    <option value="30" selected>近30天</option>
                                    <option value="90">近90天</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-gray-700">支付产品:</span>
                                <select class="border-gray-300 rounded-md py-1">
                                    <option value="all">全部</option>
                                    <option value="JSAPI">JSAPI支付</option>
                                    <option value="APP">APP支付</option>
                                    <option value="MINI">小程序支付</option>
                                </select>
                            </div>
                            <button class="btn btn-primary py-1 px-3">
                                <i class="fas fa-search mr-1"></i> 查询
                            </button>
                            <button class="btn btn-light py-1 px-3">
                                <i class="fas fa-download mr-1"></i> 导出
                            </button>
                        </div>
                        
                        <!-- 统计卡片 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">交易总量</p>
                                        <p class="text-2xl font-bold text-gray-800">89,732</p>
                                    </div>
                                    <div class="rounded-full bg-blue-100 p-3">
                                        <i class="fas fa-shopping-cart text-blue-500"></i>
                                    </div>
                                </div>
                                <p class="text-xs text-green-500 mt-2">
                                    <i class="fas fa-arrow-up"></i> 12.5% 较上期
                                </p>
                            </div>
                            
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">交易金额</p>
                                        <p class="text-2xl font-bold text-gray-800">¥1,384,290</p>
                                    </div>
                                    <div class="rounded-full bg-green-100 p-3">
                                        <i class="fas fa-yuan-sign text-green-500"></i>
                                    </div>
                                </div>
                                <p class="text-xs text-green-500 mt-2">
                                    <i class="fas fa-arrow-up"></i> 8.3% 较上期
                                </p>
                            </div>
                            
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">成功率</p>
                                        <p class="text-2xl font-bold text-gray-800">99.6%</p>
                                    </div>
                                    <div class="rounded-full bg-purple-100 p-3">
                                        <i class="fas fa-check-circle text-purple-500"></i>
                                    </div>
                                </div>
                                <p class="text-xs text-green-500 mt-2">
                                    <i class="fas fa-arrow-up"></i> 0.2% 较上期
                                </p>
                            </div>
                            
                            <div class="bg-white p-4 rounded-lg shadow">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-gray-500">平均响应时间</p>
                                        <p class="text-2xl font-bold text-gray-800">320ms</p>
                                    </div>
                                    <div class="rounded-full bg-yellow-100 p-3">
                                        <i class="fas fa-clock text-yellow-500"></i>
                                    </div>
                                </div>
                                <p class="text-xs text-red-500 mt-2">
                                    <i class="fas fa-arrow-up"></i> 15ms 较上期
                                </p>
                            </div>
                        </div>
                        
                        <!-- 图表区域 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div class="bg-white p-4 rounded-lg shadow">
                                <h4 class="font-medium text-gray-700 mb-4">交易量趋势</h4>
                                <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
                                    <p class="text-gray-500">交易量趋势图表</p>
                                </div>
                            </div>
                            
                            <div class="bg-white p-4 rounded-lg shadow">
                                <h4 class="font-medium text-gray-700 mb-4">成功率趋势</h4>
                                <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
                                    <p class="text-gray-500">成功率趋势图表</p>
                                </div>
                            </div>
                            
                            <div class="bg-white p-4 rounded-lg shadow">
                                <h4 class="font-medium text-gray-700 mb-4">支付产品分布</h4>
                                <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
                                    <p class="text-gray-500">支付产品分布图表</p>
                                </div>
                            </div>
                            
                            <div class="bg-white p-4 rounded-lg shadow">
                                <h4 class="font-medium text-gray-700 mb-4">渠道使用情况</h4>
                                <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
                                    <p class="text-gray-500">渠道使用情况图表</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 详细数据表格 -->
                        <div class="bg-white p-4 rounded-lg shadow">
                            <h4 class="font-medium text-gray-700 mb-4">详细数据记录</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易量</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易金额</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成功率</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平均响应时间</th>
                                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">费率成本</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        <tr>
                                            <td class="py-3 px-4">2023-06-25</td>
                                            <td class="py-3 px-4">3,201</td>
                                            <td class="py-3 px-4">¥48,235.60</td>
                                            <td class="py-3 px-4">99.7%</td>
                                            <td class="py-3 px-4">315ms</td>
                                            <td class="py-3 px-4">¥241.18</td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">2023-06-24</td>
                                            <td class="py-3 px-4">3,156</td>
                                            <td class="py-3 px-4">¥46,982.40</td>
                                            <td class="py-3 px-4">99.5%</td>
                                            <td class="py-3 px-4">310ms</td>
                                            <td class="py-3 px-4">¥234.91</td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">2023-06-23</td>
                                            <td class="py-3 px-4">3,089</td>
                                            <td class="py-3 px-4">¥45,420.30</td>
                                            <td class="py-3 px-4">99.8%</td>
                                            <td class="py-3 px-4">305ms</td>
                                            <td class="py-3 px-4">¥227.10</td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">2023-06-22</td>
                                            <td class="py-3 px-4">2,958</td>
                                            <td class="py-3 px-4">¥43,265.80</td>
                                            <td class="py-3 px-4">99.6%</td>
                                            <td class="py-3 px-4">322ms</td>
                                            <td class="py-3 px-4">¥216.33</td>
                                        </tr>
                                        <tr>
                                            <td class="py-3 px-4">2023-06-21</td>
                                            <td class="py-3 px-4">3,045</td>
                                            <td class="py-3 px-4">¥44,780.50</td>
                                            <td class="py-3 px-4">99.4%</td>
                                            <td class="py-3 px-4">318ms</td>
                                            <td class="py-3 px-4">¥223.90</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <div class="mt-4 flex justify-between items-center">
                                <span class="text-sm text-gray-500">显示 1-5 条，共 30 条</span>
                                <div class="flex items-center space-x-1">
                                    <button class="btn btn-light px-3 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                                        <i class="fas fa-chevron-left"></i>
                                    </button>
                                    <button class="btn btn-primary px-3 py-1 text-sm">1</button>
                                    <button class="btn btn-light px-3 py-1 text-sm">2</button>
                                    <button class="btn btn-light px-3 py-1 text-sm">3</button>
                                    <button class="btn btn-light px-3 py-1 text-sm">
                                        <i class="fas fa-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 标签页切换
        document.querySelectorAll('.tab-link').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有标签页的active类
                document.querySelectorAll('.tab-link').forEach(t => {
                    t.classList.remove('active');
                });
                
                // 隐藏所有内容区域
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                
                // 激活当前标签页
                this.classList.add('active');
                
                // 显示对应内容
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.remove('hidden');
            });
        });

        // 动态设置页面主题颜色
        document.documentElement.style.setProperty('--primary-color', '#3b7cfe');
        document.documentElement.style.setProperty('--primary-dark', '#2a5cb9');
        document.documentElement.style.setProperty('--primary-light', '#e8f0ff');
        document.documentElement.style.setProperty('--secondary-color', '#6c5ce7');

        // 上传区域交互
        document.querySelectorAll('.upload-area').forEach(area => {
            area.addEventListener('click', function() {
                alert('此处应打开文件选择器');
            });
            
            area.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.style.borderColor = 'var(--primary-color)';
                this.style.backgroundColor = 'var(--primary-light)';
            });
            
            area.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.style.borderColor = '';
                this.style.backgroundColor = '';
            });
            
            area.addEventListener('drop', function(e) {
                e.preventDefault();
                this.style.borderColor = '';
                this.style.backgroundColor = '';
                alert('文件已上传');
            });
        });
    </script>
</body>
</html> 
