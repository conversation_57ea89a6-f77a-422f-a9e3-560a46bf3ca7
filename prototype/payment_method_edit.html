<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑支付方式 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }

        /* 文件上传 */
        .file-upload-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .file-upload-wrapper input[type=file] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 支付方式管理 (当前页面，需要高亮) -->
                <li class="nav-item active">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="payment_method_list.html" class="hover:text-primary">支付方式管理</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">编辑支付方式</span>
        </div>

        <h2 class="text-2xl font-bold text-gray-800 mb-6">编辑支付方式</h2>

        <div class="card p-8">
            <form action="#" method="POST">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 支付方式名称 -->
                    <div>
                        <label for="payment-name" class="block text-sm font-medium text-gray-700 mb-1">支付方式名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="payment-name" name="payment-name" value="微信支付" required class="w-full" placeholder="例如：微信支付">
                    </div>

                    <!-- 支付方式代码 -->
                    <div>
                        <label for="payment-code" class="block text-sm font-medium text-gray-700 mb-1">支付方式代码 <span class="text-red-500">*</span></label>
                        <input type="text" id="payment-code" name="payment-code" value="WECHAT_PAY" required class="w-full bg-gray-100" placeholder="例如：WECHAT_PAY" readonly>
                        <p class="text-xs text-gray-500 mt-1">代码全局唯一，创建后不可修改。</p>
                    </div>

                    <!-- 支付方式描述 -->
                    <div class="md:col-span-2">
                        <label for="payment-description" class="block text-sm font-medium text-gray-700 mb-1">描述</label>
                        <textarea id="payment-description" name="payment-description" rows="3" class="w-full" placeholder="支付方式的详细描述">微信官方支付接口</textarea>
                    </div>

                    <!-- 状态 -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态 <span class="text-red-500">*</span></label>
                        <select id="status" name="status" required class="w-full">
                            <option value="active" selected>启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>

                    <!-- 排序 -->
                    <div>
                        <label for="sort-order" class="block text-sm font-medium text-gray-700 mb-1">排序</label>
                        <input type="number" id="sort-order" name="sort-order" value="1" class="w-full" placeholder="数字越小越靠前">
                    </div>

                    <!-- 图标上传 -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">图标</label>
                        <div class="flex items-center space-x-4">
                            <img id="icon-preview" src="https://img.yzcdn.cn/vant/logo.png" alt="当前图标" class="h-16 w-16 rounded-md object-contain border border-gray-200 p-1">
                            <div class="file-upload-wrapper">
                                <button type="button" class="btn btn-light">更换图标</button>
                                <input type="file" id="icon-upload" name="icon-upload" accept="image/*" onchange="previewIcon(event)">
                            </div>
                            <span id="file-name" class="text-sm text-gray-500"></span>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">建议尺寸 128x128px，支持 PNG, JPG, SVG 格式。</p>
                    </div>
                    
                    <!-- 支持的货币 (示例，实际可能需要更复杂的选择) -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">支持的货币</label>
                        <div class="flex flex-wrap gap-2">
                            <span class="tag tag-blue">CNY <button type="button" class="ml-1 text-xs font-bold">×</button></span>
                            <span class="tag tag-green">USD <button type="button" class="ml-1 text-xs font-bold">×</button></span>
                            <button type="button" class="text-sm text-primary hover:underline">+ 添加货币</button>
                        </div>
                    </div>

                    <!-- 配置信息 (示例，实际可能需要更复杂的配置项) -->
                    <div class="md:col-span-2">
                        <h3 class="text-lg font-medium text-gray-900 mb-3 border-b pb-2">支付配置</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="config-appid" class="block text-sm font-medium text-gray-700 mb-1">App ID</label>
                                <input type="text" id="config-appid" name="config-appid" value="wx1234567890abcdef" class="w-full">
                            </div>
                             <div>
                                <label for="config-mchid" class="block text-sm font-medium text-gray-700 mb-1">商户号 (Mch ID)</label>
                                <input type="text" id="config-mchid" name="config-mchid" value="1234567890" class="w-full">
                            </div>
                             <div class="md:col-span-2">
                                <label for="config-apikey" class="block text-sm font-medium text-gray-700 mb-1">API 密钥</label>
                                <input type="password" id="config-apikey" name="config-apikey" value="abcdefghijklmnopqrstuvwxyz123456" class="w-full">
                            </div>
                            <div class="md:col-span-2">
                                <label for="config-notifyurl" class="block text-sm font-medium text-gray-700 mb-1">回调通知地址</label>
                                <input type="url" id="config-notifyurl" name="config-notifyurl" value="https://api.example.com/notify/wechat" class="w-full">
                            </div>
                        </div>
                    </div>

                </div>

                <!-- 操作按钮 -->
                <div class="mt-8 pt-6 border-t border-gray-200 flex justify-end space-x-3">
                    <a href="payment_method_list.html" class="btn btn-light">取消</a>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            // 检查当前菜单项是否应该展开 (基于页面URL或active class)
            let isActive = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.href === window.location.href) {
                    isActive = true;
                    link.classList.add('font-semibold', 'text-primary'); // 高亮当前子菜单项
                }
            });

            // 如果子菜单包含活动项，则展开
            if (isActive || item.closest('.nav-item').classList.contains('active')) {
                 // 检查父菜单项是否已标记为active
                if (item.closest('.nav-item').classList.contains('active')) {
                    // 这里不自动展开，除非是当前页面所在的父菜单
                } else if (isActive) {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            }
            
            // 添加点击事件
            item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    // 关闭其他打开的子菜单
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            openSubmenu.previousElementSibling.querySelector('.submenu-icon').style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 动态设置当前导航项的激活状态
        const currentPath = window.location.pathname.split('/').pop();
        document.querySelectorAll('.nav-item a').forEach(link => {
            const linkPath = link.getAttribute('href').split('/').pop();
            const parentNavItem = link.closest('.nav-item');
            if (linkPath === currentPath) {
                parentNavItem.classList.add('active');
                 // 如果是子菜单项，展开父菜单
                 const submenu = link.closest('.submenu');
                 if (submenu) {
                     submenu.classList.add('active');
                     const menuToggle = submenu.previousElementSibling;
                     if (menuToggle && menuToggle.classList.contains('menu-toggle')) {
                         menuToggle.querySelector('.submenu-icon').style.transform = 'rotate(180deg)';
                     }
                     // 同时给父菜单项也加上active，但样式上可能需要调整以区分
                     const parentMenuItem = submenu.closest('.nav-item');
                     if(parentMenuItem) parentMenuItem.classList.add('active');
                 }
            } else {
                // 移除其他非直接父级的 active 类，防止多个父菜单高亮
                if(!parentNavItem.contains(document.querySelector(`.nav-item a[href$="${currentPath}"]`))){
                    // parentNavItem.classList.remove('active'); // 暂时注释掉，因为模板的逻辑就是给父菜单加active
                }
            }
        });
        // 处理父菜单的 active 状态
        document.querySelectorAll('.nav-item').forEach(navItem => {
            if (!navItem.querySelector('a') && navItem.querySelector('.submenu.active')) {
                // 对于包含展开子菜单的父菜单项，确保它有 active 样式
                 navItem.classList.add('active');
            }
            // 如果是当前页面的父菜单项，但不是直接链接，也添加active
            if (navItem.querySelector(`.submenu a[href$="${currentPath}"]`)){
                 navItem.classList.add('active');
            }
        });

        // 图标预览功能
        function previewIcon(event) {
            const reader = new FileReader();
            const preview = document.getElementById('icon-preview');
            const fileNameSpan = document.getElementById('file-name');
            reader.onload = function(){
                if (reader.readyState == 2) {
                    preview.src = reader.result;
                }
            }
            if(event.target.files[0]){
                reader.readAsDataURL(event.target.files[0]);
                 fileNameSpan.textContent = event.target.files[0].name;
            } else {
                 // 如果用户取消选择，恢复默认或之前的图标
                 // preview.src = 'default-icon.png'; // 或者之前的图标URL
                 fileNameSpan.textContent = "";
            }
        }

    </script>
</body>
</html> 
