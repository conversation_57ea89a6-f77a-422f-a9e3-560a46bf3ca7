<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付方式过滤条件配置 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 开关组件 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: var(--primary-color);
        }
        
        input:focus + .toggle-slider {
            box-shadow: 0 0 1px var(--primary-color);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        /* 添加支付方式过滤条件的特定样式 */
        .payment-filter-item {
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            transition: all 0.3s;
        }
        
        .payment-filter-item:hover {
            border-color: var(--primary-color);
        }
        
        .payment-method-icon {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .filter-tags-container {
            display: flex;
            flex-wrap: wrap;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="header fixed top-0 left-0 right-0 h-16 flex items-center justify-between px-6 z-30">
        <div class="flex items-center">
            <div class="text-xl font-bold">收银台及渠道管理系统</div>
        </div>
        <div class="flex items-center space-x-4">
            <div class="relative">
                <button class="flex items-center text-white focus:outline-none">
                    <span class="mr-2">管理员</span>
                    <img src="https://via.placeholder.com/36" alt="用户头像" class="w-8 h-8 rounded-full">
                </button>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed top-0 left-0 bottom-0 pt-16 pb-4 px-4 overflow-y-auto">
        <nav class="mt-8">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <a href="payment_scene.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-layer-group w-5 h-5 text-center"></i>
                        <span class="ml-3">支付场景管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <a href="channel_route_rules.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-random w-5 h-5 text-center"></i>
                        <span class="ml-3">渠道路由管理</span>
                    </a>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 支付方式管理 -->
                <li class="nav-item active">
                    <a href="#" class="flex items-center justify-between px-4 py-3 text-primary rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                            <span class="ml-3">支付方式管理</span>
                        </div>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </a>
                    <ul class="submenu active pl-9 pr-2">
                        <li class="py-2">
                            <a href="payment_method_list.html" class="text-gray-700 hover:text-primary block transition-colors">
                                支付方式列表
                            </a>
                        </li>
                        <li class="py-2">
                            <a href="payment_method_filter.html" class="text-primary font-medium block transition-colors">
                                过滤条件配置
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 商户管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <a href="channel_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-university w-5 h-5 text-center"></i>
                        <span class="ml-3">渠道账号管理</span>
                    </a>
                </li>

                <!-- 渠道员工管理 -->
                <li class="nav-item">
                    <a href="channel_employee.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-users w-5 h-5 text-center"></i>
                        <span class="ml-3">渠道员工管理</span>
                    </a>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <a href="#" class="flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="fas fa-chevron-down text-xs"></i>
                    </a>
                    <ul class="submenu pl-9 pr-2">
                        <li class="py-2">
                            <a href="condition_module.html" class="text-gray-700 hover:text-primary block transition-colors">
                                条件模块
                            </a>
                        </li>
                        <li class="py-2">
                            <a href="condition_rule.html" class="text-gray-700 hover:text-primary block transition-colors">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- 字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">字典管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>

                <!-- 用户管理 -->
                <li class="nav-item">
                    <a href="user_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-user-cog w-5 h-5 text-center"></i>
                        <span class="ml-3">用户管理</span>
                    </a>
                </li>

                <!-- 角色管理 -->
                <li class="nav-item">
                    <a href="role_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-user-tag w-5 h-5 text-center"></i>
                        <span class="ml-3">角色管理</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区域 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <a href="payment_method_list.html" class="hover:text-primary">支付方式管理</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">过滤条件配置</span>
        </div>

        <div class="mb-6 flex justify-between items-center">
            <h2 class="text-xl font-bold text-gray-800">支付方式过滤条件配置</h2>
            <button id="addPaymentMethodBtn" class="btn btn-primary flex items-center">
                <i class="fas fa-plus mr-2"></i>
                <span>添加支付方式</span>
            </button>
        </div>

        <!-- 过滤条件配置卡片 -->
        <div class="card mb-6">
            <div class="p-6">
                <h3 class="text-lg font-medium text-gray-800 mb-4">过滤条件配置</h3>
                <div class="bg-gray-50 p-4 rounded-md mb-4">
                    <div class="flex items-center text-gray-700">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        <span>通过配置过滤条件，可以根据不同场景控制支付方式的展示规则。</span>
                    </div>
                </div>
                
                <!-- 支付方式过滤条件列表 -->
                <div class="space-y-6">
                    <!-- 支付宝 -->
                    <div class="payment-filter-item rounded-lg overflow-hidden">
                        <div class="p-4 bg-white">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="payment-method-icon bg-blue-100 text-blue-600 mr-3">
                                        <i class="fab fa-alipay text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">支付宝</h4>
                                        <p class="text-xs text-gray-500">ALIPAY</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="flex items-center mr-4">
                                        <label class="mr-2 text-sm text-gray-600">启用先乘后付功能</label>
                                        <label class="toggle-switch">
                                            <input type="checkbox">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <div class="filter-tags-container">
                                    <div class="tag tag-blue">
                                        <span>用户支付金额过滤</span>
                                    </div>
                                    <div class="tag tag-green">
                                        <span>行程时间段过滤</span>
                                    </div>
                                    <div class="tag tag-purple">
                                        <span>用户信用等级过滤</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 微信支付 -->
                    <div class="payment-filter-item rounded-lg overflow-hidden">
                        <div class="p-4 bg-white">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="payment-method-icon bg-green-100 text-green-600 mr-3">
                                        <i class="fab fa-weixin text-xl"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">微信支付</h4>
                                        <p class="text-xs text-gray-500">WECHAT_PAY</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <div class="flex items-center mr-4">
                                        <label class="mr-2 text-sm text-gray-600">启用先乘后付功能</label>
                                        <label class="toggle-switch">
                                            <input type="checkbox">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <button class="btn btn-primary flex items-center text-sm" id="addFilterBtn">
                                        <i class="fas fa-plus mr-1"></i>
                                        <span>添加过滤条件</span>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 微信支付暂无过滤条件 -->
                        </div>
                    </div>
                    
                </div>
                
            </div>
        </div>

    </main>

    <!-- 添加过滤条件模态框 -->
    <div id="filterModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg w-full max-w-2xl">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-800">添加过滤条件</h3>
                    <button id="closeFilterModal" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="filterType">
                        过滤条件类型
                    </label>
                    <select class="appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline" id="filterType">
                        <option value="">请选择过滤条件类型</option>
                        <option value="userAmount">用户支付金额过滤</option>
                        <option value="tripTime">行程时间段过滤</option>
                        <option value="userCredit">用户信用等级过滤</option>
                        <option value="userLocation">用户地理位置过滤</option>
                        <option value="deviceType">设备类型过滤</option>
                    </select>
                </div>
                
                <!-- 用户支付金额过滤设置 -->
                <div id="userAmountFilter" class="filter-option hidden">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">金额范围</label>
                        <div class="flex items-center">
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 mr-2 leading-tight focus:outline-none focus:shadow-outline" type="number" placeholder="最小金额">
                            <span class="mx-2">-</span>
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" type="number" placeholder="最大金额">
                        </div>
                    </div>
                </div>
                
                <!-- 行程时间段过滤设置 -->
                <div id="tripTimeFilter" class="filter-option hidden">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">时间段</label>
                        <div class="flex items-center">
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 mr-2 leading-tight focus:outline-none focus:shadow-outline" type="time">
                            <span class="mx-2">-</span>
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" type="time">
                        </div>
                    </div>
                </div>
                
                <!-- 用户信用等级过滤设置 -->
                <div id="userCreditFilter" class="filter-option hidden">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">最低信用等级</label>
                        <select class="appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="1">一星</option>
                            <option value="2">二星</option>
                            <option value="3">三星</option>
                            <option value="4">四星</option>
                            <option value="5">五星</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                <button id="cancelFilter" class="btn btn-light mr-3">取消</button>
                <button id="saveFilter" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 添加支付方式模态框 -->
    <div id="paymentMethodModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg w-full max-w-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-800">添加支付方式</h3>
                    <button id="closePaymentModal" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="paymentMethod">
                        支付方式
                    </label>
                    <select class="appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline" id="paymentMethod">
                        <option value="">请选择支付方式</option>
                        <option value="unionpay">银联支付</option>
                        <option value="apple">Apple Pay</option>
                        <option value="huawei">华为支付</option>
                        <option value="jd">京东支付</option>
                    </select>
                </div>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                <button id="cancelPayment" class="btn btn-light mr-3">取消</button>
                <button id="savePayment" class="btn btn-primary">添加</button>
            </div>
        </div>
    </div>

    <!-- JavaScript 脚本 -->
    <script>
        // 添加过滤条件按钮点击事件
        document.getElementById('addFilterBtn').addEventListener('click', function() {
            document.getElementById('filterModal').classList.remove('hidden');
        });
        
        // 关闭过滤条件模态框
        document.getElementById('closeFilterModal').addEventListener('click', function() {
            document.getElementById('filterModal').classList.add('hidden');
        });
        
        document.getElementById('cancelFilter').addEventListener('click', function() {
            document.getElementById('filterModal').classList.add('hidden');
        });
        
        // 添加支付方式按钮点击事件
        document.getElementById('addPaymentMethodBtn').addEventListener('click', function() {
            document.getElementById('paymentMethodModal').classList.remove('hidden');
        });
        
        // 关闭添加支付方式模态框
        document.getElementById('closePaymentModal').addEventListener('click', function() {
            document.getElementById('paymentMethodModal').classList.add('hidden');
        });
        
        document.getElementById('cancelPayment').addEventListener('click', function() {
            document.getElementById('paymentMethodModal').classList.add('hidden');
        });
        
        // 过滤条件类型选择变化事件
        document.getElementById('filterType').addEventListener('change', function() {
            // 隐藏所有过滤选项
            document.querySelectorAll('.filter-option').forEach(function(element) {
                element.classList.add('hidden');
            });
            
            // 显示选中的过滤选项
            const selectedFilter = this.value;
            if (selectedFilter) {
                document.getElementById(`${selectedFilter}Filter`).classList.remove('hidden');
            }
        });
        
        // 保存过滤条件
        document.getElementById('saveFilter').addEventListener('click', function() {
            // 此处添加保存逻辑
            
            // 模拟添加新标签
            const filterType = document.getElementById('filterType').value;
            const filterNames = {
                'userAmount': '用户支付金额过滤',
                'tripTime': '行程时间段过滤',
                'userCredit': '用户信用等级过滤',
                'userLocation': '用户地理位置过滤',
                'deviceType': '设备类型过滤'
            };
            
            if (filterType) {
                const tagColors = {
                    'userAmount': 'blue',
                    'tripTime': 'green',
                    'userCredit': 'purple',
                    'userLocation': 'yellow',
                    'deviceType': 'red'
                };
                
                // 获取微信支付区域
                const wechatPayFilter = document.querySelector('.payment-filter-item:nth-child(2)');
                
                // 如果没有过滤标签容器，则创建一个
                let filterTagsContainer = wechatPayFilter.querySelector('.filter-tags-container');
                if (!filterTagsContainer) {
                    filterTagsContainer = document.createElement('div');
                    filterTagsContainer.className = 'filter-tags-container mt-4';
                    wechatPayFilter.querySelector('.p-4').appendChild(filterTagsContainer);
                }
                
                // 创建新标签
                const newTag = document.createElement('div');
                newTag.className = `tag tag-${tagColors[filterType]}`;
                newTag.innerHTML = `<span>${filterNames[filterType]}</span>`;
                
                // 添加到容器
                filterTagsContainer.appendChild(newTag);
                
                // 关闭模态框
                document.getElementById('filterModal').classList.add('hidden');
                
                // 重置表单
                document.getElementById('filterType').value = '';
                document.querySelectorAll('.filter-option').forEach(function(element) {
                    element.classList.add('hidden');
                });
            }
        });
        
        // 保存支付方式
        document.getElementById('savePayment').addEventListener('click', function() {
            // 此处添加保存逻辑
            
            // 模拟添加新支付方式
            const paymentMethod = document.getElementById('paymentMethod').value;
            const paymentNames = {
                'unionpay': '银联支付',
                'apple': 'Apple Pay',
                'huawei': '华为支付',
                'jd': '京东支付'
            };
            
            const paymentCodes = {
                'unionpay': 'UNIONPAY',
                'apple': 'APPLE_PAY',
                'huawei': 'HUAWEI_PAY',
                'jd': 'JD_PAY'
            };
            
            const paymentIcons = {
                'unionpay': '<i class="fab fa-cc-visa text-xl"></i>',
                'apple': '<i class="fab fa-apple-pay text-xl"></i>',
                'huawei': '<i class="fas fa-mobile-alt text-xl"></i>',
                'jd': '<i class="fas fa-shopping-cart text-xl"></i>'
            };
            
            if (paymentMethod) {
                // 获取支付方式列表容器
                const paymentContainer = document.querySelector('.space-y-6');
                
                // 创建新支付方式区域
                const newPaymentMethod = document.createElement('div');
                newPaymentMethod.className = 'payment-filter-item rounded-lg overflow-hidden';
                newPaymentMethod.innerHTML = `
                    <div class="p-4 bg-white">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="payment-method-icon bg-gray-100 text-gray-600 mr-3">
                                    ${paymentIcons[paymentMethod]}
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">${paymentNames[paymentMethod]}</h4>
                                    <p class="text-xs text-gray-500">${paymentCodes[paymentMethod]}</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="flex items-center mr-4">
                                    <label class="mr-2 text-sm text-gray-600">启用先乘后付功能</label>
                                    <label class="toggle-switch">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                                <button class="btn btn-primary flex items-center text-sm add-filter-btn">
                                    <i class="fas fa-plus mr-1"></i>
                                    <span>添加过滤条件</span>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到容器
                paymentContainer.appendChild(newPaymentMethod);
                
                // 为新添加的按钮绑定事件
                const newAddFilterBtn = newPaymentMethod.querySelector('.add-filter-btn');
                newAddFilterBtn.addEventListener('click', function() {
                    document.getElementById('filterModal').classList.remove('hidden');
                });
                
                // 关闭模态框
                document.getElementById('paymentMethodModal').classList.add('hidden');
                
                // 重置表单
                document.getElementById('paymentMethod').value = '';
            }
        });
    </script>
</body>
</html> 