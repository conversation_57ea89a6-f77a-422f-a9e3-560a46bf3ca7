<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付方式管理 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 添加 Chart.js CDN -->
    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/Chart.js/3.7.1/chart.min.js"></script>
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 开关组件 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: var(--success-color);
        }
        
        input:focus + .toggle-slider {
            box-shadow: 0 0 1px var(--success-color);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="images/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="images/avatar.png" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item active">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-primary rounded-lg bg-primary-light">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="#" class="block py-2 text-gray-400 hover:text-primary cursor-not-allowed">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 页面标题和操作按钮 -->
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">支付方式管理</h2>
            <a href="payment_method_detail.html" class="btn btn-primary flex items-center">
                <i class="fas fa-plus mr-2"></i>新增支付方式
            </a>
        </div>

        <!-- 搜索和筛选区域 -->
        <div class="card p-6 mb-6">
            <form class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">支付方式名称</label>
                    <input type="text" class="w-full" placeholder="请输入支付方式名称">
                </div>
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">支付方式编码</label>
                    <input type="text" class="w-full" placeholder="请输入支付方式编码">
                </div>
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select class="w-full">
                        <option value="">全部</option>
                        <option value="1">启用</option>
                        <option value="0">停用</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">关联支付产品</label>
                    <select class="w-full">
                        <option value="">全部</option>
                        <option value="JSAPI">JSAPI</option>
                        <option value="APP">APP支付</option>
                        <option value="MINI">MINI支付</option>
                        <option value="NATIVE">扫码支付</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                    <div class="flex items-center space-x-2">
                        <input type="date" class="w-full" placeholder="开始日期">
                        <span>至</span>
                        <input type="date" class="w-full" placeholder="结束日期">
                    </div>
                </div>
                <div class="form-group flex items-end space-x-2">
                    <button type="button" class="btn btn-primary flex-1">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                    <button type="button" class="btn btn-light flex-1">
                        <i class="fas fa-redo mr-2"></i>重置
                    </button>
                </div>
            </form>
        </div>

        <!-- 数据列表 -->
        <div class="card">
            <!-- 批量操作工具栏 -->
            <div class="px-6 py-4 border-b border-gray-100 flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <button class="btn btn-light flex items-center text-sm">
                        <i class="fas fa-file-export mr-1"></i>导出
                    </button>
                    <button class="btn btn-light flex items-center text-sm">
                        <i class="fas fa-sync-alt mr-1"></i>刷新
                    </button>
                </div>
                <div>
                    <span class="text-gray-500 text-sm">共 <strong class="text-primary">12</strong> 条记录</span>
                </div>
            </div>

            <!-- 表格 -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="table-header">
                        <tr>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-primary rounded">
                            </th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">支付方式</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编码</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联支付产品</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联场景</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联渠道</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <!-- 微信支付 -->
                        <tr class="hover:bg-gray-50" data-payment-method="WECHAT_PAY" data-payment-name="微信支付">
                            <td class="py-3 px-4">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-primary rounded">
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center">
                                    <img src="images/wechat.png" class="h-8 w-8 rounded mr-3" alt="微信支付">
                                    <span class="font-medium">微信支付</span>
                                </div>
                            </td>
                            <td class="py-3 px-4 text-gray-500">WECHAT_PAY</td>
                            <td class="py-3 px-4">
                                <span class="tag tag-green">启用</span>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex flex-wrap gap-1">
                                    <span class="tag tag-blue">JSAPI</span>
                                    <span class="tag tag-blue">APP支付</span>
                                    <span class="tag tag-blue">小程序支付</span>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-purple">5个场景</span>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-yellow">2个渠道</span>
                            </td>
                            <td class="py-3 px-4 text-gray-500">2023-06-15 10:30:45</td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-2">
                                    <a href="payment_method_detail.html" class="text-primary hover:text-primary-dark" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="payment_method_trend.html?method=WECHAT_PAY&name=微信支付" class="text-blue-500 hover:text-blue-700" title="查看趋势">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                    <label class="toggle-switch" title="状态切换">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <!-- 支付宝 -->
                        <tr class="hover:bg-gray-50" data-payment-method="ALIPAY" data-payment-name="支付宝">
                            <td class="py-3 px-4">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-primary rounded">
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center">
                                    <img src="images/alipay.png" class="h-8 w-8 rounded mr-3" alt="支付宝">
                                    <span class="font-medium">支付宝</span>
                                </div>
                            </td>
                            <td class="py-3 px-4 text-gray-500">ALIPAY</td>
                            <td class="py-3 px-4">
                                <span class="tag tag-green">启用</span>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex flex-wrap gap-1">
                                    <span class="tag tag-blue">手机网站支付</span>
                                    <span class="tag tag-blue">APP支付</span>
                                    <span class="tag tag-blue">当面付</span>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-purple">4个场景</span>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-yellow">1个渠道</span>
                            </td>
                            <td class="py-3 px-4 text-gray-500">2023-06-16 14:20:32</td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-2">
                                    <a href="payment_method_detail.html" class="text-primary hover:text-primary-dark" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="payment_method_trend.html?method=ALIPAY&name=支付宝" class="text-blue-500 hover:text-blue-700" title="查看趋势">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                    <label class="toggle-switch" title="状态切换">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <!-- 银联支付 -->
                        <tr class="hover:bg-gray-50" data-payment-method="UNION_PAY" data-payment-name="银联支付">
                            <td class="py-3 px-4">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-primary rounded">
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center">
                                    <img src="images/unionpay.png" class="h-8 w-8 rounded mr-3" alt="银联支付">
                                    <span class="font-medium">银联支付</span>
                                </div>
                            </td>
                            <td class="py-3 px-4 text-gray-500">UNION_PAY</td>
                            <td class="py-3 px-4">
                                <span class="tag tag-green">启用</span>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex flex-wrap gap-1">
                                    <span class="tag tag-blue">网关支付</span>
                                    <span class="tag tag-blue">APP支付</span>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-purple">3个场景</span>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-yellow">1个渠道</span>
                            </td>
                            <td class="py-3 px-4 text-gray-500">2023-06-20 09:15:18</td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-2">
                                    <a href="payment_method_detail.html" class="text-primary hover:text-primary-dark" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="payment_method_trend.html?method=UNION_PAY&name=银联支付" class="text-blue-500 hover:text-blue-700" title="查看趋势">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                    <label class="toggle-switch" title="状态切换">
                                        <input type="checkbox" checked>
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </td>
                        </tr>
                        <!-- 花呗分期 -->
                        <tr class="hover:bg-gray-50" data-payment-method="HUABEI" data-payment-name="花呗分期">
                            <td class="py-3 px-4">
                                <input type="checkbox" class="form-checkbox h-4 w-4 text-primary rounded">
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex items-center">
                                    <img src="images/huabei.png" class="h-8 w-8 rounded mr-3" alt="花呗分期">
                                    <span class="font-medium">花呗分期</span>
                                </div>
                            </td>
                            <td class="py-3 px-4 text-gray-500">HUABEI</td>
                            <td class="py-3 px-4">
                                <span class="tag tag-red">停用</span>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex flex-wrap gap-1">
                                    <span class="tag tag-blue">花呗分期</span>
                                </div>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-purple">2个场景</span>
                            </td>
                            <td class="py-3 px-4">
                                <span class="tag tag-yellow">1个渠道</span>
                            </td>
                            <td class="py-3 px-4 text-gray-500">2023-07-05 16:40:55</td>
                            <td class="py-3 px-4">
                                <div class="flex items-center space-x-2">
                                    <a href="payment_method_detail.html" class="text-primary hover:text-primary-dark" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="payment_method_trend.html?method=HUABEI&name=花呗分期" class="text-blue-500 hover:text-blue-700" title="查看趋势">
                                        <i class="fas fa-chart-line"></i>
                                    </a>
                                    <label class="toggle-switch" title="状态切换">
                                        <input type="checkbox">
                                        <span class="toggle-slider"></span>
                                    </label>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="px-6 py-4 flex justify-between items-center border-t border-gray-100">
                <div>
                    <span class="text-sm text-gray-500">显示 <span class="text-primary font-medium">1-10</span> 条，共 <span class="text-primary font-medium">12</span> 条</span>
                </div>
                <div class="flex items-center space-x-1">
                    <button class="btn btn-light px-3 py-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="btn btn-primary px-3 py-1 text-sm">1</button>
                    <button class="btn btn-light px-3 py-1 text-sm">2</button>
                    <button class="btn btn-light px-3 py-1 text-sm">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 添加表格行悬停效果
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseover', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseout', function() {
                this.classList.remove('bg-gray-50');
            });
        });

        // 动态设置页面主题颜色
        document.documentElement.style.setProperty('--primary-color', '#3b7cfe');
        document.documentElement.style.setProperty('--primary-dark', '#2a5cb9');
        document.documentElement.style.setProperty('--primary-light', '#e8f0ff');
        document.documentElement.style.setProperty('--secondary-color', '#6c5ce7');

        // 全选/取消全选
        const selectAllCheckbox = document.querySelector('thead input[type="checkbox"]');
        const itemCheckboxes = document.querySelectorAll('tbody input[type="checkbox"]');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                itemCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        }
        
        // 状态切换确认
        document.querySelectorAll('.toggle-switch input').forEach(toggle => {
            toggle.addEventListener('change', function() {
                if (!this.checked) {
                    if (!confirm('确定要停用此支付方式吗？停用后将不再对用户展示。')) {
                        this.checked = true;
                    }
                } else {
                    if (!confirm('确定要启用此支付方式吗？')) {
                        this.checked = false;
                    }
                }
            });
        });

    </script>
</body>
</html> 
