<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付方式趋势 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 添加 Chart.js CDN -->
    <script src="https://lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/Chart.js/3.7.1/chart.min.js"></script>
    <style>
        /* 复用 dashboard.html 的基础样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }
        body { font-family: 'Noto Sans SC', sans-serif; background-color: #f6f8fb; color: #333; }
        .sidebar { width: 260px; transition: all 0.3s; background: #fff; box-shadow: 0 0 20px rgba(0, 0, 0, 0.05); z-index: 20; }
        .content { margin-left: 260px; transition: all 0.3s; }
        .header { background: linear-gradient(to right, var(--primary-color), var(--secondary-color)); color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
        .nav-item { margin-bottom: 0.5rem; border-radius: 0.5rem; transition: all 0.3s; }
        .nav-item:hover { background-color: var(--primary-light); }
        .nav-item.active { background-color: var(--primary-light); color: var(--primary-color); border-right: 3px solid var(--primary-color); }
        .submenu { max-height: 0; overflow: hidden; transition: max-height 0.3s ease-out; }
        .submenu.active { max-height: 500px; }
        .card { background: white; border-radius: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: all 0.3s; overflow: hidden; }
        .card:hover { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); transform: translateY(-5px); }
        .btn { border-radius: 0.5rem; padding: 0.5rem 1rem; transition: all 0.3s; font-weight: 500; }
        .btn-primary { background-color: var(--primary-color); color: white; }
        .btn-primary:hover { background-color: var(--primary-dark); transform: translateY(-2px); box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3); }
        .btn-light { background-color: #f1f5f9; color: #64748b; }
        .btn-light:hover { background-color: #e2e8f0; }
        .chart-container { border-radius: 1rem; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: all 0.3s; overflow: hidden; }
        .chart-container:hover { box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1); transform: translateY(-5px); }

    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <!-- 返回按钮 -->
             <a href="payment_method_list.html" class="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full" title="返回列表">
                <i class="fas fa-arrow-left"></i>
            </a>
            <img src="images/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="images/avatar.png" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1"><i class="fas fa-user mr-2 text-gray-500"></i>个人信息</a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1"><i class="fas fa-cog mr-2 text-gray-500"></i>系统设置</a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1"><i class="fas fa-sign-out-alt mr-2"></i>退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 (保持与列表页一致) -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
             <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i><span class="ml-3">仪表盘</span>
                    </a>
                </li>
                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i><span class="ml-3">数据字典管理</span>
                    </a>
                </li>
                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center"><i class="fas fa-sitemap w-5 h-5 text-center"></i><span class="ml-3">支付场景管理</span></div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">场景列表</a></li>
                        <li class="my-2"><a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">版本历史</a></li>
                    </ul>
                </li>
                <!-- 条件筛选管理 -->
                 <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center"><i class="fas fa-filter w-5 h-5 text-center"></i><span class="ml-3">条件筛选管理</span></div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">条件模块</a></li>
                        <li class="my-2"><a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">条件规则</a></li>
                    </ul>
                </li>
                <!-- 支付方式管理 -->
                <li class="nav-item active"> <!-- 保持当前菜单高亮 -->
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-primary rounded-lg bg-primary-light">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i><span class="ml-3">支付方式管理</span>
                    </a>
                </li>
                <!-- 渠道路由管理 -->
                 <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center"><i class="fas fa-random w-5 h-5 text-center"></i><span class="ml-3">渠道路由管理</span></div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">路由规则</a></li>
                        <li class="my-2"><a href="#" class="block py-2 text-gray-400 hover:text-primary cursor-not-allowed">版本管理</a></li>
                    </ul>
                </li>
                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>
                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i><span class="ml-3">商户号管理</span>
                    </a>
                </li>
                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i><span class="ml-3">应用管理</span>
                    </a>
                </li>
                <!-- 渠道账号管理 -->
                 <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center"><i class="fas fa-users-cog w-5 h-5 text-center"></i><span class="ml-3">渠道账号管理</span></div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2"><a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">渠道账号</a></li>
                        <li class="my-2"><a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">渠道员工</a></li>
                    </ul>
                </li>
                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i><span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="mb-6">
            <h2 id="pageTitle" class="text-2xl font-bold text-gray-800">支付方式趋势</h2>
             <p class="text-sm text-gray-500 mt-1">查看指定支付方式在不同渠道下的支付趋势数据。</p>
        </div>

        <!-- 图表区域 -->
        <div class="card p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 id="chartTitle" class="font-semibold text-gray-800 text-lg">支付趋势</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-600 rounded-lg font-medium trend-timespan" data-timespan="day">日</button>
                    <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg trend-timespan" data-timespan="week">周</button>
                    <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-lg trend-timespan" data-timespan="month">月</button>
                </div>
            </div>
            <!-- 图表容器 -->
            <div class="chart-container relative" style="height: 400px;">
                 <canvas id="paymentTrendChart"></canvas>
            </div>
        </div>

    </main>

    <script>
        // 侧边栏和用户下拉菜单的逻辑 (复用)
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() { /* ... 折叠逻辑 ... */
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });
        document.getElementById('userDropdown')?.addEventListener('click', function(e) { /* ... 下拉菜单逻辑 ... */
             e.stopPropagation();
             document.getElementById('userMenu').classList.toggle('hidden');
        });
        document.addEventListener('click', function(event) { /* ... 点击外部关闭 ... */
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                 userMenu.classList.add('hidden');
            }
        });
        // 动态设置当前导航项的激活状态 (这里应该高亮支付方式管理)
         function setActiveNav() {
            const currentPath = 'payment_method_list.html'; // 将此页面视为列表页的一部分
            document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active','bg-primary-light', 'text-primary'));
             document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
            document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
            document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');

            document.querySelectorAll('.nav-item > a').forEach(link => {
                 if (link.getAttribute('href') && link.getAttribute('href').includes(currentPath)) {
                     link.classList.add('bg-primary-light', 'text-primary');
                     link.closest('.nav-item').classList.add('active');
                 }
             });
        }
        setActiveNav();


        // --- 图表逻辑 ---
        let paymentChart = null;
        const urlParams = new URLSearchParams(window.location.search);
        const paymentMethodCode = urlParams.get('method');
        const paymentMethodName = urlParams.get('name') || '未知方式';

        // 更新页面和图表标题
        document.getElementById('pageTitle').textContent = `${paymentMethodName} - 支付趋势`;
        document.getElementById('chartTitle').textContent = `${paymentMethodName} - 支付趋势`;

        // 获取模拟趋势数据 (与之前类似)
        function getMockTrendData(paymentMethod, timespan) {
            const labelsMap = {
                day: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '23:59'],
                week: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                month: ['第一周', '第二周', '第三周', '第四周']
            };
            const labels = labelsMap[timespan] || labelsMap['day'];
            const baseValue = Math.random() * 5000; // 增加基数使图表更明显
            const channel1Data = labels.map(() => Math.max(0, baseValue + Math.random() * 2000 - 1000)); // 保证数据非负
             const channel2Data = labels.map(() => Math.max(0, baseValue / 1.5 + Math.random() * 1500 - 750)); // 保证数据非负

            let datasets = [
                {
                    label: '渠道X金额',
                    data: channel1Data,
                    borderColor: 'rgba(59, 124, 254, 1)', // --primary-color
                    backgroundColor: 'rgba(59, 124, 254, 0.1)',
                    fill: true,
                    tension: 0.3,
                     yAxisID: 'yAmount' // 指定Y轴
                }
            ];

            // 假设支付宝和微信有两个渠道，其他只有一个
            if (['ALIPAY', 'WECHAT_PAY'].includes(paymentMethod)) {
                 datasets.push({
                    label: '渠道Y金额',
                    data: channel2Data,
                    borderColor: 'rgba(108, 92, 231, 1)', // --secondary-color
                    backgroundColor: 'rgba(108, 92, 231, 0.1)',
                    fill: true,
                    tension: 0.3,
                    yAxisID: 'yAmount' // 指定Y轴
                });
            }

            // 添加模拟笔数数据
             const countData = labels.map(() => Math.max(0, 50 + Math.random() * 100 - 50));
             datasets.push({
                 label: '交易笔数',
                 data: countData,
                 borderColor: 'rgba(0, 184, 148, 1)', // --success-color
                 backgroundColor: 'rgba(0, 184, 148, 0.1)',
                 fill: false, // 笔数通常不用填充面积
                 tension: 0.3,
                 yAxisID: 'yCount', // 指定另一个Y轴
                 type: 'line' // 可以混合图表类型，但这里保持line
             });

            return { labels: labels, datasets: datasets };
        }

        // 渲染趋势图 (添加双Y轴)
        function renderTrendChart(timespan) {
            const canvas = document.getElementById('paymentTrendChart');
            if (!canvas) return;
            const ctx = canvas.getContext('2d');
            const data = getMockTrendData(paymentMethodCode, timespan);

            if (paymentChart) {
                paymentChart.destroy(); // 销毁旧图表
            }

            paymentChart = new Chart(ctx, {
                type: 'line',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                     interaction: { // 更好的提示交互
                         mode: 'index',
                         intersect: false,
                    },
                    scales: {
                        yAmount: { // 金额Y轴 (左侧)
                             type: 'linear',
                             display: true,
                             position: 'left',
                            beginAtZero: true,
                             title: {
                                 display: true,
                                 text: '金额 (元)'
                             }
                        },
                         yCount: { // 笔数Y轴 (右侧)
                            type: 'linear',
                            display: true,
                             position: 'right',
                             beginAtZero: true,
                             title: {
                                 display: true,
                                 text: '笔数'
                             },
                             // 确保笔数轴不与金额轴重叠
                             grid: {
                                 drawOnChartArea: false, // Only draw grid lines for the first Y axis
                            },
                        },
                        x: {
                             title: {
                                 display: true,
                                 text: '时间'
                             }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom', // 放到底部防止遮挡图表
                        },
                        tooltip: { // 自定义提示信息
                             callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                     if (label) {
                                         label += ': ';
                                     }
                                    if (context.parsed.y !== null) {
                                         // 根据Y轴ID判断单位
                                         if (context.dataset.yAxisID === 'yCount') {
                                             label += context.parsed.y + ' 笔';
                                         } else {
                                            label += '¥' + context.parsed.y.toFixed(2);
                                         }
                                     }
                                     return label;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 时间跨度按钮点击事件
        document.querySelectorAll('.trend-timespan').forEach(button => {
            button.addEventListener('click', function() {
                const selectedTimespan = this.dataset.timespan;
                renderTrendChart(selectedTimespan);

                // 更新按钮样式
                document.querySelectorAll('.trend-timespan').forEach(btn => {
                    if (btn === this) {
                        btn.classList.add('bg-blue-100', 'text-blue-600', 'font-medium');
                        btn.classList.remove('text-gray-600', 'hover:bg-gray-100');
                    } else {
                        btn.classList.remove('bg-blue-100', 'text-blue-600', 'font-medium');
                        btn.classList.add('text-gray-600', 'hover:bg-gray-100');
                    }
                });
            });
        });

        // 初始化渲染日视图
        if (paymentMethodCode) {
            renderTrendChart('day');
        } else {
             // 如果没有参数，显示提示信息
             const chartContainer = document.querySelector('.chart-container');
             chartContainer.innerHTML = '<p class="text-center text-gray-500 p-10">缺少支付方式参数，无法加载图表。</p>';
        }

    </script>
</body>
</html> 