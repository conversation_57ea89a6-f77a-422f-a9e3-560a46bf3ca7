<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收银台及渠道管理系统 - 编辑支付场景</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            width: calc(100% - 260px);
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: #ffffff;
            border-radius: 1rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            border: 1px solid rgba(226, 232, 240, 0.7);
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .card:hover {
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            transform: none;
            border-color: rgba(226, 232, 240, 0.7);
        }

        /* 表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 按钮样式 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 40px;
            height: 20px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 支付方式列表样式 */
        .payment-method-item {
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .payment-method-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .payment-method-item.default-selected {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 1px var(--primary-color);
        }

        .drag-handle {
            cursor: move;
        }
        
        /* 支付方式过滤条件样式 */
        .payment-filter-item {
            border: 1px solid var(--border-color);
            margin-bottom: 1rem;
            transition: all 0.3s;
        }
        
        .payment-filter-item:hover {
            border-color: var(--primary-color);
        }
        
        .payment-method-icon {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        .filter-tags-container {
            display: inline-flex;
            flex-wrap: wrap;
            margin-left: 1rem;
            align-items: center;
        }
        
        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
            <nav class="mt-6 px-4">
                <ul>
                    <!-- 仪表盘 -->
                    <li class="nav-item">
                        <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                            <span class="ml-3">仪表盘</span>
                        </a>
                    </li>

                    <!-- 数据字典管理 -->
                    <li class="nav-item">
                        <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-book w-5 h-5 text-center"></i>
                            <span class="ml-3">数据字典管理</span>
                        </a>
                    </li>

                    <!-- 支付场景管理 -->
                    <li class="nav-item active">
                        <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                            <div class="flex items-center">
                                <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                                <span class="ml-3">支付场景管理</span>
                            </div>
                            <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300" style="transform: rotate(180deg);"></i>
                        </div>
                        <ul class="submenu active pl-12 pr-4">
                            <li class="my-2">
                                <a href="payment_scene.html" class="block py-2 text-primary font-medium">
                                    场景列表
                                </a>
                            </li>
                            <li class="my-2">
                                <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                    版本历史
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 条件筛选管理 -->
                    <li class="nav-item">
                        <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-filter w-5 h-5 text-center"></i>
                                <span class="ml-3">条件筛选管理</span>
                            </div>
                            <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                        </div>
                        <ul class="submenu pl-12 pr-4">
                            <li class="my-2">
                                <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                    条件模块
                                </a>
                            </li>
                            <li class="my-2">
                                <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                    条件规则
                                </a>
                            </li>

                        </ul>
                    </li>
                    
                    <!-- 支付方式管理 -->
                    <li class="nav-item">
                        <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                            <span class="ml-3">支付方式管理</span>
                        </a>
                    </li>

                    <!-- 渠道路由管理 -->
                    <li class="nav-item">
                        <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-random w-5 h-5 text-center"></i>
                                <span class="ml-3">渠道路由管理</span>
                            </div>
                            <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                        </div>
                        <ul class="submenu pl-12 pr-4">
                            <li class="my-2">
                                <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                    路由规则
                                </a>
                            </li>
                            <li class="my-2">
                                <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                    版本管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Feature Flags -->
                    <li class="nav-item">
                        <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                            <span class="ml-3">功能开关</span>
                        </a>
                    </li>

                    <!-- 商户号管理 -->
                    <li class="nav-item">
                        <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-store w-5 h-5 text-center"></i>
                            <span class="ml-3">商户号管理</span>
                        </a>
                    </li>

                    <!-- 应用管理 -->
                    <li class="nav-item">
                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                    <!-- 操作日志 -->
                    <li class="nav-item">
                        <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-history w-5 h-5 text-center"></i>
                            <span class="ml-3">操作日志</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="content pt-20 pb-8 px-8">
            <!-- 面包屑导航 -->
            <div class="mb-6 flex items-center text-sm text-gray-500">
                <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
                <i class="fas fa-angle-right mx-2"></i>
                <a href="payment_scene.html" class="hover:text-primary">支付场景管理</a>
                <i class="fas fa-angle-right mx-2"></i>
                <span class="text-gray-800">编辑支付场景</span>
            </div>

            <div class="mb-6 flex justify-between items-center">
                <h2 class="text-xl font-bold text-gray-800">编辑支付场景</h2>
                <div class="flex space-x-3">
                    <button class="btn btn-light flex items-center">
                        <i class="fas fa-eye mr-2"></i>
                        <span>预览</span>
                    </button>
                    <button class="btn btn-success flex items-center">
                        <i class="fas fa-paper-plane mr-2"></i>
                        <span>发布</span>
                    </button>
                    <button class="btn btn-primary flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        <span>保存</span>
                    </button>
                </div>
            </div>

            <!-- 表单内容将在下一步添加 -->

            <!-- 基本信息表单 -->
            <div class="card mb-6">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">基本信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneName">
                                场景名称 <span class="text-red-500">*</span>
                            </label>
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="sceneName" type="text" placeholder="请输入场景名称" value="电商购物-标准">
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneCode">
                                场景编码 <span class="text-red-500">*</span>
                            </label>
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="sceneCode" type="text" placeholder="请输入场景编码" value="ECOM_STD">
                            <p class="text-xs text-gray-500 mt-1">使用大写字母和下划线，例如：ECOM_STD</p>
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2">
                                状态
                            </label>
                            <div class="flex items-center">
                                <input id="sceneStatusActive" type="radio" name="sceneStatus" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" checked>
                                <label for="sceneStatusActive" class="ml-2 block text-gray-700">
                                    启用
                                </label>
                                <input id="sceneStatusInactive" type="radio" name="sceneStatus" value="0" class="ml-6 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="sceneStatusInactive" class="ml-2 block text-gray-700">
                                    禁用
                                </label>
                            </div>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneDesc">
                                场景描述
                            </label>
                            <textarea class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="sceneDesc" rows="3" placeholder="请输入场景描述">标准电商购物场景，支持多种支付方式，适用于大部分电商应用。</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 场景条件配置 -->
            <div class="card mb-6">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-800">场景条件配置</h3>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>配置支付场景的触发条件，支持条件组合</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-medium text-gray-700">条件模块</h4>
                        <div class="flex space-x-2">
                            <button type="button" id="selectConditionModuleBtn" class="btn btn-primary flex items-center text-sm">
                                <i class="fas fa-plus mr-1"></i>
                                <span>选择条件模块</span>
                            </button>
                            <button type="button" id="previewConditionsBtn" class="btn btn-light flex items-center text-sm">
                                <i class="fas fa-eye mr-1"></i>
                                <span>预览JSON</span>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 条件模块选择区域 -->
                    <div id="noConditionsMessage" class="flex items-center p-4 bg-gray-50 rounded-md">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        <span class="text-gray-600">请选择一个条件模块</span>
                    </div>
                </div>
            </div>

            <!-- 支付方式配置 -->
            <div class="card mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-800">支付方式配置</h3>
                        <button id="addPaymentMethodBtn" class="btn btn-primary flex items-center text-sm">
                            <i class="fas fa-plus mr-1"></i>
                            <span>添加支付方式</span>
                        </button>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>支付方式的展示顺序可通过拖拽调整，从上至下依次展示。</span>
                        </div>
                        <div class="mt-2 text-sm text-gray-600">
                            若需管理支付方式，请前往 <a href="payment_method_list.html" class="text-blue-600 hover:text-blue-800 underline">支付方式管理</a> 页面进行配置。
                        </div>
                    </div>
                    
                    <!-- 支付方式列表 -->
                    <div class="space-y-3">
                        <!-- 微信支付 -->
                        <div class="payment-method-item rounded-md p-4 bg-white default-selected" data-id="1">
                            <div class="flex items-center">
                                <div class="drag-handle text-gray-400 cursor-move mr-3">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 flex items-center justify-center bg-green-100 text-green-600 rounded-md mr-3">
                                                <i class="fab fa-weixin text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">微信支付</h4>
                                                <p class="text-xs text-gray-500">WECHAT_PAY</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">状态:</span>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">折叠:</span>
                                                <label class="switch">
                                                    <input type="checkbox" class="fold-checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">默认:</span>
                                                <label class="switch">
                                                    <input type="radio" name="defaultPayment" class="default-radio" style="opacity:0; position:absolute;" checked>
                                                    <span class="slider" style="background-color: var(--primary-color);"></span>
                                                </label>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-gray-600 hover:text-blue-600 edit-payment-btn">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-600 hover:text-red-600 delete-payment-btn">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 支付宝 -->
                        <div class="payment-method-item rounded-md p-4 bg-white" data-id="2">
                            <div class="flex items-center">
                                <div class="drag-handle text-gray-400 cursor-move mr-3">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 flex items-center justify-center bg-blue-100 text-blue-600 rounded-md mr-3">
                                                <i class="fab fa-alipay text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">支付宝</h4>
                                                <p class="text-xs text-gray-500">ALIPAY</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">状态:</span>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">折叠:</span>
                                                <label class="switch">
                                                    <input type="checkbox" class="fold-checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">默认:</span>
                                                <label class="switch">
                                                    <input type="radio" name="defaultPayment" class="default-radio" style="opacity:0; position:absolute;">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-gray-600 hover:text-blue-600 edit-payment-btn">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-600 hover:text-red-600 delete-payment-btn">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 京东支付 -->
                        <div class="payment-method-item rounded-md p-4 bg-white" data-id="3">
                            <div class="flex items-center">
                                <div class="drag-handle text-gray-400 cursor-move mr-3">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 flex items-center justify-center bg-red-100 text-red-600 rounded-md mr-3">
                                                <i class="fas fa-shopping-cart text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">京东支付</h4>
                                                <p class="text-xs text-gray-500">JD_PAY</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">状态:</span>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">折叠:</span>
                                                <label class="switch">
                                                    <input type="checkbox" class="fold-checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">默认:</span>
                                                <label class="switch">
                                                    <input type="radio" name="defaultPayment" class="default-radio" style="opacity:0; position:absolute;">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-gray-600 hover:text-blue-600 edit-payment-btn">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-600 hover:text-red-600 delete-payment-btn">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 银联支付 -->
                        <div class="payment-method-item rounded-md p-4 bg-white" data-id="4">
                            <div class="flex items-center">
                                <div class="drag-handle text-gray-400 cursor-move mr-3">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 flex items-center justify-center bg-purple-100 text-purple-600 rounded-md mr-3">
                                                <i class="fas fa-credit-card text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">银联</h4>
                                                <p class="text-xs text-gray-500">UNIONPAY</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">状态:</span>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">折叠:</span>
                                                <label class="switch">
                                                    <input type="checkbox" class="fold-checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">默认:</span>
                                                <label class="switch">
                                                    <input type="radio" name="defaultPayment" class="default-radio" style="opacity:0; position:absolute;">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-gray-600 hover:text-blue-600 edit-payment-btn">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-600 hover:text-red-600 delete-payment-btn">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 过滤条件和渠道规则部分将在下一步添加 -->

            <!-- 过滤条件配置 -->
            <div class="card mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-800">支付方式过滤条件配置</h3>
                        <button class="btn btn-primary flex items-center text-sm" id="addPaymentMethodFilterBtn">
                            <i class="fas fa-plus mr-1"></i>
                            <span>添加支付方式</span>
                        </button>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>通过配置过滤条件，可以根据不同场景控制支付方式的展示规则。</span>
                        </div>
                    </div>
                    
                    <!-- 支付方式过滤条件列表 -->
                    <div class="space-y-6">
                        <!-- 微信支付 -->
                        <div class="payment-filter-item rounded-lg overflow-hidden">
                            <div class="p-4 bg-white">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="payment-method-icon bg-green-100 text-green-600 mr-3">
                                            <i class="fab fa-weixin text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-800">微信支付</h4>
                                            <p class="text-xs text-gray-500">WECHAT_PAY</p>
                                        </div>
                                        <div class="filter-tags-container">
                                            <div class="tag tag-blue">
                                                <span>行程时间段过滤</span>
                                            </div>
                                            <div class="tag tag-purple">
                                                <span>用户信用等级过滤</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center">
                                        
                                        <button class="btn btn-primary flex items-center text-sm add-filter-btn">
                                            <i class="fas fa-plus mr-1"></i>
                                            <span>添加过滤条件</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 银联支付 -->
                        <div class="payment-filter-item rounded-lg overflow-hidden">
                            <div class="p-4 bg-white">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="payment-method-icon bg-purple-100 text-purple-600 mr-3">
                                            <i class="fas fa-credit-card text-xl"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-800">银联支付</h4>
                                            <p class="text-xs text-gray-500">UNIONPAY</p>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center">
                                        
                                        <button class="btn btn-primary flex items-center text-sm add-filter-btn">
                                            <i class="fas fa-plus mr-1"></i>
                                            <span>添加过滤条件</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 渲染配置 -->
            <div class="card mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-800">渲染规则配置</h3>
                        <button class="btn btn-primary flex items-center text-sm" id="addChannelRuleBtn">
                            <i class="fas fa-plus mr-1"></i>
                            <span>添加渲染规则</span>
                        </button>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>渲染规则用于指定特定条件下的支付方式选择优先级。</span>
                        </div>
                    </div>
                    
                    <!-- 渲染规则列表 -->
                    <div id="noChannelRulesMessage" class="flex items-center p-4 bg-gray-50 rounded-md">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        <span class="text-gray-600">尚未配置渲染规则，使用全局默认路由规则</span>
                    </div>
                    <div id="selectedChannelRulesContainer" class="mt-4 space-y-3">
                        <!-- 已选渲染规则将在此处显示 -->
                    </div>
                </div>
            </div>


            <!-- JavaScript 交互脚本 -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // 确保过滤条件模态框初始状态正确
                    const noFilterSelected = document.getElementById('noFilterSelected');
                    if (noFilterSelected) noFilterSelected.classList.remove('hidden');
                    
                    // 侧边栏子菜单折叠展开
                    document.querySelectorAll('.menu-toggle').forEach(item => {
                        const submenu = item.nextElementSibling;
                        const icon = item.querySelector('.submenu-icon');
                        // 检查当前菜单项是否应该展开 (基于页面URL或active class)
                        let isActive = false;
                        if (submenu) { // 确保submenu存在
                            submenu.querySelectorAll('a').forEach(link => {
                                // 完全匹配URL或者父级菜单是active
                                if (link.href === window.location.href || item.closest('.nav-item').classList.contains('active')) {
                                   if (link.href === window.location.href) {
                                        isActive = true;
                                        link.classList.add('font-semibold', 'text-primary'); // 高亮当前子菜单项
                                   }
                                }
                            });
                        }
                        
                        // 如果子菜单包含活动项 或 父菜单是active，则展开
                        if (isActive || item.closest('.nav-item').classList.contains('active')) {
                             // 对于父菜单项，如果它自己不是链接且包含活动子项，则展开
                             if(!item.closest('.nav-item').querySelector('a[href]') && isActive) {
                                 submenu.classList.add('active');
                                 icon.style.transform = 'rotate(180deg)';
                             } else if (item.closest('.nav-item').classList.contains('active') && submenu) {
                                 // 如果父菜单是active，也展开它
                                 submenu.classList.add('active');
                                 icon.style.transform = 'rotate(180deg)';
                             }
                        }
                        
                        // 添加点击事件
                         if(item && submenu) { // 确保元素存在
                            item.addEventListener('click', function() {
                                if (submenu.classList.contains('active')) {
                                    submenu.classList.remove('active');
                                    icon.style.transform = 'rotate(0deg)';
                                } else {
                                    // 关闭其他打开的子菜单
                                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                                        if (openSubmenu !== submenu) {
                                            openSubmenu.classList.remove('active');
                                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                                        }
                                    });
                                    submenu.classList.add('active');
                                    icon.style.transform = 'rotate(180deg)';
                                }
                            });
                        }
                    });

                    // 用户下拉菜单
                    const userDropdown = document.getElementById('userDropdown');
                    const userMenu = document.getElementById('userMenu');
                    if (userDropdown) {
                        userDropdown.addEventListener('click', function(e) {
                            e.stopPropagation();
                            userMenu.classList.toggle('hidden');
                        });
                    }

                    // 点击页面其他位置关闭用户菜单
                    document.addEventListener('click', function(event) {
                        if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                            userMenu.classList.add('hidden');
                        }
                    });
                    
                    // 支付方式默认选择
                    const defaultRadios = document.querySelectorAll('.default-radio');
                    defaultRadios.forEach(radio => {
                        radio.addEventListener('change', function() {
                            document.querySelectorAll('.payment-method-item').forEach(item => {
                                item.classList.remove('default-selected');
                            });
                            
                            if (this.checked) {
                                const paymentItem = this.closest('.payment-method-item');
                                paymentItem.classList.add('default-selected');
                                
                                // 更新所有单选按钮的样式
                                document.querySelectorAll('.default-radio').forEach(r => {
                                    const slider = r.nextElementSibling;
                                    if (r.checked) {
                                        slider.style.backgroundColor = 'var(--primary-color)';
                                    } else {
                                        slider.style.backgroundColor = '#ccc';
                                    }
                                });
                            }
                        });
                    });
                    
                    // 添加支付方式按钮事件
                    document.getElementById('addPaymentMethodBtn').addEventListener('click', function() {
                        // 显示添加支付方式模态框
                        document.getElementById('paymentMethodModal').classList.remove('hidden');
                    });
                    
                    // 添加过滤条件按钮事件
                    document.getElementById('addPaymentMethodFilterBtn').addEventListener('click', function() {
                        // 显示添加支付方式模态框
                        document.getElementById('paymentMethodModal').classList.remove('hidden');
                    });
                
                    
                    // 支付方式编辑按钮事件
                    const editPaymentBtns = document.querySelectorAll('.edit-payment-btn');
                    const editModal = document.getElementById('paymentMethodEditModal');
                    const closeEditModalBtn = document.getElementById('closeEditModal');
                    const cancelEditBtn = document.getElementById('cancelEditBtn');
                    const savePaymentBtn = document.getElementById('savePaymentBtn');
                    
                    // 编辑按钮点击事件
                    editPaymentBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const paymentItem = this.closest('.payment-method-item');
                            const paymentId = paymentItem.dataset.id;
                            
                            // 填充表单数据
                            populateEditForm(paymentId);
                            
                            // 显示模态框
                            editModal.classList.remove('hidden');
                        });
                    });
                    
                    // 关闭模态框
                    function closeEditModal() {
                        editModal.classList.add('hidden');
                    }
                    
                    // 关闭按钮点击事件
                    closeEditModalBtn.addEventListener('click', closeEditModal);
                    cancelEditBtn.addEventListener('click', closeEditModal);
                    
                    // 点击模态框外部关闭
                    editModal.addEventListener('click', function(e) {
                        if (e.target === editModal) {
                            closeEditModal();
                        }
                    });
                    
                    // 填充编辑表单
                    function populateEditForm(paymentId) {
                        const data = paymentMethodsData[paymentId];
                        
                        document.getElementById('editPaymentId').value = paymentId;
                        document.getElementById('displayTitle').value = data.title;
                        document.getElementById('displayDesc').value = data.desc || '';
                        
                        // 清除所有选中状态
                        document.querySelectorAll('input[name="relatedProducts"]').forEach(checkbox => {
                            checkbox.checked = false;
                        });
                        
                        // 设置关联产品选中状态
                        if (data.productIds && data.productIds.length) {
                            // 如果是数组形式
                            data.productIds.forEach(id => {
                                const checkbox = document.getElementById(`product${id}`);
                                if (checkbox) checkbox.checked = true;
                            });
                        } else if (data.productId) {
                            // 如果是单一ID形式(兼容旧数据)
                            const checkbox = document.getElementById(`product${data.productId}`);
                            if (checkbox) checkbox.checked = true;
                        }
                        
                        document.getElementById('enableBnpl').checked = data.enableBnpl;
                    }
                    
                    // 保存按钮点击事件
                    savePaymentBtn.addEventListener('click', function() {
                        const paymentId = document.getElementById('editPaymentId').value;
                        const title = document.getElementById('displayTitle').value.trim();
                        const desc = document.getElementById('displayDesc').value.trim();
                        
                        // 获取选中的关联产品
                        const productIds = [];
                        document.querySelectorAll('input[name="relatedProducts"]:checked').forEach(checkbox => {
                            productIds.push(checkbox.value);
                        });
                        
                        const enableBnpl = document.getElementById('enableBnpl').checked;
                        
                        // 验证表单
                        if (!title) {
                            alert('请输入显示标题');
                            return;
                        }
                        
                        if (productIds.length === 0) {
                            alert('请至少选择一个关联支付产品');
                            return;
                        }
                        
                        // 更新数据
                        paymentMethodsData[paymentId] = {
                            title,
                            desc,
                            productIds,
                            enableBnpl
                        };
                        
                        // 更新UI
                        const paymentItem = document.querySelector(`.payment-method-item[data-id="${paymentId}"]`);
                        paymentItem.querySelector('h4').textContent = title;
                        
                        // 显示成功提示
                        alert('保存成功！');
                        
                        // 关闭模态框
                        closeEditModal();
                    });
                    
                    // 支付方式删除按钮事件
                    const deletePaymentBtns = document.querySelectorAll('.delete-payment-btn');
                    deletePaymentBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const paymentItem = this.closest('.payment-method-item');
                            const paymentId = paymentItem.dataset.id;
                            if (confirm(`确定要删除此支付方式吗？`)) {
                                alert(`删除支付方式 ID: ${paymentId}`);
                            }
                        });
                    });
                    
                    // 初始化条件模块列表
                    let conditionModuleList = null;
                    let filterTemplateList = null;
                    
                    // 选择条件模块按钮事件
                    document.getElementById('selectConditionModuleBtn').addEventListener('click', function() {
                        // 显示条件模块选择模态框
                        document.getElementById('conditionModuleModal').classList.remove('hidden');
                        
                        // 延迟初始化列表，确保模态框已经显示
                        setTimeout(() => {
                            if (!conditionModuleList) {
                                const container = document.getElementById('conditionModuleListContainer');
                                conditionModuleList = new ConditionTemplateList(container, {
                                    onSelect: function(template) {
                                        // 更新条件模块显示区域
                                        const noConditionsMessage = document.getElementById('noConditionsMessage');
                                        noConditionsMessage.innerHTML = `
                                            <div class="selected-condition-module p-4 bg-blue-50 rounded-md border border-blue-200 w-full">
                                                <div class="flex justify-between items-center">
                                                    <div>
                                                        <div class="flex items-center">
                                                            <div class="w-10 h-10 rounded-md bg-blue-100 flex items-center justify-center mr-3">
                                                                <i class="fas fa-filter text-blue-600"></i>
                                                            </div>
                                                            <div>
                                                                <h4 class="font-medium text-gray-800">${template.name}</h4>
                                                                <p class="text-xs text-gray-500">${template.code}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <a href="condition_module_view.html?id=${template.id}" target="_blank" class="btn btn-light text-sm mr-2">
                                                            <i class="fas fa-eye mr-1"></i>查看详情
                                                        </a>
                                                        <button class="btn btn-danger text-sm" id="removeConditionModule">
                                                            <i class="fas fa-times mr-1"></i>移除
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                        
                                        // 绑定移除条件模块按钮事件
                                        document.getElementById('removeConditionModule').addEventListener('click', function() {
                                            noConditionsMessage.innerHTML = `
                                                <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                                <span class="text-gray-600">请选择一个条件模块</span>
                                            `;
                                        });
                                        
                                        // 关闭模态框
                                        document.getElementById('conditionModuleModal').classList.add('hidden');
                                    }
                                });
                            }
                        }, 100);
                    });
                    
                    // 关闭条件模块选择模态框
                    document.getElementById('closeConditionModuleModal').addEventListener('click', function() {
                        document.getElementById('conditionModuleModal').classList.add('hidden');
                    });
                    
                    document.getElementById('cancelConditionModule').addEventListener('click', function() {
                        document.getElementById('conditionModuleModal').classList.add('hidden');
                    });
                    
                    // 预览JSON按钮事件
                    document.getElementById('previewConditionsBtn').addEventListener('click', function() {
                        alert('显示条件JSON预览');
                    });

                    // 添加过滤条件按钮
                    const addFilterBtns = document.querySelectorAll('.add-filter-btn');
                    addFilterBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            // 显示添加过滤条件模态框
                            document.getElementById('filterModal').classList.remove('hidden');
                            // 存储当前操作的支付方式项
                            currentPaymentItem = this.closest('.payment-filter-item');
                            
                            // 重置过滤条件表单
                            document.getElementById('filterType').value = '';
                            document.querySelectorAll('.filter-option').forEach(function(element) {
                                element.classList.add('hidden');
                            });
                            document.getElementById('noFilterSelected').classList.remove('hidden');
                            
                            // 清空搜索框
                            const searchInput = document.querySelector('#filterTypePanel input[placeholder="搜索过滤条件..."]');
                            if (searchInput) searchInput.value = '';
                            
                            // 默认显示过滤条件类型面板
                            document.getElementById('filterTypeTab').click();
                        });
                    });
                    
                    // 过滤条件模态框标签页切换
                    document.getElementById('filterTypeTab').addEventListener('click', function() {
                        // 切换标签页样式
                        document.querySelectorAll('.tab-button').forEach(tab => {
                            tab.classList.remove('border-blue-500', 'text-blue-600');
                            tab.classList.add('border-transparent', 'text-gray-500');
                        });
                        this.classList.remove('border-transparent', 'text-gray-500');
                        this.classList.add('border-blue-500', 'text-blue-600');
                        
                        // 切换面板显示
                        document.querySelectorAll('.tab-panel').forEach(panel => {
                            panel.classList.add('hidden');
                        });
                        document.getElementById('filterTypePanel').classList.remove('hidden');
                    });
                    
                    document.getElementById('filterTemplateTab').addEventListener('click', function() {
                        // 切换标签页样式
                        document.querySelectorAll('.tab-button').forEach(tab => {
                            tab.classList.remove('border-blue-500', 'text-blue-600');
                            tab.classList.add('border-transparent', 'text-gray-500');
                        });
                        this.classList.remove('border-transparent', 'text-gray-500');
                        this.classList.add('border-blue-500', 'text-blue-600');
                        
                        // 切换面板显示
                        document.querySelectorAll('.tab-panel').forEach(panel => {
                            panel.classList.add('hidden');
                        });
                        document.getElementById('filterTemplatePanel').classList.remove('hidden');
                        
                        // 延迟初始化列表，确保面板已经显示
                        setTimeout(() => {
                            if (!filterTemplateList) {
                                const container = document.getElementById('filterTemplateListContainer');
                                filterTemplateList = new ConditionTemplateList(container, {
                                    onSelect: function(template) {
                                        // 添加条件模板作为过滤条件
                                        if (currentPaymentItem) {
                                            // 查找或创建过滤标签容器
                                            let filterTagsContainer = currentPaymentItem.querySelector('.filter-tags-container');
                                            if (!filterTagsContainer) {
                                                filterTagsContainer = document.createElement('div');
                                                filterTagsContainer.className = 'filter-tags-container';
                                                
                                                // 添加到支付方式名称后面
                                                const paymentNameContainer = currentPaymentItem.querySelector('.flex.items-center').querySelector('div:nth-child(2)').parentNode;
                                                paymentNameContainer.appendChild(filterTagsContainer);
                                            }
                                            
                                            // 创建新标签
                                            const newTag = document.createElement('div');
                                            newTag.className = `tag tag-blue`;
                                            newTag.innerHTML = `<span>${template.name}</span>`;
                                            
                                            // 添加到容器
                                            filterTagsContainer.appendChild(newTag);
                                        }
                                        
                                        // 关闭模态框
                                        document.getElementById('filterModal').classList.add('hidden');
                                    }
                                });
                            }
                        }, 100);
                    });
                    
                    // 关闭过滤条件模态框
                    document.getElementById('closeFilterModal').addEventListener('click', function() {
                        document.getElementById('filterModal').classList.add('hidden');
                    });
                    
                    document.getElementById('cancelFilter').addEventListener('click', function() {
                        document.getElementById('filterModal').classList.add('hidden');
                    });
                    
                    // 关闭添加支付方式模态框
                    document.getElementById('closePaymentModal').addEventListener('click', function() {
                        document.getElementById('paymentMethodModal').classList.add('hidden');
                    });
                    
                    document.getElementById('cancelPayment').addEventListener('click', function() {
                        document.getElementById('paymentMethodModal').classList.add('hidden');
                    });
                    
                    // 过滤条件类型选择变化事件
                    document.getElementById('filterType').addEventListener('change', function() {
                        // 隐藏所有过滤选项
                        document.querySelectorAll('.filter-option').forEach(function(element) {
                            element.classList.add('hidden');
                        });
                        
                        // 显示选中的过滤选项
                        const selectedFilter = this.value;
                        if (selectedFilter) {
                            document.getElementById(`${selectedFilter}Filter`).classList.remove('hidden');
                            document.getElementById('noFilterSelected').classList.add('hidden');
                        } else {
                            document.getElementById('noFilterSelected').classList.remove('hidden');
                        }
                    });
                    
                    // 过滤条件搜索框事件
                    const filterSearchInput = document.querySelector('#filterTypePanel input[placeholder="搜索过滤条件..."]');
                    if (filterSearchInput) {
                        filterSearchInput.addEventListener('input', function() {
                            const searchTerm = this.value.toLowerCase();
                            const filterOptions = document.getElementById('filterType').options;
                            
                            // 根据搜索词过滤下拉选项
                            for (let i = 0; i < filterOptions.length; i++) {
                                const option = filterOptions[i];
                                const optionText = option.text.toLowerCase();
                                
                                if (optionText.includes(searchTerm) || searchTerm === '') {
                                    option.style.display = '';
                                } else {
                                    option.style.display = 'none';
                                }
                            }
                        });
                    }
                    
                    // 保存过滤条件
                    document.getElementById('saveFilter').addEventListener('click', function() {
                        // 检查哪个面板是活动的
                        const isTemplateActive = !document.getElementById('filterTemplatePanel').classList.contains('hidden');
                        
                        if (isTemplateActive) {
                            // 这种情况由选择模板按钮直接处理
                            return;
                        }
                        
                        // 获取选择的过滤条件类型
                        const filterType = document.getElementById('filterType').value;
                        const filterNames = {
                            'userAmount': '用户支付金额过滤',
                            'tripTime': '行程时间段过滤',
                            'userCredit': '用户信用等级过滤',
                            'userLocation': '用户地理位置过滤',
                            'deviceType': '设备类型过滤'
                        };
                        
                        if (filterType && currentPaymentItem) {
                            const tagColors = {
                                'userAmount': 'blue',
                                'tripTime': 'green',
                                'userCredit': 'purple',
                                'userLocation': 'yellow',
                                'deviceType': 'red'
                            };
                            
                            // 查找或创建过滤标签容器
                            let filterTagsContainer = currentPaymentItem.querySelector('.filter-tags-container');
                            if (!filterTagsContainer) {
                                filterTagsContainer = document.createElement('div');
                                filterTagsContainer.className = 'filter-tags-container';
                                
                                // 添加到支付方式名称后面
                                const paymentNameContainer = currentPaymentItem.querySelector('.flex.items-center').querySelector('div:nth-child(2)').parentNode;
                                paymentNameContainer.appendChild(filterTagsContainer);
                            }
                            
                            // 创建新标签
                            const newTag = document.createElement('div');
                            newTag.className = `tag tag-${tagColors[filterType]}`;
                            newTag.innerHTML = `<span>${filterNames[filterType]}</span>`;
                            
                            // 添加到容器
                            filterTagsContainer.appendChild(newTag);
                            
                            // 关闭模态框
                            document.getElementById('filterModal').classList.add('hidden');
                            
                            // 重置表单
                            document.getElementById('filterType').value = '';
                            document.querySelectorAll('.filter-option').forEach(function(element) {
                                element.classList.add('hidden');
                            });
                            document.getElementById('noFilterSelected').classList.remove('hidden');
                        }
                    });
                    
                    // 保存支付方式
                    document.getElementById('savePayment').addEventListener('click', function() {
                        // 获取选择的支付方式
                        const paymentMethodSelect = document.getElementById('paymentMethod');
                        const paymentMethod = paymentMethodSelect.value;
                        const paymentMethodText = paymentMethodSelect.options[paymentMethodSelect.selectedIndex].text;
                        
                        if (paymentMethod) {
                            const paymentCodes = {
                                'alipay': 'ALIPAY',
                                'jd': 'JD_PAY',
                                'apple': 'APPLE_PAY',
                                'huawei': 'HUAWEI_PAY'
                            };
                            
                            const paymentIcons = {
                                'alipay': '<i class="fab fa-alipay text-xl"></i>',
                                'jd': '<i class="fas fa-shopping-cart text-xl"></i>',
                                'apple': '<i class="fab fa-apple-pay text-xl"></i>',
                                'huawei': '<i class="fas fa-mobile-alt text-xl"></i>'
                            };
                            
                            const iconBgColors = {
                                'alipay': 'bg-blue-100 text-blue-600',
                                'jd': 'bg-red-100 text-red-600',
                                'apple': 'bg-gray-100 text-gray-600',
                                'huawei': 'bg-orange-100 text-orange-600'
                            };
                            
                            // 获取支付方式列表容器
                            const paymentContainer = document.querySelector('.space-y-6');
                            
                            // 创建新支付方式区域
                            const newPaymentMethod = document.createElement('div');
                            newPaymentMethod.className = 'payment-filter-item rounded-lg overflow-hidden';
                            newPaymentMethod.innerHTML = `
                                <div class="p-4 bg-white">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="payment-method-icon ${iconBgColors[paymentMethod]} mr-3">
                                                ${paymentIcons[paymentMethod]}
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">${paymentMethodText}</h4>
                                                <p class="text-xs text-gray-500">${paymentCodes[paymentMethod]}</p>
                                            </div>
                                            <div class="filter-tags-container">
                                            </div>
                                        </div>
                                        
                                        <div class="flex items-center">
                                            <div class="flex items-center mr-4">
                                                <label class="mr-2 text-sm text-gray-600">是否先乘后付</label>
                                                <label class="switch">
                                                    <input type="checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <button class="btn btn-primary flex items-center text-sm add-filter-btn">
                                                <i class="fas fa-plus mr-1"></i>
                                                <span>添加过滤条件</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            // 添加到容器
                            paymentContainer.appendChild(newPaymentMethod);
                            
                            // 为新添加的按钮绑定事件
                            const newAddFilterBtn = newPaymentMethod.querySelector('.add-filter-btn');
                            newAddFilterBtn.addEventListener('click', function() {
                                // 显示添加过滤条件模态框
                                document.getElementById('filterModal').classList.remove('hidden');
                                // 存储当前操作的支付方式项
                                currentPaymentItem = this.closest('.payment-filter-item');
                                
                                // 重置过滤条件表单
                                document.getElementById('filterType').value = '';
                                document.querySelectorAll('.filter-option').forEach(function(element) {
                                    element.classList.add('hidden');
                                });
                                document.getElementById('noFilterSelected').classList.remove('hidden');
                                
                                // 清空搜索框
                                const searchInput = document.querySelector('#filterModal input[placeholder="搜索过滤条件..."]');
                                if (searchInput) searchInput.value = '';
                            });
                            
                            // 关闭模态框
                            document.getElementById('paymentMethodModal').classList.add('hidden');
                            
                            // 重置表单
                            document.getElementById('paymentMethod').value = '';
                        }
                    });
                    
                    // 变量用于存储当前操作的支付方式项
                    let currentPaymentItem = null;

                    // --- 渲染规则选择逻辑 Start ---
                    const channelRulesData = [
                        { id: 'rule_marketing', name: '营销文案渲染', description: '根据活动或用户标签展示不同的营销文案。' },
                        { id: 'rule_bnpl', name: '先乘后付推荐渲染', description: '向符合条件的用户优先推荐先乘后付方式。' },
                        { id: 'rule_recent', name: '最近使用优先排序渲染', description: '将用户最近使用过的支付方式排在前面。' },
                        { id: 'rule_amount_limit', name: '金额限制高亮', description: '对有金额限制的支付方式进行特殊提示。' },
                        { id: 'rule_discount_tag', name: '优惠活动标签', description: '显示参与优惠活动的支付方式标签。' }
                    ];

                    let selectedRuleIds = new Set(); // 使用 Set 存储已选规则ID

                    const addChannelRuleBtn = document.getElementById('addChannelRuleBtn');
                    const channelRuleModal = document.getElementById('channelRuleModal');
                    const closeChannelRuleModalBtn = document.getElementById('closeChannelRuleModal');
                    const cancelChannelRuleSelectionBtn = document.getElementById('cancelChannelRuleSelection');
                    const confirmChannelRuleSelectionBtn = document.getElementById('confirmChannelRuleSelection');
                    const channelRuleSearchInput = document.getElementById('channelRuleSearchInput');
                    const channelRuleListContainer = document.getElementById('channelRuleListContainer');
                    const noChannelRulesFoundMsg = document.getElementById('noChannelRulesFound');
                    const selectedChannelRulesContainer = document.getElementById('selectedChannelRulesContainer');
                    const noChannelRulesMessage = document.getElementById('noChannelRulesMessage');

                    // 渲染模态框中的规则列表
                    function renderChannelRuleList(rules) {
                        channelRuleListContainer.innerHTML = ''; // 清空现有列表
                        noChannelRulesFoundMsg.classList.add('hidden');

                        if (rules.length === 0) {
                            noChannelRulesFoundMsg.classList.remove('hidden');
                            return;
                        }

                        rules.forEach(rule => {
                            const isSelected = selectedRuleIds.has(rule.id);
                            const ruleElement = document.createElement('div');
                            ruleElement.className = 'flex items-start p-3 bg-white rounded-md border border-gray-200 hover:bg-gray-50';
                            ruleElement.innerHTML = `
                                <input type="checkbox" id="rule_${rule.id}" value="${rule.id}" 
                                       class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 rule-checkbox" 
                                       ${isSelected ? 'checked' : ''}>
                                <label for="rule_${rule.id}" class="ml-3 flex-1 cursor-pointer">
                                    <div class="font-medium text-gray-800">${rule.name}</div>
                                    <div class="text-sm text-gray-500 mt-1">${rule.description}</div>
                                </label>
                            `;
                            channelRuleListContainer.appendChild(ruleElement);
                        });
                    }

                    // 渲染页面上已选的规则列表
                    function renderSelectedChannelRules() {
                        selectedChannelRulesContainer.innerHTML = ''; // 清空现有列表

                        if (selectedRuleIds.size === 0) {
                            noChannelRulesMessage.classList.remove('hidden');
                            selectedChannelRulesContainer.classList.add('hidden');
                            return;
                        }

                        noChannelRulesMessage.classList.add('hidden');
                        selectedChannelRulesContainer.classList.remove('hidden');

                        selectedRuleIds.forEach(ruleId => {
                            const rule = channelRulesData.find(r => r.id === ruleId);
                            if (rule) {
                                const ruleElement = document.createElement('div');
                                ruleElement.className = 'flex items-center justify-between p-3 bg-blue-50 rounded-md border border-blue-200';
                                ruleElement.innerHTML = `
                                    <div>
                                        <div class="font-medium text-gray-800">${rule.name}</div>
                                        <div class="text-sm text-gray-500 mt-1">${rule.description}</div>
                                    </div>
                                    <button class="btn btn-light btn-sm text-red-600 hover:bg-red-100 remove-channel-rule-btn" data-rule-id="${rule.id}">
                                        <i class="fas fa-times mr-1"></i>移除
                                    </button>
                                `;
                                selectedChannelRulesContainer.appendChild(ruleElement);
                            }
                        });
                    }

                    // 打开模态框
                    addChannelRuleBtn.addEventListener('click', () => {
                        renderChannelRuleList(channelRulesData); // 初始加载所有规则
                        channelRuleModal.classList.remove('hidden');
                        channelRuleSearchInput.value = ''; // 清空搜索框
                    });

                    // 关闭模态框
                    function closeChannelRuleModal() {
                        channelRuleModal.classList.add('hidden');
                    }
                    closeChannelRuleModalBtn.addEventListener('click', closeChannelRuleModal);
                    cancelChannelRuleSelectionBtn.addEventListener('click', closeChannelRuleModal);
                    channelRuleModal.addEventListener('click', (e) => {
                        if (e.target === channelRuleModal) { // 点击背景关闭
                            closeChannelRuleModal();
                        }
                    });

                    // 搜索规则
                    channelRuleSearchInput.addEventListener('input', () => {
                        const searchTerm = channelRuleSearchInput.value.toLowerCase();
                        const filteredRules = channelRulesData.filter(rule => 
                            rule.name.toLowerCase().includes(searchTerm) || 
                            rule.description.toLowerCase().includes(searchTerm)
                        );
                        renderChannelRuleList(filteredRules);
                    });

                    // 确认选择
                    confirmChannelRuleSelectionBtn.addEventListener('click', () => {
                        selectedRuleIds.clear(); // 清空旧的选择
                        const checkboxes = channelRuleListContainer.querySelectorAll('.rule-checkbox:checked');
                        checkboxes.forEach(cb => {
                            selectedRuleIds.add(cb.value);
                        });
                        renderSelectedChannelRules();
                        closeChannelRuleModal();
                    });

                    // 移除已选规则 (事件委托)
                    selectedChannelRulesContainer.addEventListener('click', (e) => {
                        if (e.target.classList.contains('remove-channel-rule-btn') || e.target.closest('.remove-channel-rule-btn')) {
                            const button = e.target.closest('.remove-channel-rule-btn');
                            const ruleIdToRemove = button.getAttribute('data-rule-id');
                            if (ruleIdToRemove) {
                                selectedRuleIds.delete(ruleIdToRemove);
                                renderSelectedChannelRules();
                            }
                        }
                    });

                    // 初始化显示状态
                    renderSelectedChannelRules();

                    // --- 渲染规则选择逻辑 End ---
                });

                

                
            </script>
            
            <!-- 条件模块选择模态框 -->
            <div id="conditionModuleModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
                <div class="bg-white rounded-lg w-full max-w-3xl">
                    <!-- 模态框头部 -->
                    <div class="flex justify-between items-center p-4 border-b">
                        <h4 class="text-lg font-medium text-gray-800">选择条件模块</h4>
                        <button id="closeConditionModuleModal" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <!-- 模态框内容 -->
                    <div class="p-4">
                        <div id="conditionModuleListContainer">
                            <!-- 条件模板列表将在这里动态加载 -->
                        </div>
                    </div>
                    
                    <!-- 模态框底部按钮 -->
                    <div class="flex justify-end p-4 border-t">
                        <button id="cancelConditionModule" class="btn btn-light mr-2">取消</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 支付方式编辑模态框 -->
    <div id="paymentMethodEditModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-3xl">
            <!-- 模态框头部 -->
            <div class="flex justify-between items-center p-4 border-b">
                <h4 class="text-lg font-medium text-gray-800">编辑支付方式</h4>
                <button id="closeEditModal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 模态框内容 -->
            <div class="p-4">
                <form id="paymentMethodEditForm">
                    <input type="hidden" id="editPaymentId">
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2" for="displayTitle">
                            显示标题 <span class="text-red-500">*</span>
                        </label>
                        <input class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" 
                               id="displayTitle" type="text" placeholder="请输入显示标题">
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2" for="displayDesc">
                            显示描述
                        </label>
                        <textarea class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" 
                                  id="displayDesc" rows="3" placeholder="请输入显示描述"></textarea>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            关联支付产品 <span class="text-red-500">*</span>
                        </label>
                        <div class="grid grid-cols-2 gap-3 mt-2">
                            <div class="flex items-center">
                                <input id="product1" name="relatedProducts" type="checkbox" value="1" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="product1" class="ml-2 text-gray-700 text-sm">
                                    H5支付
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="product2" name="relatedProducts" type="checkbox" value="2" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="product2" class="ml-2 text-gray-700 text-sm">
                                    JSAPI支付
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="product3" name="relatedProducts" type="checkbox" value="3" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="product3" class="ml-2 text-gray-700 text-sm">
                                    APP支付
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="product4" name="relatedProducts" type="checkbox" value="4" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="product4" class="ml-2 text-gray-700 text-sm">
                                    WAP支付
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="product5" name="relatedProducts" type="checkbox" value="5" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="product5" class="ml-2 text-gray-700 text-sm">
                                    Native支付
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="product6" name="relatedProducts" type="checkbox" value="6" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                <label for="product6" class="ml-2 text-gray-700 text-sm">
                                    小程序支付
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="flex items-center">
                            <input id="enableBnpl" type="checkbox" class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                            <label for="enableBnpl" class="ml-2 text-gray-700 text-sm font-medium">
                                是否先乘后付
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1 ml-6">启用后将支持用户使用分期付款等延时支付方式</p>
                    </div>
                </form>
            </div>
            
            <!-- 模态框底部按钮 -->
            <div class="flex justify-end p-4 border-t">
                <button class="btn btn-light mr-2" id="cancelEditBtn">
                    取消
                </button>
                <button class="btn btn-primary" id="savePaymentBtn">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 添加过滤条件模态框 -->
    <div id="filterModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden overflow-y-auto overflow-x-hidden">
        <div class="bg-white rounded-lg w-full max-w-3xl mx-auto my-8">
            <!-- 模态框头部 -->
            <div class="flex justify-between items-center p-4 border-b">
                <h4 class="text-lg font-medium text-gray-800">添加过滤条件</h4>
                <button id="closeFilterModal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 模态框内容 -->
            <div class="p-4">
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-3">
                        <p class="text-sm text-gray-600">请选择一个过滤条件应用到当前支付方式</p>
                        <a href="condition_rule.html" target="_blank" class="text-blue-600 text-sm hover:underline">
                            <i class="fas fa-external-link-alt mr-1"></i>管理条件规则
                        </a>
                    </div>
                    
                    <!-- 标签页切换 -->
                    <div class="border-b border-gray-200 mb-4">
                        <nav class="flex -mb-px">
                            <button id="filterTemplateTab" class="tab-button py-3 px-4 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                                条件模板
                            </button>
                            <button id="filterTypeTab" class="tab-button py-3 px-4 border-b-2 border-blue-500 text-blue-600 font-medium text-sm">
                                过滤条件类型
                            </button>
                            
                        </nav>
                    </div>
                    
                    <!-- 过滤条件类型面板 -->
                    <div id="filterTypePanel" class="tab-panel">
                        <div class="relative mb-4">
                            <input type="text" placeholder="搜索过滤条件..." class="w-full pl-10 pr-4 py-2 border rounded-md">
                            <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        
                        <div class="bg-gray-50 p-3 rounded-md mb-3">
                            <label class="block text-gray-700 text-sm font-medium mb-2" for="filterType">
                            过滤条件类型
                        </label>
                            <select class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline" id="filterType">
                            <option value="">请选择过滤条件类型</option>
                            <option value="userAmount">用户支付金额过滤</option>
                            <option value="tripTime">行程时间段过滤</option>
                            <option value="userCredit">用户信用等级过滤</option>
                            <option value="userLocation">用户地理位置过滤</option>
                            <option value="deviceType">设备类型过滤</option>
                        </select>
                        </div>
                        
                        <div class="max-h-80 overflow-y-auto border rounded-md">
                        <!-- 用户支付金额过滤设置 -->
                            <div id="userAmountFilter" class="filter-option hidden p-4 bg-white">
                            <div class="mb-4">
                                    <label class="block text-gray-700 text-sm font-medium mb-2">金额范围</label>
                                <div class="flex items-center">
                                        <input class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 mr-2 leading-tight focus:outline-none focus:shadow-outline" type="number" placeholder="最小金额">
                                    <span class="mx-2">-</span>
                                        <input class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" type="number" placeholder="最大金额">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 行程时间段过滤设置 -->
                            <div id="tripTimeFilter" class="filter-option hidden p-4 bg-white">
                            <div class="mb-4">
                                    <label class="block text-gray-700 text-sm font-medium mb-2">时间段</label>
                                <div class="flex items-center">
                                        <input class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 mr-2 leading-tight focus:outline-none focus:shadow-outline" type="time">
                                    <span class="mx-2">-</span>
                                        <input class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" type="time">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 用户信用等级过滤设置 -->
                            <div id="userCreditFilter" class="filter-option hidden p-4 bg-white">
                            <div class="mb-4">
                                    <label class="block text-gray-700 text-sm font-medium mb-2">最低信用等级</label>
                                    <select class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline">
                                    <option value="1">一星</option>
                                    <option value="2">二星</option>
                                    <option value="3">三星</option>
                                    <option value="4">四星</option>
                                    <option value="5">五星</option>
                                </select>
                                </div>
                            </div>
                            
                            <!-- 未选择提示 -->
                            <div id="noFilterSelected" class="p-8 text-center text-gray-500">
                                <i class="fas fa-filter text-gray-300 text-4xl mb-3"></i>
                                <p>请先选择一个过滤条件类型</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 条件模板面板 -->
                    <div id="filterTemplatePanel" class="tab-panel hidden">
                        <div id="filterTemplateListContainer">
                            <!-- 条件模板列表将在这里动态加载 -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 模态框底部按钮 -->
            <div class="flex justify-end p-4 border-t">
                <button id="cancelFilter" class="btn btn-light mr-2">取消</button>
                <button id="saveFilter" class="btn btn-primary">确认</button>
            </div>
        </div>
    </div>

    <!-- 添加支付方式模态框 -->
    <div id="paymentMethodModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg w-full max-w-3xl">
            <!-- 模态框头部 -->
            <div class="flex justify-between items-center p-4 border-b">
                <h4 class="text-lg font-medium text-gray-800">添加支付方式</h4>
                <button id="closePaymentModal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 模态框内容 -->
            <div class="p-4">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-medium mb-2" for="paymentMethod">
                        支付方式
                    </label>
                    <select class="appearance-none border rounded-md w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline" id="paymentMethod">
                        <option value="">请选择支付方式</option>
                        <option value="alipay">支付宝</option>
                        <option value="jd">京东支付</option>
                        <option value="apple">Apple Pay</option>
                        <option value="huawei">华为支付</option>
                    </select>
                </div>
            </div>
            
            <!-- 模态框底部按钮 -->
            <div class="flex justify-end p-4 border-t">
                <button id="cancelPayment" class="btn btn-light mr-2">取消</button>
                <button id="savePayment" class="btn btn-primary">添加</button>
            </div>
        </div>
    </div>

    <!-- 全局JavaScript -->
    <script>
        // 支付方式信息映射表
        const paymentMethodsData = {
            "1": { 
                title: "微信支付", 
                desc: "中国领先的移动支付平台",
                productIds: ["1", "2", "6"],
                enableBnpl: false
            },
            "2": { 
                title: "支付宝", 
                desc: "全球领先的独立第三方支付平台",
                productIds: ["1", "3", "4"],
                enableBnpl: true
            },
            "3": { 
                title: "京东支付", 
                desc: "京东集团旗下支付平台",
                productIds: ["1", "3"],
                enableBnpl: false
            },
            "4": { 
                title: "银联", 
                desc: "中国银联提供的支付服务",
                productIds: ["5"],
                enableBnpl: false
            }
        };
        
        // 条件模板数据
        const conditionTemplateData = [
            {
                id: 1,
                name: "乘客角色条件",
                code: "CM001",
                conditionCount: 2,
                createTime: "2023-10-15 14:30"
            },
            {
                id: 2,
                name: "高级会员促销",
                code: "CM002",
                conditionCount: 3,
                createTime: "2023-10-16 09:45"
            },
            {
                id: 3,
                name: "特定用户群体",
                code: "CM003",
                conditionCount: 4,
                createTime: "2023-10-17 11:20"
            },
            {
                id: 4,
                name: "特定时间段优惠",
                code: "CM004",
                conditionCount: 2,
                createTime: "2023-10-18 15:10"
            },
            {
                id: 5,
                name: "地域限制条件",
                code: "CM005",
                conditionCount: 1,
                createTime: "2023-10-19 10:05"
            },
            {
                id: 6,
                name: "设备类型条件",
                code: "CM006",
                conditionCount: 3,
                createTime: "2023-10-20 14:25"
            },
            {
                id: 7,
                name: "消费金额条件",
                code: "CM007",
                conditionCount: 2,
                createTime: "2023-10-21 09:30"
            },
            {
                id: 8,
                name: "历史订单条件",
                code: "CM008",
                conditionCount: 5,
                createTime: "2023-10-22 16:45"
            },
            {
                id: 9,
                name: "支付方式偏好",
                code: "CM009",
                conditionCount: 3,
                createTime: "2023-10-23 11:15"
            },
            {
                id: 10,
                name: "信用等级条件",
                code: "CM010",
                conditionCount: 4,
                createTime: "2023-10-24 13:50"
            }
        ];
        
        // 条件模板列表类
        class ConditionTemplateList {
            constructor(container, options = {}) {
                this.container = container;
                this.options = Object.assign({
                    pageSize: 5,
                    onSelect: null,
                    buttonLabel: "选择"
                }, options);
                
                this.data = [...conditionTemplateData];
                this.filteredData = [...this.data];
                this.currentPage = 1;
                
                this.init();
            }
            
            init() {
                // 获取模板内容并克隆到容器
                const template = document.getElementById('conditionTemplateList');
                this.container.innerHTML = '';
                this.container.appendChild(template.content.cloneNode(true));
                
                // 获取DOM元素
                this.searchInput = this.container.querySelector('#templateSearchInput');
                this.tableBody = this.container.querySelector('#templateTableBody');
                this.totalItemsEl = this.container.querySelector('#totalItems');
                this.currentPageEl = this.container.querySelector('#currentPage');
                this.totalPagesEl = this.container.querySelector('#totalPages');
                this.prevPageBtn = this.container.querySelector('#prevPageBtn');
                this.nextPageBtn = this.container.querySelector('#nextPageBtn');
                
                // 绑定事件
                this.searchInput.addEventListener('input', () => this.handleSearch());
                this.prevPageBtn.addEventListener('click', () => this.prevPage());
                this.nextPageBtn.addEventListener('click', () => this.nextPage());
                
                // 初始加载数据
                this.renderTable();
                this.updatePagination();
            }
            
            handleSearch() {
                const searchValue = this.searchInput.value.toLowerCase();
                this.filteredData = this.data.filter(item => {
                    return (
                        item.name.toLowerCase().includes(searchValue) ||
                        item.code.toLowerCase().includes(searchValue)
                    );
                });
                
                this.currentPage = 1;
                this.renderTable();
                this.updatePagination();
            }
            
            renderTable() {
                this.tableBody.innerHTML = '';
                
                const start = (this.currentPage - 1) * this.options.pageSize;
                const end = start + this.options.pageSize;
                const pageData = this.filteredData.slice(start, end);
                
                if (pageData.length === 0) {
                    const emptyRow = document.createElement('tr');
                    emptyRow.innerHTML = `<td colspan="5" class="px-6 py-4 text-center text-gray-500">未找到匹配的条件模板</td>`;
                    this.tableBody.appendChild(emptyRow);
                    return;
                }
                
                pageData.forEach(item => {
                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <button class="btn btn-light select-template-btn" data-id="${item.id}" data-name="${item.name}" data-code="${item.code}">
                                <i class="fas fa-check text-blue-600"></i>
                            </button>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.name}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.code}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.conditionCount}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.createTime}</td>
                    `;
                    this.tableBody.appendChild(row);
                });
                
                // 绑定选择按钮事件
                this.tableBody.querySelectorAll('.select-template-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const id = e.currentTarget.getAttribute('data-id');
                        const name = e.currentTarget.getAttribute('data-name');
                        const code = e.currentTarget.getAttribute('data-code');
                        
                        if (typeof this.options.onSelect === 'function') {
                            this.options.onSelect({
                                id: id, 
                                name: name, 
                                code: code
                            });
                        }
                    });
                });
            }
            
            updatePagination() {
                const totalItems = this.filteredData.length;
                const totalPages = Math.ceil(totalItems / this.options.pageSize);
                
                this.totalItemsEl.textContent = totalItems;
                this.currentPageEl.textContent = this.currentPage;
                this.totalPagesEl.textContent = totalPages || 1;
                
                // 更新按钮状态
                this.prevPageBtn.disabled = this.currentPage <= 1;
                this.nextPageBtn.disabled = this.currentPage >= totalPages;
            }
            
            prevPage() {
                if (this.currentPage > 1) {
                    this.currentPage--;
                    this.renderTable();
                    this.updatePagination();
                }
            }
            
            nextPage() {
                const totalPages = Math.ceil(this.filteredData.length / this.options.pageSize);
                if (this.currentPage < totalPages) {
                    this.currentPage++;
                    this.renderTable();
                    this.updatePagination();
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // 确保过滤条件模态框初始状态正确
            const noFilterSelected = document.getElementById('noFilterSelected');
            if (noFilterSelected) noFilterSelected.classList.remove('hidden');
            
            // 侧边栏子菜单折叠展开
            document.querySelectorAll('.menu-toggle').forEach(item => {
                const submenu = item.nextElementSibling;
                const icon = item.querySelector('.submenu-icon');
                // 检查当前菜单项是否应该展开 (基于页面URL或active class)
                let isActive = false;
                if (submenu) { // 确保submenu存在
                    submenu.querySelectorAll('a').forEach(link => {
                        // 完全匹配URL或者父级菜单是active
                        if (link.href === window.location.href || item.closest('.nav-item').classList.contains('active')) {
                           if (link.href === window.location.href) {
                                isActive = true;
                                link.classList.add('font-semibold', 'text-primary'); // 高亮当前子菜单项
                           }
                        }
                    });
                }
                
                // 如果子菜单包含活动项 或 父菜单是active，则展开
                if (isActive || item.closest('.nav-item').classList.contains('active')) {
                     // 对于父菜单项，如果它自己不是链接且包含活动子项，则展开
                     if(!item.closest('.nav-item').querySelector('a[href]') && isActive) {
                         submenu.classList.add('active');
                         icon.style.transform = 'rotate(180deg)';
                     } else if (item.closest('.nav-item').classList.contains('active') && submenu) {
                         // 如果父菜单是active，也展开它
                         submenu.classList.add('active');
                         icon.style.transform = 'rotate(180deg)';
                     }
                }
                
                // 添加点击事件
                 if(item && submenu) { // 确保元素存在
                    item.addEventListener('click', function() {
                        if (submenu.classList.contains('active')) {
                            submenu.classList.remove('active');
                            icon.style.transform = 'rotate(0deg)';
                        } else {
                            // 关闭其他打开的子菜单
                            document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                                if (openSubmenu !== submenu) {
                                    openSubmenu.classList.remove('active');
                                    const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                                    if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                                }
                            });
                            submenu.classList.add('active');
                            icon.style.transform = 'rotate(180deg)';
                        }
                    });
                }
            });

            // 用户下拉菜单
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown) {
                userDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                    userMenu.classList.toggle('hidden');
                });
            }

            // 点击页面其他位置关闭用户菜单
            document.addEventListener('click', function(event) {
                if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                    userMenu.classList.add('hidden');
                }
            });
            
            // 支付方式默认选择
            const defaultRadios = document.querySelectorAll('.default-radio');
            defaultRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    document.querySelectorAll('.payment-method-item').forEach(item => {
                        item.classList.remove('default-selected');
                    });
                    
                    if (this.checked) {
                        const paymentItem = this.closest('.payment-method-item');
                        paymentItem.classList.add('default-selected');
                        
                        // 更新所有单选按钮的样式
                        document.querySelectorAll('.default-radio').forEach(r => {
                            const slider = r.nextElementSibling;
                            if (r.checked) {
                                slider.style.backgroundColor = 'var(--primary-color)';
                            } else {
                                slider.style.backgroundColor = '#ccc';
                            }
                        });
                    }
                });
            });
            
            // 添加支付方式按钮事件
            document.getElementById('addPaymentMethodBtn').addEventListener('click', function() {
                // 显示添加支付方式模态框
                document.getElementById('paymentMethodModal').classList.remove('hidden');
            });
            
            // 添加过滤条件按钮事件
            document.getElementById('addPaymentMethodFilterBtn').addEventListener('click', function() {
                // 显示添加支付方式模态框
                document.getElementById('paymentMethodModal').classList.remove('hidden');
            });
            
            
            
            // 支付方式编辑按钮事件
            const editPaymentBtns = document.querySelectorAll('.edit-payment-btn');
            const editModal = document.getElementById('paymentMethodEditModal');
            const closeEditModalBtn = document.getElementById('closeEditModal');
            const cancelEditBtn = document.getElementById('cancelEditBtn');
            const savePaymentBtn = document.getElementById('savePaymentBtn');
            
            // 编辑按钮点击事件
            editPaymentBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const paymentItem = this.closest('.payment-method-item');
                    const paymentId = paymentItem.dataset.id;
                    
                    // 填充表单数据
                    populateEditForm(paymentId);
                    
                    // 显示模态框
                    editModal.classList.remove('hidden');
                });
            });
            
            // 关闭模态框
            function closeEditModal() {
                editModal.classList.add('hidden');
            }
            
            // 关闭按钮点击事件
            closeEditModalBtn.addEventListener('click', closeEditModal);
            cancelEditBtn.addEventListener('click', closeEditModal);
            
            // 点击模态框外部关闭
            editModal.addEventListener('click', function(e) {
                if (e.target === editModal) {
                    closeEditModal();
                }
            });
            
            // 填充编辑表单
            function populateEditForm(paymentId) {
                const data = paymentMethodsData[paymentId];
                
                document.getElementById('editPaymentId').value = paymentId;
                document.getElementById('displayTitle').value = data.title;
                document.getElementById('displayDesc').value = data.desc || '';
                
                // 清除所有选中状态
                document.querySelectorAll('input[name="relatedProducts"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                // 设置关联产品选中状态
                if (data.productIds && data.productIds.length) {
                    // 如果是数组形式
                    data.productIds.forEach(id => {
                        const checkbox = document.getElementById(`product${id}`);
                        if (checkbox) checkbox.checked = true;
                    });
                } else if (data.productId) {
                    // 如果是单一ID形式(兼容旧数据)
                    const checkbox = document.getElementById(`product${data.productId}`);
                    if (checkbox) checkbox.checked = true;
                }
                
                document.getElementById('enableBnpl').checked = data.enableBnpl;
            }
            
            // 保存按钮点击事件
            savePaymentBtn.addEventListener('click', function() {
                const paymentId = document.getElementById('editPaymentId').value;
                const title = document.getElementById('displayTitle').value.trim();
                const desc = document.getElementById('displayDesc').value.trim();
                
                // 获取选中的关联产品
                const productIds = [];
                document.querySelectorAll('input[name="relatedProducts"]:checked').forEach(checkbox => {
                    productIds.push(checkbox.value);
                });
                
                const enableBnpl = document.getElementById('enableBnpl').checked;
                
                // 验证表单
                if (!title) {
                    alert('请输入显示标题');
                    return;
                }
                
                if (productIds.length === 0) {
                    alert('请至少选择一个关联支付产品');
                    return;
                }
                
                // 更新数据
                paymentMethodsData[paymentId] = {
                    title,
                    desc,
                    productIds,
                    enableBnpl
                };
                
                // 更新UI
                const paymentItem = document.querySelector(`.payment-method-item[data-id="${paymentId}"]`);
                paymentItem.querySelector('h4').textContent = title;
                
                // 显示成功提示
                alert('保存成功！');
                
                // 关闭模态框
                closeEditModal();
            });
            
            // 支付方式删除按钮事件
            const deletePaymentBtns = document.querySelectorAll('.delete-payment-btn');
            deletePaymentBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const paymentItem = this.closest('.payment-method-item');
                    const paymentId = paymentItem.dataset.id;
                    if (confirm(`确定要删除此支付方式吗？`)) {
                        alert(`删除支付方式 ID: ${paymentId}`);
                    }
                });
            });
            
            // 选择条件模块按钮事件
            document.getElementById('selectConditionModuleBtn').addEventListener('click', function() {
                // 显示条件模块选择模态框
                document.getElementById('conditionModuleModal').classList.remove('hidden');
            });
            
            // 关闭条件模块选择模态框
            document.getElementById('closeConditionModuleModal').addEventListener('click', function() {
                document.getElementById('conditionModuleModal').classList.add('hidden');
            });
            
            document.getElementById('cancelConditionModule').addEventListener('click', function() {
                document.getElementById('conditionModuleModal').classList.add('hidden');
            });
            
            // 选择条件模块
            document.querySelectorAll('.select-condition-module').forEach(btn => {
                btn.addEventListener('click', function() {
                    const moduleId = this.getAttribute('data-id');
                    const moduleName = this.getAttribute('data-name');
                    const moduleCode = this.getAttribute('data-code');
                    
                    // 更新条件模块显示区域
                    const noConditionsMessage = document.getElementById('noConditionsMessage');
                    noConditionsMessage.innerHTML = `
                        <div class="selected-condition-module p-4 bg-blue-50 rounded-md border border-blue-200 w-full">
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 rounded-md bg-blue-100 flex items-center justify-center mr-3">
                                            <i class="fas fa-filter text-blue-600"></i>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-800">${moduleName}</h4>
                                            <p class="text-xs text-gray-500">${moduleCode}</p>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <a href="condition_module_view.html?id=${moduleId}" target="_blank" class="btn btn-light text-sm mr-2">
                                        <i class="fas fa-eye mr-1"></i>查看详情
                                    </a>
                                    <button class="btn btn-danger text-sm" id="removeConditionModule">
                                        <i class="fas fa-times mr-1"></i>移除
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 绑定移除条件模块按钮事件
                    document.getElementById('removeConditionModule').addEventListener('click', function() {
                        noConditionsMessage.innerHTML = `
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span class="text-gray-600">请选择一个条件模块</span>
                        `;
                    });
                    
                    // 关闭模态框
                    document.getElementById('conditionModuleModal').classList.add('hidden');
                });
            });
            
            // 预览JSON按钮事件
            document.getElementById('previewConditionsBtn').addEventListener('click', function() {
                alert('显示条件JSON预览');
            });

            // 添加过滤条件按钮
            const addFilterBtns = document.querySelectorAll('.add-filter-btn');
            addFilterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 显示添加过滤条件模态框
                    document.getElementById('filterModal').classList.remove('hidden');
                    // 存储当前操作的支付方式项
                    currentPaymentItem = this.closest('.payment-filter-item');
                    
                    // 重置过滤条件表单
                    document.getElementById('filterType').value = '';
                    document.querySelectorAll('.filter-option').forEach(function(element) {
                        element.classList.add('hidden');
                    });
                    document.getElementById('noFilterSelected').classList.remove('hidden');
                    
                    // 清空搜索框
                    const searchInput = document.querySelector('#filterModal input[placeholder="搜索过滤条件..."]');
                    if (searchInput) searchInput.value = '';
                });
            });
            
            // 关闭过滤条件模态框
            document.getElementById('closeFilterModal').addEventListener('click', function() {
                document.getElementById('filterModal').classList.add('hidden');
            });
            
            document.getElementById('cancelFilter').addEventListener('click', function() {
                document.getElementById('filterModal').classList.add('hidden');
            });
            
            // 关闭添加支付方式模态框
            document.getElementById('closePaymentModal').addEventListener('click', function() {
                document.getElementById('paymentMethodModal').classList.add('hidden');
            });
            
            document.getElementById('cancelPayment').addEventListener('click', function() {
                document.getElementById('paymentMethodModal').classList.add('hidden');
            });
            
            // 过滤条件类型选择变化事件
            document.getElementById('filterType').addEventListener('change', function() {
                // 隐藏所有过滤选项
                document.querySelectorAll('.filter-option').forEach(function(element) {
                    element.classList.add('hidden');
                });
                
                // 显示选中的过滤选项
                const selectedFilter = this.value;
                if (selectedFilter) {
                    document.getElementById(`${selectedFilter}Filter`).classList.remove('hidden');
                    document.getElementById('noFilterSelected').classList.add('hidden');
                } else {
                    document.getElementById('noFilterSelected').classList.remove('hidden');
                }
            });
            
            // 过滤条件搜索框事件
            const filterSearchInput = document.querySelector('#filterModal input[placeholder="搜索过滤条件..."]');
            if (filterSearchInput) {
                filterSearchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const filterOptions = document.getElementById('filterType').options;
                    
                    // 根据搜索词过滤下拉选项
                    for (let i = 0; i < filterOptions.length; i++) {
                        const option = filterOptions[i];
                        const optionText = option.text.toLowerCase();
                        
                        if (optionText.includes(searchTerm) || searchTerm === '') {
                            option.style.display = '';
                        } else {
                            option.style.display = 'none';
                        }
                    }
                });
            }
            
            // 保存过滤条件
            document.getElementById('saveFilter').addEventListener('click', function() {
                // 获取选择的过滤条件类型
                const filterType = document.getElementById('filterType').value;
                const filterNames = {
                    'userAmount': '用户支付金额过滤',
                    'tripTime': '行程时间段过滤',
                    'userCredit': '用户信用等级过滤',
                    'userLocation': '用户地理位置过滤',
                    'deviceType': '设备类型过滤'
                };
                
                if (filterType && currentPaymentItem) {
                    const tagColors = {
                        'userAmount': 'blue',
                        'tripTime': 'green',
                        'userCredit': 'purple',
                        'userLocation': 'yellow',
                        'deviceType': 'red'
                    };
                    
                    // 查找或创建过滤标签容器
                    let filterTagsContainer = currentPaymentItem.querySelector('.filter-tags-container');
                    if (!filterTagsContainer) {
                        filterTagsContainer = document.createElement('div');
                        filterTagsContainer.className = 'filter-tags-container';
                        
                        // 添加到支付方式名称后面
                        const paymentNameContainer = currentPaymentItem.querySelector('.flex.items-center').querySelector('div:nth-child(2)').parentNode;
                        paymentNameContainer.appendChild(filterTagsContainer);
                    }
                    
                    // 创建新标签
                    const newTag = document.createElement('div');
                    newTag.className = `tag tag-${tagColors[filterType]}`;
                    newTag.innerHTML = `<span>${filterNames[filterType]}</span>`;
                    
                    // 添加到容器
                    filterTagsContainer.appendChild(newTag);
                    
                    // 关闭模态框
                    document.getElementById('filterModal').classList.add('hidden');
                    
                    // 重置表单
                    document.getElementById('filterType').value = '';
                    document.querySelectorAll('.filter-option').forEach(function(element) {
                        element.classList.add('hidden');
                    });
                    document.getElementById('noFilterSelected').classList.remove('hidden');
                }
            });
            
            // 保存支付方式
            document.getElementById('savePayment').addEventListener('click', function() {
                // 获取选择的支付方式
                const paymentMethodSelect = document.getElementById('paymentMethod');
                const paymentMethod = paymentMethodSelect.value;
                const paymentMethodText = paymentMethodSelect.options[paymentMethodSelect.selectedIndex].text;
                
                if (paymentMethod) {
                    const paymentCodes = {
                        'alipay': 'ALIPAY',
                        'jd': 'JD_PAY',
                        'apple': 'APPLE_PAY',
                        'huawei': 'HUAWEI_PAY'
                    };
                    
                    const paymentIcons = {
                        'alipay': '<i class="fab fa-alipay text-xl"></i>',
                        'jd': '<i class="fas fa-shopping-cart text-xl"></i>',
                        'apple': '<i class="fab fa-apple-pay text-xl"></i>',
                        'huawei': '<i class="fas fa-mobile-alt text-xl"></i>'
                    };
                    
                    const iconBgColors = {
                        'alipay': 'bg-blue-100 text-blue-600',
                        'jd': 'bg-red-100 text-red-600',
                        'apple': 'bg-gray-100 text-gray-600',
                        'huawei': 'bg-orange-100 text-orange-600'
                    };
                    
                    // 获取支付方式列表容器
                    const paymentContainer = document.querySelector('.space-y-6');
                    
                    // 创建新支付方式区域
                    const newPaymentMethod = document.createElement('div');
                    newPaymentMethod.className = 'payment-filter-item rounded-lg overflow-hidden';
                    newPaymentMethod.innerHTML = `
                        <div class="p-4 bg-white">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="payment-method-icon ${iconBgColors[paymentMethod]} mr-3">
                                        ${paymentIcons[paymentMethod]}
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">${paymentMethodText}</h4>
                                        <p class="text-xs text-gray-500">${paymentCodes[paymentMethod]}</p>
                                    </div>
                                    <div class="filter-tags-container">
                                    </div>
                                </div>
                                
                                <div class="flex items-center">
                                    <button class="btn btn-primary flex items-center text-sm add-filter-btn">
                                        <i class="fas fa-plus mr-1"></i>
                                        <span>添加过滤条件</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 添加到容器
                    paymentContainer.appendChild(newPaymentMethod);
                    
                    // 为新添加的按钮绑定事件
                    const newAddFilterBtn = newPaymentMethod.querySelector('.add-filter-btn');
                    newAddFilterBtn.addEventListener('click', function() {
                        // 显示添加过滤条件模态框
                        document.getElementById('filterModal').classList.remove('hidden');
                        // 存储当前操作的支付方式项
                        currentPaymentItem = this.closest('.payment-filter-item');
                        
                        // 重置过滤条件表单
                        document.getElementById('filterType').value = '';
                        document.querySelectorAll('.filter-option').forEach(function(element) {
                            element.classList.add('hidden');
                        });
                        document.getElementById('noFilterSelected').classList.remove('hidden');
                        
                        // 清空搜索框
                        const searchInput = document.querySelector('#filterModal input[placeholder="搜索过滤条件..."]');
                        if (searchInput) searchInput.value = '';
                    });
                    
                    // 关闭模态框
                    document.getElementById('paymentMethodModal').classList.add('hidden');
                    
                    // 重置表单
                    document.getElementById('paymentMethod').value = '';
                }
            });
            
            // 变量用于存储当前操作的支付方式项
            let currentPaymentItem = null;
        });

        

        
    </script>
    
    <!-- 条件模板公共模块列表模板 -->
    <template id="conditionTemplateList">
        <div class="condition-template-list">
            <div class="mb-4">
                <div class="flex justify-between items-center mb-3">
                    <p class="text-sm text-gray-600">请选择一个条件模板</p>
                    <a href="condition_module.html" target="_blank" class="text-blue-600 text-sm hover:underline">
                        <i class="fas fa-external-link-alt mr-1"></i>管理条件模块
                    </a>
                </div>
                <div class="relative">
                    <input type="text" id="templateSearchInput" placeholder="搜索条件模块..." class="w-full pl-10 pr-4 py-2 border rounded-md">
                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                </div>
            </div>
            
            <div class="overflow-x-auto max-h-80">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">选择</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模块名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模块编码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">条件组数量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                        </tr>
                    </thead>
                    <tbody id="templateTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 表格内容将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页控件 -->
            <div class="flex justify-between items-center mt-4">
                <div class="text-sm text-gray-600">
                    共 <span id="totalItems">0</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button id="prevPageBtn" class="btn btn-light flex items-center text-sm px-3 py-1" disabled>
                        <i class="fas fa-chevron-left mr-1"></i> 上一页
                    </button>
                    <span class="text-sm text-gray-600">第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页</span>
                    <button id="nextPageBtn" class="btn btn-light flex items-center text-sm px-3 py-1" disabled>
                        下一页 <i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- 添加渲染规则选择模态框 -->
    <div id="channelRuleModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden overflow-y-auto overflow-x-hidden">
        <div class="bg-white rounded-lg w-full max-w-2xl mx-auto my-8">
            <!-- 模态框头部 -->
            <div class="flex justify-between items-center p-4 border-b">
                <h4 class="text-lg font-medium text-gray-800">选择渲染规则</h4>
                <button id="closeChannelRuleModal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 模态框内容 -->
            <div class="p-4">
                <div class="mb-4">
                    <div class="relative">
                        <input type="text" id="channelRuleSearchInput" placeholder="按名称或描述搜索渲染规则..." class="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </div>

                <div id="channelRuleListContainer" class="max-h-80 overflow-y-auto border rounded-md p-2 space-y-2 bg-gray-50">
                    <!-- 渲染规则列表将在这里动态加载 -->
                    <div id="noChannelRulesFound" class="text-center text-gray-500 py-4 hidden">未找到匹配的规则</div>
                </div>
            </div>

            <!-- 模态框底部按钮 -->
            <div class="flex justify-end p-4 border-t">
                <button id="cancelChannelRuleSelection" class="btn btn-light mr-2">取消</button>
                <button id="confirmChannelRuleSelection" class="btn btn-primary">确认</button>
            </div>
        </div>
    </div>
</body>
</html> 
_file>