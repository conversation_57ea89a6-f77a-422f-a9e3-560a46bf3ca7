<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收银台及渠道管理系统 - 编辑支付场景</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 250px;
            width: calc(100% - 250px);
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            border-radius: 1rem;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08), 
                        0 2px 8px rgba(0, 0, 0, 0.04), 
                        inset 0 -1px 0 rgba(0, 0, 0, 0.05);
            transition: all 0.35s ease;
            overflow: hidden;
            border: 1px solid rgba(226, 232, 240, 0.7);
            position: relative;
        }
        
        .card:hover {
            box-shadow: 0 12px 30px rgba(59, 124, 254, 0.12), 
                        0 4px 8px rgba(59, 124, 254, 0.06), 
                        inset 0 -2px 0 rgba(59, 124, 254, 0.15);
            transform: translateY(-3px) scale(1.01);
            border-color: rgba(59, 124, 254, 0.2);
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .card:hover::before {
            opacity: 1;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.25rem 1.5rem;
            background-color: rgba(248, 250, 252, 0.8);
            border-bottom: 1px solid #e2e8f0;
            position: relative;
        }
        
        .card-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
            display: flex;
            align-items: center;
        }
        
        .card-header h3 i {
            margin-right: 0.75rem;
            color: var(--primary-color);
        }
        
        .card-body {
            padding: 1.5rem;
            background-color: white;
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        
        /* 条件卡片样式 */
        .condition-card {
            border: 1px dashed #cbd5e1;
            transition: all 0.3s ease;
            background: white;
        }
        
        .condition-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        /* 条件模块卡片样式 */
        .condition-module-card {
            border: 1px solid rgba(226, 232, 240, 0.9);
            border-radius: 0.75rem;
            overflow: hidden;
            margin-bottom: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            background-color: #fff;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .condition-module-card:hover {
            border-color: rgba(59, 124, 254, 0.3);
            box-shadow: 0 8px 16px rgba(59, 124, 254, 0.1);
            transform: translateY(-2px);
        }
        
        .condition-module-card .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px 18px;
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            border-bottom: 1px solid #e2e8f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .condition-module-card .card-header:hover {
            background: linear-gradient(145deg, #f8fafc, #f1f5f9);
        }
        
        .condition-module-card .card-title {
            font-weight: 600;
            color: #334155;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .condition-module-card .card-actions {
            display: flex;
            gap: 8px;
        }
        
        .condition-module-card .card-body {
            padding: 16px;
            background-color: #fff;
            height: 0;
            overflow: hidden;
            transition: height 0.3s ease;
            opacity: 0;
        }
        
        .condition-module-card .card-body.expanded {
            height: auto;
            opacity: 1;
            border-top: 1px solid #e2e8f0;
        }
        
        .condition-module-card .module-tag {
            display: inline-block;
            padding: 4px 8px;
            background-color: #e2e8f0;
            color: #475569;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-right: 8px;
        }
        
        .condition-module-card .rule-count {
            display: inline-flex;
            align-items: center;
            font-size: 0.825rem;
            color: #64748b;
        }
        
        .condition-module-card .toggle-icon {
            transition: transform 0.3s;
        }
        
        .condition-module-card .toggle-icon.rotated {
            transform: rotate(180deg);
        }
        
        .module-cards {
            margin-top: 16px;
        }
        
        .info-message {
            display: flex;
            align-items: center;
            padding: 12px;
            background-color: #f8fafc;
            border: 1px dashed #cbd5e1;
            border-radius: 8px;
            color: #64748b;
            margin: 16px 0;
        }
        
        .info-message i {
            margin-right: 8px;
            color: #3b82f6;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .section-header h4 {
            font-size: 1rem;
            font-weight: 600;
            color: #334155;
        }
        
        .action-buttons {
            display: flex;
            gap: 8px;
        }

        /* 支付方式项样式 */
        .payment-method-item {
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-method-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .payment-method-item.selected {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }
        
        .drag-handle {
            cursor: move;
        }

        /* 自定义条件构建器样式 */
        .custom-condition-builder .form-control {
            @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300;
        }

        .condition-rule-item {
            @apply flex items-start space-x-2 p-3 border border-gray-200 rounded-md bg-gray-50;
        }

        .condition-rule-item select, 
        .condition-rule-item input {
            @apply px-2 py-1.5 border border-gray-300 rounded text-sm;
        }

        .btn-remove-rule {
            @apply text-gray-500 hover:text-red-500 p-1;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 0;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            width: 80%;
            max-width: 800px;
            position: relative;
            animation: modalFadeIn 0.3s;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 24px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .modal-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #334155;
            margin: 0;
        }
        
        .modal-body {
            padding: 24px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 16px 24px;
            border-top: 1px solid #e2e8f0;
        }
        
        .close {
            color: #64748b;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: #334155;
        }
        
        @keyframes modalFadeIn {
            from {opacity: 0; transform: translateY(-20px);}
            to {opacity: 1; transform: translateY(0);}
        }

        /* 卡片内容增强样式 */
        .card-body .section-header {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .card-body .section-header::after {
            content: '';
            position: absolute;
            bottom: -0.75rem;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
        }
        
        .card-body .info-message {
            backdrop-filter: blur(4px);
            border: 1px dashed rgba(203, 213, 225, 0.8);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
        }
        
        .card-body .action-buttons .btn {
            transition: all 0.25s ease;
            transform-origin: center;
        }
        
        .card-body .action-buttons .btn:hover {
            transform: translateY(-2px);
        }
        
        .card-body .action-buttons .btn:active {
            transform: translateY(1px);
        }

        /* 开关控件样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 36px;
            height: 20px;
        }
        
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
            border-radius: 34px;
        }
        
        .slider:before {
            position: absolute;
            content: "";
            height: 16px;
            width: 16px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .slider {
            background-color: var(--primary-color);
        }
        
        input:focus + .slider {
            box-shadow: 0 0 1px var(--primary-color);
        }
        
        input:checked + .slider:before {
            -webkit-transform: translateX(16px);
            -ms-transform: translateX(16px);
            transform: translateX(16px);
        }
        
        /* 折叠和默认选中样式 */
        /* 移除折叠功能对文案的控制
        .payment-method-item.folded .mt-2 {
            display: none;
        }
        */
        
        .payment-method-item.default-selected {
            border-left: 3px solid var(--primary-color);
            background-color: var(--primary-light);
        }
        
        .payment-method-item .default-radio + .slider {
            background-color: #ccc;
        }
        
        .payment-method-item .default-radio:checked + .slider {
            background-color: var(--primary-color);
        }
        
        .payment-method-item .fold-checkbox + .slider,
        .payment-method-item .default-radio + .slider {
            cursor: pointer;
        }

        /* 条件模块选择模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }
        
        .modal-content {
            background-color: #fefefe;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .modal-body {
            padding: 16px;
        }
        
        .modal-header, .modal-footer {
            padding: 16px;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }
        
        .modal-footer {
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 8px;
        }

        .condition-group .group-body {
            display: block; /* 默认展开 */
            transition: max-height 0.3s ease-out;
            overflow: hidden;
        }
        
        .condition-group .expand-icon {
            transition: transform 0.3s ease;
        }
        
        .condition-group .expand-icon.collapsed {
            transform: rotate(-90deg);
        }

        /* 卡片内容增强样式 */
        .card-body .section-header {
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .card-body .section-header::after {
            content: '';
            position: absolute;
            bottom: -0.75rem;
            left: 0;
            width: 50px;
            height: 3px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            border-radius: 3px;
        }
        
        .card-body .info-message {
            backdrop-filter: blur(4px);
            border: 1px dashed rgba(203, 213, 225, 0.8);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
        }
        
        .card-body .action-buttons .btn {
            transition: all 0.25s ease;
            transform-origin: center;
        }
        
        .card-body .action-buttons .btn:hover {
            transform: translateY(-2px);
        }
        
        .card-body .action-buttons .btn:active {
            transform: translateY(1px);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- 侧边栏导航 -->
        <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
            <nav class="mt-6 px-4">
                <ul>
                    <!-- 仪表盘 -->
                    <li class="nav-item">
                        <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                            <span class="ml-3">仪表盘</span>
                        </a>
                    </li>

                    <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>
                        </ul>
                    </li>

                    <!-- 支付场景管理 -->
                    <li class="nav-item active">
                        <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                            <div class="flex items-center">
                                <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                                <span class="ml-3">支付场景管理</span>
                            </div>
                            <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300" style="transform: rotate(180deg);"></i>
                        </div>
                        <ul class="submenu active pl-12 pr-4">
                            <li class="my-2">
                                <a href="payment_scene.html" class="block py-2 text-primary font-medium">
                                    场景列表
                                </a>
                            </li>
                            <li class="my-2">
                                <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                    版本历史
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- 条件筛选管理 -->
                    <li class="nav-item">
                        <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-filter w-5 h-5 text-center"></i>
                                <span class="ml-3">条件筛选管理</span>
                            </div>
                            <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                        </div>
                        <ul class="submenu pl-12 pr-4">
                            <li class="my-2">
                                <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                    条件模块
                                </a>
                            </li>
                            <li class="my-2">
                                <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                    条件规则
                                </a>
                            </li>

                        </ul>
                    </li>
                    
                    <!-- 支付方式管理 -->
                    <li class="nav-item">
                        <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                            <span class="ml-3">支付方式管理</span>
                        </a>
                    </li>

                    <!-- 渠道路由管理 -->
                    <li class="nav-item">
                        <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-random w-5 h-5 text-center"></i>
                                <span class="ml-3">渠道路由管理</span>
                            </div>
                            <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                        </div>
                        <ul class="submenu pl-12 pr-4">
                            <li class="my-2">
                                <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                    路由规则
                                </a>
                            </li>
                            <li class="my-2">
                                <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                    版本管理
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Feature Flags -->
                    <li class="nav-item">
                        <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                            <span class="ml-3">功能开关</span>
                        </a>
                    </li>

                    <!-- 商户号管理 -->
                    <li class="nav-item">
                        <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-store w-5 h-5 text-center"></i>
                            <span class="ml-3">商户号管理</span>
                        </a>
                    </li>

                    <!-- 应用管理 -->
                    <li class="nav-item">
                        <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                            <span class="ml-3">应用管理</span>
                        </a>
                    </li>

                    <!-- 操作日志 -->
                    <li class="nav-item">
                        <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-history w-5 h-5 text-center"></i>
                            <span class="ml-3">操作日志</span>
                        </a>
                    </li>

                    <!-- 系统设置 -->
                    <li class="nav-item">
                        <a href="system_settings.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                            <i class="fas fa-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">系统设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="content pt-20 pb-8 px-8">
            <div class="mb-6 flex justify-between items-center">
                <div class="flex items-center">
                    <a href="payment_scene.html" class="text-gray-500 hover:text-gray-700 mr-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h2 class="text-xl font-bold text-gray-800">编辑支付场景</h2>
                </div>
                <div class="flex space-x-3">
                    <button class="btn btn-light flex items-center">
                        <i class="fas fa-eye mr-2"></i>
                        <span>预览</span>
                    </button>
                    <button class="btn btn-success flex items-center">
                        <i class="fas fa-paper-plane mr-2"></i>
                        <span>发布</span>
                    </button>
                    <button class="btn btn-primary flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        <span>保存</span>
                    </button>
                </div>
            </div>

            <!-- 表单内容 -->
            <div class="card mb-6">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">基本信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneName">
                                场景名称 <span class="text-red-500">*</span>
                            </label>
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="sceneName" type="text" placeholder="请输入场景名称" value="电商购物-标准">
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneCode">
                                场景编码 <span class="text-red-500">*</span>
                            </label>
                            <input class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="sceneCode" type="text" placeholder="请输入场景编码" value="ECOM_STD">
                            <p class="text-xs text-gray-500 mt-1">使用大写字母和下划线，例如：ECOM_STD</p>
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneType">
                                场景类型 <span class="text-red-500">*</span>
                            </label>
                            <select class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="sceneType">
                                <option value="ECOMMERCE" selected>电商购物</option>
                                <option value="MEMBERSHIP">会员充值</option>
                                <option value="COURSE">课程购买</option>
                                <option value="OTHER">其他</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneStatus">
                                状态
                            </label>
                            <div class="flex items-center">
                                <input id="sceneStatusActive" type="radio" name="sceneStatus" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" checked>
                                <label for="sceneStatusActive" class="ml-2 block text-gray-700">
                                    启用
                                </label>
                                <input id="sceneStatusInactive" type="radio" name="sceneStatus" value="0" class="ml-6 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <label for="sceneStatusInactive" class="ml-2 block text-gray-700">
                                    禁用
                                </label>
                            </div>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="sceneDesc">
                                场景描述
                            </label>
                            <textarea class="appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" id="sceneDesc" rows="3" placeholder="请输入场景描述">标准电商购物场景，支持多种支付方式，适用于大部分电商应用。</textarea>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-filter"></i>场景条件配置</h3>
                        <div class="info-tooltip">
                            <i class="fas fa-info-circle"></i>
                            <span class="tooltip-text">配置支付场景的触发条件，支持条件组合</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="section-header">
                            <h4>条件模块</h4>
                            <div class="action-buttons">
                                <button type="button" id="selectConditionModuleBtn" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> 选择条件模块
                                </button>
                                <button type="button" id="previewConditionsBtn" class="btn btn-light">
                                    <i class="fas fa-eye"></i> 预览JSON
                                </button>
                            </div>
                        </div>
                        
                        <!-- 条件模块选择区域 -->
                        <div id="noConditionsMessage" class="info-message">
                            <i class="fas fa-info-circle"></i>
                            <span>请选择一个条件模块</span>
                        </div>
                        
                        <div id="selectedConditionModules" class="module-cards" style="display: none;">
                            <!-- 选择的条件模块会显示在这里 -->
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-800">支付方式配置</h3>
                        <button id="addPaymentMethodBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center text-sm">
                            <i class="fas fa-plus mr-1"></i>
                            <span>添加支付方式</span>
                        </button>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-md mb-4">
                        <div class="flex items-center text-gray-700">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>支付方式的展示顺序可通过拖拽调整，从上至下依次展示。</span>
                        </div>
                        <div class="mt-2 text-sm text-gray-600">
                            若需管理支付方式，请前往 <a href="payment_method_list.html" class="text-blue-600 hover:text-blue-800 underline">支付方式管理</a> 页面进行配置。
                        </div>
                    </div>
                    
                    <!-- 支付方式列表 -->
                    <div class="space-y-3">
                        <!-- 支付方式项 1 -->
                        <div class="payment-method-item rounded-md p-4 bg-white" data-id="1">
                            <div class="flex items-center">
                                <div class="drag-handle text-gray-400 cursor-move mr-3">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 flex items-center justify-center bg-blue-100 text-blue-600 rounded-md mr-3">
                                                <i class="fab fa-alipay text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">支付宝</h4>
                                                <p class="text-xs text-gray-500">ALIPAY</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">状态:</span>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">折叠:</span>
                                                <label class="switch">
                                                    <input type="checkbox" class="fold-checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">默认:</span>
                                                <label class="switch">
                                                    <input type="radio" name="defaultPayment" class="default-radio" style="opacity:0; position:absolute;">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-gray-600 hover:text-blue-600 edit-payment-btn">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-600 hover:text-red-600 delete-payment-btn">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-2 text-sm border-t border-gray-100 pt-2 px-2">
                                        <div class="text-xs text-gray-500">文案设置:</div>
                                        <div class="flex items-center mt-1">
                                            <div class="text-xs text-gray-600 w-16">标题:</div>
                                            <div class="text-sm text-gray-700 font-medium">支付宝 - 全球领先的支付工具</div>
                                        </div>
                                        <div class="flex items-start mt-1">
                                            <div class="text-xs text-gray-600 w-16">描述:</div>
                                            <div class="text-xs text-gray-600 flex-1">收付款便捷，自由支配，专业安全。</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 支付方式项 2 (微信支付) -->
                        <div class="payment-method-item rounded-md p-4 bg-white default-selected" data-id="2">
                            <div class="flex items-center">
                                <div class="drag-handle text-gray-400 cursor-move mr-3">
                                    <i class="fas fa-grip-vertical"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex justify-between">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 flex items-center justify-center bg-green-100 text-green-600 rounded-md mr-3">
                                                <i class="fab fa-weixin text-xl"></i>
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-800">微信支付</h4>
                                                <p class="text-xs text-gray-500">WECHAT_PAY</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">状态:</span>
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                            <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">折叠:</span>
                                                <label class="switch">
                                                    <input type="checkbox" class="fold-checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                </div>
                                        <div class="flex items-center">
                                                <span class="text-sm text-gray-600 mr-2">默认:</span>
                                                <label class="switch">
                                                    <input type="radio" name="defaultPayment" class="default-radio" style="opacity:0; position:absolute;" checked>
                                                    <span class="slider" style="background-color: var(--primary-color);"></span>
                                                </label>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button class="text-gray-600 hover:text-blue-600 edit-payment-btn">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="text-gray-600 hover:text-red-600 delete-payment-btn">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                    </div>
                </div>
            </div>

                                    <div class="mt-2 text-sm border-t border-gray-100 pt-2 px-2">
                                        <div class="text-xs text-gray-500">文案设置:</div>
                                        <div class="flex items-center mt-1">
                                            <div class="text-xs text-gray-600 w-16">标题:</div>
                                            <div class="text-sm text-gray-700 font-medium">微信支付 - 更快更安全</div>
            </div>
                                        <div class="flex items-start mt-1">
                                            <div class="text-xs text-gray-600 w-16">描述:</div>
                                            <div class="text-xs text-gray-600 flex-1">绑定银行卡或零钱，使用微信扫码或账号支付，稳定流畅。</div>
    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
            </div>
        </main>
    </div>

    <script>
        // 添加条件模块测试用例数据 - 全局定义
        const conditionModuleTestCases = {
            "1": [  // 用户属性条件测试用例
                { id: "u1", name: "VIP等级大于等于3级", operator: ">=", value: "3", type: "vip_level", status: true },
                { id: "u2", name: "注册时间超过30天", operator: ">", value: "30", type: "register_days", status: true },
                { id: "u3", name: "用户年龄在18-40岁之间", operator: "between", value: "18,40", type: "age", status: true }
            ],
            "2": [  // 订单属性条件测试用例
                { id: "o1", name: "订单金额大于500元", operator: ">", value: "500", type: "order_amount", status: true },
                { id: "o2", name: "订单商品数量大于等于3件", operator: ">=", value: "3", type: "product_count", status: true },
                { id: "o3", name: "包含数码类商品", operator: "contains", value: "数码", type: "product_category", status: true }
            ],
            "3": [  // 环境条件测试用例
                { id: "e1", name: "工作日（周一至周五）", operator: "in", value: "1,2,3,4,5", type: "weekday", status: true },
                { id: "e2", name: "时间在9:00-21:00之间", operator: "time_between", value: "09:00,21:00", type: "time", status: true },
                { id: "e3", name: "节假日专享", operator: "is", value: "true", type: "is_holiday", status: true },
                { id: "e4", name: "移动设备访问", operator: "in", value: "mobile,tablet", type: "device_type", status: true }
            ],
            "4": [  // 支付相关条件测试用例
                { id: "p1", name: "历史支付成功次数大于3次", operator: ">", value: "3", type: "payment_success_count", status: true },
                { id: "p2", name: "最近支付方式为微信支付", operator: "=", value: "WECHAT", type: "last_payment_method", status: true },
                { id: "p3", name: "30天内有退款记录", operator: "contains", value: "REFUND", type: "payment_history", status: true },
                { id: "p4", name: "支持借记卡支付", operator: "is", value: "true", type: "support_debit_card", status: true }
            ]
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 删除局部变量定义，因为已经在全局定义了
            
            // 用户下拉菜单
            document.getElementById('userDropdown').addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('userMenu').classList.toggle('hidden');
            });
            
            // 点击页面其他位置关闭下拉菜单
            document.addEventListener('click', function(e) {
                const userMenu = document.getElementById('userMenu');
                const userDropdown = document.getElementById('userDropdown');
                
                if (!userDropdown.contains(e.target) && !userMenu.contains(e.target)) {
                    userMenu.classList.add('hidden');
                }
            });

            // 侧边栏菜单展开/折叠
            document.querySelectorAll('.menu-toggle').forEach(function(menuToggle) {
                menuToggle.addEventListener('click', function() {
                    // 获取子菜单和图标
                    const submenu = this.nextElementSibling;
                    const icon = this.querySelector('.submenu-icon');
                    
                    // 切换子菜单的展开状态
                    submenu.classList.toggle('active');
                    
                    // 切换图标旋转
                    if (submenu.classList.contains('active')) {
                        icon.style.transform = 'rotate(180deg)';
                    } else {
                        icon.style.transform = 'rotate(0)';
                    }
                });
            });

            // ===== JSON预览相关 =====
            // 绑定预览JSON事件
            document.getElementById('previewConditionsBtn').addEventListener('click', function() {
                const jsonContent = generateConditionsJson();
                document.getElementById('jsonPreview').textContent = jsonContent;
                openModal('jsonPreviewModal');
            });
        
            // 绑定复制到剪贴板事件
            document.getElementById('copyJsonBtn').addEventListener('click', function() {
                const jsonContent = document.getElementById('jsonPreview').textContent;
            
                // 使用Clipboard API复制
                navigator.clipboard.writeText(jsonContent).then(function() {
                    alert('已复制到剪贴板');
                }, function() {
                    // 如果API失败，使用传统方法
                    const textarea = document.createElement('textarea');
                    textarea.value = jsonContent;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    alert('已复制到剪贴板');
                });
            });

            // 处理条件模块的选择
            document.getElementById('selectConditionModuleBtn').addEventListener('click', function() {
                openModal('selectConditionModuleModal');
            });

            // 添加选定的条件模块
            document.getElementById('confirmSelectModule').addEventListener('click', function() {
                selectConditionModule();
            });
            
            // 初始化时默认显示空状态提示
            document.getElementById('noConditionsMessage').style.display = 'flex';
            
            // ===== 支付方式管理相关 =====
            // 处理添加支付方式按钮点击
            document.getElementById('addPaymentMethodBtn').addEventListener('click', function() {
                // 打开模态框
                openModal('addPaymentMethodModal');
            });
            
            // 添加支付方式搜索功能
            document.getElementById('searchPaymentMethod').addEventListener('keyup', function() {
                filterPaymentMethods(this.value);
            });
            
            // 添加支付方式选项点击事件
            document.querySelectorAll('.payment-method-option').forEach(function(option) {
                option.addEventListener('click', function() {
                    // 移除其他选项的选中状态
                    document.querySelectorAll('.payment-method-option').forEach(opt => {
                        opt.classList.remove('bg-blue-50');
                    });
                    
                    // 添加当前选项的选中状态
                    this.classList.add('bg-blue-50');
                });
            });
            
            // 添加支付方式确认按钮
            document.getElementById('confirmAddPaymentMethod').addEventListener('click', function() {
                // 获取选中的支付方式
                const selectedOption = document.querySelector('.payment-method-option.bg-blue-50');
                
                if (!selectedOption) {
                    alert('请选择一个支付方式');
                    return;
                }
                
                // 获取支付方式信息
                const id = selectedOption.getAttribute('data-id');
                const name = selectedOption.getAttribute('data-name');
                const code = selectedOption.getAttribute('data-code');
                const status = selectedOption.getAttribute('data-status') === 'true';
                const icon = selectedOption.getAttribute('data-icon');
                const iconColor = selectedOption.getAttribute('data-icon-color');
                const title = selectedOption.getAttribute('data-title');
                const description = selectedOption.getAttribute('data-description');
                
                // 添加到支付方式列表
                addPaymentMethod(id, name, code, icon, iconColor, status, title, description);
                
                // 关闭模态框
                closeModal('addPaymentMethodModal');
            });
            
            // 初始化编辑和删除按钮事件
            initPaymentMethodButtons();
            
            // 添加确认删除按钮事件监听
            document.getElementById('confirmDeletePaymentMethod').addEventListener('click', function() {
                const methodId = document.getElementById('deletePaymentMethodId').value;
                if (methodId) {
                    // 删除对应的支付方式项
                    const item = document.querySelector(`.payment-method-item[data-id="${methodId}"]`);
                    if (item) {
                        item.remove();
                    }
                    
                    // 关闭模态框
                    closeModal('deleteConfirmModal');
                }
            });
            
            // 添加编辑确认按钮事件监听
            document.getElementById('confirmEditPaymentMethod').addEventListener('click', function() {
                // 获取输入值
                const id = document.getElementById('editPaymentMethodId').value;
                const name = document.getElementById('editPaymentMethodName').value.trim();
                const code = document.getElementById('editPaymentMethodCode').value.trim();
                const status = document.getElementById('editPaymentMethodStatus').checked;
                const title = document.getElementById('editPaymentMethodTitle').value.trim();
                const description = document.getElementById('editPaymentMethodDescription').value.trim();
                const selectedIcon = document.querySelector('#editPaymentMethodModal .icon-option.selected');
                
                // 验证
                if (!name) {
                    alert('请输入支付方式名称');
                    return;
                }
                
                if (!code) {
                    alert('请输入支付方式代码');
                    return;
                }
                
                if (!selectedIcon) {
                    alert('请选择图标类型');
                    return;
                }
                
                // 获取图标信息
                const iconType = selectedIcon.getAttribute('data-icon');
                const iconColor = selectedIcon.getAttribute('data-color');
                
                // 更新支付方式
                updatePaymentMethod(id, name, code, iconType, iconColor, status, title, description);
                
                // 关闭模态框
                closeModal('editPaymentMethodModal');
            });
            
            // 初始化折叠和默认选中功能
            initFoldAndDefaultFeatures();
            
            // 在图标选项上添加点击事件
                    document.querySelectorAll('#editPaymentMethodModal .icon-option').forEach(function(option) {
                option.addEventListener('click', function() {
                    document.querySelectorAll('#editPaymentMethodModal .icon-option').forEach(function(opt) {
                        opt.classList.remove('selected', 'border-blue-500', 'border-2');
                    });
                    this.classList.add('selected', 'border-blue-500', 'border-2');
            });
        });

            // 初始化页面时确保微信支付默认选中状态生效
            updateDefaultRadioDisplay(document.querySelector('.payment-method-item[data-id="2"] .default-radio'));
            
            // 初始化拖拽排序
            initDragSort();
            
            // 预览按钮事件
            document.querySelector('.btn-light:not(.close)').addEventListener('click', function() {
                updatePreviewDisplay();
            });
            
            // 保存按钮事件
            document.querySelector('.btn-primary:not(#confirmEditPaymentMethod):not(#confirmSelectModule):not(#copyJsonBtn)').addEventListener('click', function() {
                alert('已保存场景配置');
            });
            
            // 发布按钮事件
            document.querySelector('.btn-success').addEventListener('click', function() {
                alert('已发布最新版本');
            });

            // 初始化时默认选择第一个条件模块
            const firstModuleOption = document.querySelector('#conditionModulesList .module-option');
            if (firstModuleOption) {
                firstModuleOption.classList.add('selected', 'bg-blue-50');
                const radio = firstModuleOption.querySelector('.module-radio');
                if (radio) {
                    radio.checked = true;
                }
            }
        });

        // ===== 缺失的功能实现 =====
        
        // 生成条件配置的JSON
        function generateConditionsJson() {
            const modules = document.querySelectorAll('#selectedConditionModules .condition-module-card');
            if (modules.length === 0) {
                return JSON.stringify({
                    condition_modules: []
                }, null, 2);
            }
            
            const conditionModules = [];
            modules.forEach(module => {
                const moduleId = module.getAttribute('data-id');
                const moduleName = module.querySelector('.card-title').textContent.trim();
                const moduleType = module.getAttribute('data-type') || 'standard';
                const isEnabled = module.getAttribute('data-enabled') === 'true';
                
                // 获取规则
                const rules = [];
                const ruleItems = module.querySelectorAll('.rule-item');
                ruleItems.forEach(item => {
                    const ruleId = item.getAttribute('data-id');
                    const ruleName = item.querySelector('h5').textContent;
                    const ruleStatus = item.querySelector('.fa-check-circle') !== null;
                    
                    // 获取规则详情
                    const typeTag = item.querySelector('.bg-blue-100');
                    const operatorTag = item.querySelector('.bg-purple-100');
                    
                    let type = '';
                    let operator = '';
                    let value = '';
                    
                    if (typeTag) {
                        type = typeTag.textContent.trim().replace(/\s+/g, '_');
                    }
                    
                    if (operatorTag) {
                        const operatorText = operatorTag.textContent.trim();
                        const parts = operatorText.split(' ');
                        operator = parts[0];
                        value = parts.slice(1).join(' ');
                    }
                    
                    rules.push({
                        id: ruleId,
                        name: ruleName,
                        type: type,
                        operator: operator,
                        value: value,
                        enabled: ruleStatus
                    });
                });
                
                conditionModules.push({
                    id: moduleId,
                    name: moduleName,
                    type: moduleType,
                    enabled: isEnabled,
                    rules: rules
                });
            });
            
            return JSON.stringify({
                condition_modules: conditionModules
            }, null, 2);
        }
        
        // 条件模块选择
        function selectConditionModule() {
            // 使用radio:checked来查找选中的模块，这样更可靠
            const selectedRadio = document.querySelector('#conditionModulesList .module-radio:checked');
            if (!selectedRadio) {
                alert('请选择一个条件模块');
                return;
            }
            
            const selectedModule = selectedRadio.closest('.module-option');
            if (!selectedModule) {
                alert('请选择一个条件模块');
                return;
            }
            
            // 清除空提示
            document.getElementById('noConditionsMessage').style.display = 'none';
            document.getElementById('selectedConditionModules').style.display = 'block';
            
            // 添加选中的模块
            const moduleId = selectedModule.getAttribute('data-id');
            const moduleName = selectedModule.querySelector('.module-name').textContent;
            const moduleDesc = selectedModule.querySelector('.module-desc').textContent;
            
            // 检查是否已添加
            if (document.querySelector(`.condition-module-card[data-id="${moduleId}"]`)) {
                alert('该条件模块已添加');
                return; // 已存在则跳过
            }
            
            // 创建新模块卡片
            addConditionModuleCard(moduleId, moduleName, moduleDesc);
            
            // 关闭模态框
            closeModal('selectConditionModuleModal');
        }
        
        // 生成规则项的HTML
        function generateRuleHTML(rule) {
            const statusClass = rule.status ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700';
            const statusIcon = rule.status ? 'fa-check-circle' : 'fa-ban';

            return `
                <div class="rule-item p-3 mb-2 border border-gray-200 rounded-md bg-white hover:bg-gray-50" data-id="${rule.id}">
                    <div class="flex justify-between">
                        <div class="flex items-center">
                            <div class="w-6 h-6 flex items-center justify-center text-gray-600 mr-2">
                                <i class="fas fa-filter"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-800">${rule.name}</h5>
                                <div class="flex flex-wrap gap-2 mt-1">
                                    <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">
                                        ${rule.type.replace(/_/g, ' ')}
                                    </span>
                                    <span class="px-2 py-0.5 bg-purple-100 text-purple-700 text-xs rounded">
                                        ${rule.operator} ${rule.value}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span class="px-2 py-0.5 ${statusClass} text-xs rounded flex items-center">
                                <i class="fas ${statusIcon} mr-1"></i>${rule.status ? '启用' : '禁用'}
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 添加条件模块卡片
        function addConditionModuleCard(id, name, description) {
            const moduleCard = document.createElement('div');
            moduleCard.className = 'condition-module-card';
            moduleCard.setAttribute('data-id', id);
            moduleCard.setAttribute('data-enabled', 'true');
            
            // 获取测试用例规则
            const rules = conditionModuleTestCases[id] || [];
            const rulesCount = rules.length;
            
            // 对规则按类型分组
            const ruleGroups = groupRulesByType(rules);
            const groupCount = Object.keys(ruleGroups).length;
            
            // 生成分组HTML
            let groupsHTML = '';
            let groupIndex = 1;
            
            for (const [type, groupRules] of Object.entries(ruleGroups)) {
                let rulesHTML = '';
                groupRules.forEach(rule => {
                    rulesHTML += generateRuleHTML(rule);
                });
                
                // 根据类型确定标签样式
                let typeLabel = type.replace(/_/g, ' ');
                let typeClassName = 'gray'; // Default color
                if (type.includes('user') || type.includes('vip')) {
                    typeClassName = 'blue';
                    typeLabel = typeLabel || '用户属性';
                } else if (type.includes('order') || type.includes('product')) {
                    typeClassName = 'green';
                    typeLabel = typeLabel || '订单属性';
                } else if (type.includes('payment')) {
                    typeClassName = 'yellow';
                    typeLabel = typeLabel || '支付相关';
                } else if (type.includes('env') || type.includes('time') || type.includes('device') || type.includes('holiday') || type.includes('weekday')) {
                    typeClassName = 'purple';
                    typeLabel = typeLabel || '环境条件';
                }
                typeLabel = typeLabel.charAt(0).toUpperCase() + typeLabel.slice(1); // 首字母大写
                
                groupsHTML += `
                    <div class="condition-group mb-4 border border-gray-200 rounded-lg overflow-hidden" data-group-id="group-${id}-${groupIndex}">
                        <div class="group-header flex justify-between items-center p-3 bg-gray-50 border-b border-gray-200 cursor-pointer" onclick="toggleGroupBody(this)">
                            <div class="flex items-center">
                                <i class="fas fa-chevron-down expand-icon mr-2 text-gray-500 text-xs"></i>
                                <span class="bg-${typeClassName}-100 text-${typeClassName}-800 px-2 py-0.5 rounded-full text-xs font-medium mr-2">组 ${groupIndex}</span>
                                <span class="text-sm font-medium text-gray-700">${typeLabel} 条件组</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">${groupRules.length} 条规则</span>
                            </div>
                        </div>
                        
                        <div class="group-body p-3 bg-white">
                            <div class="mb-3">
                                <h5 class="text-xs font-semibold text-gray-500 mb-1">组内条件关系</h5>
                                <div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-check-circle mr-1"></i>满足所有条件 (AND)
                                </div>
                            </div>
                            
                            <!-- 条件列表 -->
                            <div class="conditions-list space-y-2">
                                ${rulesHTML}
                            </div>
                        </div>
                    </div>
                `;
                
                groupIndex++;
            }
            
            moduleCard.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-project-diagram mr-2 text-primary-color"></i>
                        ${name}
                    </div>
                    <div class="card-actions">
                        <span class="rule-count">
                            <i class="fas fa-sitemap mr-1 text-gray-500"></i>
                            <span class="count">${groupCount}</span> 个组 / 
                            <i class="fas fa-list-ul mr-1 text-gray-500"></i>
                            <span class="count">${rulesCount}</span> 条规则
                        </span>
                        <button type="button" class="text-gray-600 hover:text-blue-600 toggle-module-btn p-1 rounded hover:bg-gray-100">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <button type="button" class="text-gray-600 hover:text-red-600 remove-module-btn p-1 rounded hover:bg-gray-100">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body expanded">
                    <p class="text-sm text-gray-600 mb-4 pb-3 border-b border-gray-200">${description}</p>
                    
                    <!-- 条件组间关系 -->
                    ${groupCount > 1 ? `
                    <div class="mb-4 pb-3">
                        <h4 class="text-sm font-semibold text-gray-600 mb-2">条件组关系</h4>
                        <div class="p-2 bg-gray-50 rounded-md inline-block">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-double mr-1"></i>满足所有条件组 (AND)
                            </span>
                        </div>
                    </div>
                    ` : ''}
                    
                    <!-- 条件组列表 -->
                    <div class="condition-groups">
                        ${groupsHTML}
                        ${rulesCount === 0 ? `
                        <div class="info-message p-4 border-dashed border-gray-300 rounded-md">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>此模块当前没有定义具体规则。请前往 <a href="condition_module.html" class="text-blue-600 hover:underline">条件模块管理</a> 配置规则。</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
            
            // 添加事件监听
            moduleCard.querySelector('.toggle-module-btn').addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                const body = moduleCard.querySelector('.card-body');
                body.classList.toggle('expanded');
                
                // 切换图标
                const icon = this.querySelector('i');
                if (body.classList.contains('expanded')) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                    body.style.height = 'auto'; // 展开时设置高度为 auto
                    let actualHeight = body.scrollHeight + "px"; // 获取实际高度
                    body.style.height = '0'; // 先设为0触发动画
                    requestAnimationFrame(() => { // 延迟设置实际高度
                        body.style.height = actualHeight;
                    });
                } else {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                    body.style.height = body.scrollHeight + "px"; // 设置当前高度
                    requestAnimationFrame(() => { // 延迟设为0触发动画
                       body.style.height = '0';
                    });
                }
            });
            
            // 默认展开卡片（修改逻辑）
             setTimeout(() => {
                 const body = moduleCard.querySelector('.card-body');
                 const toggleBtn = moduleCard.querySelector('.toggle-module-btn');
                 const icon = toggleBtn.querySelector('i');
                 if (body.classList.contains('expanded')) {
                     body.style.height = 'auto'; // 设置为auto以适应内容
                     icon.classList.remove('fa-chevron-down');
                     icon.classList.add('fa-chevron-up');
                 } else {
                     body.style.height = '0';
                     icon.classList.remove('fa-chevron-up');
                     icon.classList.add('fa-chevron-down');
                 }
             }, 10); // 稍微延迟以确保元素渲染
            
            moduleCard.querySelector('.remove-module-btn').addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                moduleCard.remove();
                
                // 如果没有模块，显示空提示
                if (document.querySelectorAll('#selectedConditionModules .condition-module-card').length === 0) {
                    document.getElementById('noConditionsMessage').style.display = 'flex';
                    document.getElementById('selectedConditionModules').style.display = 'none';
                }
            });
            
            // 添加卡片头部点击展开/折叠功能 (保持不变)
            moduleCard.querySelector('.card-header').addEventListener('click', function() {
                const toggleBtn = moduleCard.querySelector('.toggle-module-btn');
                toggleBtn.click(); // 触发折叠按钮的点击事件
            });
            
            // 添加到容器
            document.getElementById('selectedConditionModules').appendChild(moduleCard);
        }

        // 条件组折叠/展开功能
        function toggleGroupBody(header) {
            const groupBody = header.nextElementSibling;
            const expandIcon = header.querySelector('.expand-icon');
            
            if (groupBody.style.maxHeight && groupBody.style.maxHeight !== '0px') {
                 // 折叠
                 groupBody.style.maxHeight = '0px';
                 expandIcon.classList.add('collapsed');
            } else {
                 // 展开
                 groupBody.style.maxHeight = groupBody.scrollHeight + "px";
                 expandIcon.classList.remove('collapsed');
                 // 添加一个监听器，以便在过渡结束后移除max-height，允许内容动态变化
                 groupBody.addEventListener('transitionend', function handler() {
                     groupBody.removeEventListener('transitionend', handler);
                     if (!expandIcon.classList.contains('collapsed')) { // 确保是展开状态
                         groupBody.style.maxHeight = null; // 或者 'none'
                     }
                 });
            }
        }
        
        // 模态框操作
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
            }
        }
        
        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            }
        }
        
        // 初始化关闭按钮
        document.querySelectorAll('.close').forEach(function(closeBtn) {
            closeBtn.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                }
                });
            });
        
        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        });
        
        // 筛选支付方式
        function filterPaymentMethods(searchText) {
            const options = document.querySelectorAll('.payment-method-option');
            const searchLower = searchText.toLowerCase();
            
            options.forEach(option => {
                const name = option.getAttribute('data-name').toLowerCase();
                const code = option.getAttribute('data-code').toLowerCase();
                
                if (name.includes(searchLower) || code.includes(searchLower)) {
                    option.style.display = 'flex';
                } else {
                    option.style.display = 'none';
                }
            });
        }
        
        // 添加支付方式到列表
        function addPaymentMethod(id, name, code, icon, iconColor, status, title, description) {
            // 检查是否已添加
            if (document.querySelector(`.payment-method-item[data-id="${id}"]`)) {
                alert('该支付方式已添加');
                return;
            }
            
            // 获取关联产品信息
            const selectedOption = document.querySelector(`.payment-method-option[data-id="${id}"]`);
            let productTags = '';
            
            if (selectedOption) {
                const productElements = selectedOption.querySelectorAll('.flex-wrap.gap-1 .px-2');
                productElements.forEach(product => {
                    productTags += `<span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded mr-1">${product.textContent}</span>`;
                });
            }
            
            const methodItem = document.createElement('div');
            methodItem.className = 'payment-method-item rounded-md p-4 bg-white';
            methodItem.setAttribute('data-id', id);
            
            methodItem.innerHTML = `
                <div class="flex items-center">
                    <div class="drag-handle text-gray-400 cursor-move mr-3">
                        <i class="fas fa-grip-vertical"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 flex items-center justify-center bg-${iconColor}-100 text-${iconColor}-600 rounded-md mr-3">
                                    <i class="${icon} text-xl"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-800">${name}</h4>
                                    <p class="text-xs text-gray-500">${code}</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600 mr-2">状态:</span>
                                    <label class="switch">
                                        <input type="checkbox" ${status ? 'checked' : ''}>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600 mr-2">折叠:</span>
                                    <label class="switch">
                                        <input type="checkbox" class="fold-checkbox">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-600 mr-2">默认:</span>
                                    <label class="switch">
                                        <input type="radio" name="defaultPayment" class="default-radio" style="opacity:0; position:absolute;">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="text-gray-600 hover:text-blue-600 edit-payment-btn">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-gray-600 hover:text-red-600 delete-payment-btn">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-2 text-sm border-t border-gray-100 pt-2 px-2">
                            <div class="text-xs text-gray-500 mb-1">关联支付产品</div>
                            <div class="flex flex-wrap mb-2">
                                ${productTags}
                            </div>
                            <div class="text-xs text-gray-500">文案设置:</div>
                            <div class="flex items-center mt-1">
                                <div class="text-xs text-gray-600 w-16">标题:</div>
                                <div class="text-sm text-gray-700 font-medium">${title}</div>
                            </div>
                            <div class="flex items-start mt-1">
                                <div class="text-xs text-gray-600 w-16">描述:</div>
                                <div class="text-xs text-gray-600 flex-1">${description}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 添加事件监听
            methodItem.querySelector('.edit-payment-btn').addEventListener('click', function() {
                showEditPaymentModal(id, name, code, icon, iconColor, status, title, description);
            });
            
            methodItem.querySelector('.delete-payment-btn').addEventListener('click', function() {
                showDeleteConfirmModal(id, name);
            });
            
            // 添加折叠和默认选中功能
            const foldCheckbox = methodItem.querySelector('.fold-checkbox');
            foldCheckbox.addEventListener('change', function() {
                handleFoldCheckbox(this);
            });
            
            const defaultRadio = methodItem.querySelector('.default-radio');
            defaultRadio.addEventListener('change', function() {
                if (this.checked) {
                    markAsDefaultPayment(this);
                }
            });
            
            // 添加到支付方式列表
            const container = document.querySelector('.space-y-3');
            container.appendChild(methodItem);
        }
        
        // 初始化支付方式按钮
        function initPaymentMethodButtons() {
            // 编辑按钮
            document.querySelectorAll('.edit-payment-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const item = this.closest('.payment-method-item');
                    const id = item.getAttribute('data-id');
                    const name = item.querySelector('h4').textContent;
                    const code = item.querySelector('p.text-xs').textContent;
                    const status = item.querySelector('input[type="checkbox"]').checked;
                    const iconEl = item.querySelector('.w-8.h-8 i');
                    const icon = iconEl.className.split(' ')[0] + ' ' + iconEl.className.split(' ')[1];
                    
                    // 从背景色获取颜色名
                    const bgColorClass = item.querySelector('.w-8.h-8').className;
                    const iconColor = bgColorClass.match(/bg-(\w+)-100/)[1];
                    
                    const title = item.querySelector('.text-sm.text-gray-700.font-medium').textContent;
                    const description = item.querySelector('.text-xs.text-gray-600.flex-1').textContent;
                    
                    showEditPaymentModal(id, name, code, icon, iconColor, status, title, description);
                });
            });
            
            // 删除按钮
            document.querySelectorAll('.delete-payment-btn').forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const item = this.closest('.payment-method-item');
                    const id = item.getAttribute('data-id');
                    const name = item.querySelector('h4').textContent;
                    
                    showDeleteConfirmModal(id, name);
                });
            });
        }
        
        // 显示编辑支付方式模态框
        function showEditPaymentModal(id, name, code, icon, iconColor, status, title, description) {
            // 填充表单
            document.getElementById('editPaymentMethodId').value = id;
            document.getElementById('editPaymentMethodName').value = name;
            document.getElementById('editPaymentMethodCode').value = code;
            document.getElementById('editPaymentMethodStatus').checked = status;
            document.getElementById('editPaymentMethodTitle').value = title;
            document.getElementById('editPaymentMethodDescription').value = description;
            
            // 选中对应图标
            document.querySelectorAll('#editPaymentMethodModal .icon-option').forEach(function(option) {
                option.classList.remove('selected', 'border-blue-500', 'border-2');
                
                if (option.getAttribute('data-icon') === icon && option.getAttribute('data-color') === iconColor) {
                    option.classList.add('selected', 'border-blue-500', 'border-2');
                }
            });
            
            // 清空已选产品
            document.getElementById('selectedProducts').innerHTML = '';
            
            // 取消所有产品复选框的选中状态
            document.querySelectorAll('.product-checkbox').forEach(function(checkbox) {
                checkbox.checked = false;
            });
            
            // 获取当前支付方式项的产品标签
            const methodItem = document.querySelector(`.payment-method-item[data-id="${id}"]`);
            if (methodItem) {
                const productTags = methodItem.querySelectorAll('.flex-wrap.mb-2 .px-2');
                productTags.forEach(function(tag) {
                    const productName = tag.textContent.trim();
                    
                    // 选中对应的复选框
                    const checkbox = document.querySelector(`.product-checkbox[value="${productName}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                    
                    // 添加产品标签
                    addProductTag(productName);
                });
            }
            
            // 打开模态框
            openModal('editPaymentMethodModal');
        }
        
        // 显示删除确认模态框
        function showDeleteConfirmModal(id, name) {
            document.getElementById('deletePaymentMethodId').value = id;
            document.getElementById('deletePaymentMethodName').textContent = name;
            
            openModal('deleteConfirmModal');
        }
        
        // 更新支付方式
        function updatePaymentMethod(id, name, code, iconType, iconColor, status, title, description) {
            const item = document.querySelector(`.payment-method-item[data-id="${id}"]`);
            if (!item) return;
            
            // 更新名称和代码
            item.querySelector('h4').textContent = name;
            item.querySelector('p.text-xs').textContent = code;
            
            // 更新图标
            const iconContainer = item.querySelector('.w-8.h-8');
            iconContainer.className = `w-8 h-8 flex items-center justify-center bg-${iconColor}-100 text-${iconColor}-600 rounded-md mr-3`;
            
            const iconElement = iconContainer.querySelector('i');
            iconElement.className = `${iconType} text-xl`;
            
            // 更新状态
            item.querySelector('input[type="checkbox"]').checked = status;
            
            // 更新产品标签
            const productsContainer = item.querySelector('.flex-wrap.mb-2');
            productsContainer.innerHTML = '';
            
            const selectedTags = document.querySelectorAll('#selectedProducts span');
            selectedTags.forEach(function(tag) {
                const productName = tag.getAttribute('data-value');
                productsContainer.innerHTML += `<span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded mr-1">${productName}</span>`;
            });
            
            // 更新文案
            item.querySelector('.text-sm.text-gray-700.font-medium').textContent = title;
            item.querySelector('.text-xs.text-gray-600.flex-1').textContent = description;
        }
        
        // 初始化折叠和默认选中功能
        function initFoldAndDefaultFeatures() {
            // 折叠功能
            document.querySelectorAll('.fold-checkbox').forEach(function(checkbox) {
                checkbox.addEventListener('change', function() {
                    handleFoldCheckbox(this);
                });
            });
            
            // 默认选中功能
            document.querySelectorAll('.default-radio').forEach(function(radio) {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        markAsDefaultPayment(this);
                    }
                });
            });
        }
        
        // 处理折叠复选框
        function handleFoldCheckbox(checkbox) {
            const item = checkbox.closest('.payment-method-item');
            
            if (checkbox.checked) {
                item.classList.add('folded');
            } else {
                item.classList.remove('folded');
            }
        }
        
        // 标记为默认支付方式
        function markAsDefaultPayment(radio) {
            // 移除所有项的默认选中样式
            document.querySelectorAll('.payment-method-item').forEach(function(item) {
                item.classList.remove('default-selected');
            });
            
            // 添加当前项的默认选中样式
            const item = radio.closest('.payment-method-item');
            item.classList.add('default-selected');
            
            // 更新所有单选按钮的显示
            document.querySelectorAll('.default-radio').forEach(function(r) {
                updateDefaultRadioDisplay(r);
            });
        }
        
        // 更新默认单选按钮的显示
        function updateDefaultRadioDisplay(radio) {
            const slider = radio.nextElementSibling;
            
            if (radio.checked) {
                slider.style.backgroundColor = 'var(--primary-color)';
            } else {
                slider.style.backgroundColor = '#ccc';
            }
        }

        // 添加搜索条件模块功能
        document.getElementById('searchConditionModule').addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const options = document.querySelectorAll('#conditionModulesList .module-option');
            
            options.forEach(option => {
                const moduleName = option.querySelector('.module-name').textContent.toLowerCase();
                const moduleDesc = option.querySelector('.module-desc').textContent.toLowerCase();
                
                if (moduleName.includes(searchText) || moduleDesc.includes(searchText)) {
                    option.style.display = 'block';
                } else {
                    option.style.display = 'none';
                }
            });
        });

        // 点击条件模块选项切换选中状态
        document.querySelectorAll('#conditionModulesList .module-option').forEach(function(option) {
            // 为单选按钮添加点击事件
            const radio = option.querySelector('.module-radio');
            radio.addEventListener('click', function(event) {
                // 阻止事件冒泡，避免触发父元素的点击事件
                event.stopPropagation();
                
                // 清除所有选项的选中状态
                document.querySelectorAll('#conditionModulesList .module-option').forEach(opt => {
                    opt.classList.remove('selected', 'bg-blue-50');
                    opt.querySelector('.module-radio').checked = false;
                });
                
                // 设置当前选项为选中状态
                option.classList.add('selected', 'bg-blue-50');
                this.checked = true;
            });
            
            // 为整个选项卡片添加点击事件
            option.addEventListener('click', function() {
                // 获取当前选项的单选按钮
                const radio = this.querySelector('.module-radio');
                
                // 清除所有选项的选中状态
                document.querySelectorAll('#conditionModulesList .module-option').forEach(opt => {
                    opt.classList.remove('selected', 'bg-blue-50');
                    opt.querySelector('.module-radio').checked = false;
                });
                
                // 设置当前选项为选中状态
                this.classList.add('selected', 'bg-blue-50');
                radio.checked = true;
            });
        });

        // 初始化拖拽排序功能
        function initDragSort() {
            const container = document.querySelector('.space-y-3');
            const items = document.querySelectorAll('.payment-method-item');
            
            let draggedItem = null;
            
            items.forEach(item => {
                const handle = item.querySelector('.drag-handle');
                
                // 开始拖拽
                handle.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                    draggedItem = item;
                    
                    // 添加拖拽中的样式
                    draggedItem.classList.add('bg-blue-50', 'shadow-lg');
                    
                    // 记录初始位置
                    const startY = e.clientY;
                    const startTop = item.offsetTop;
                    
                    // 鼠标移动事件
                    function onMouseMove(e) {
                        const y = e.clientY - startY;
                        const newTop = startTop + y;
                        
                        // 设置拖拽项的位置
                        draggedItem.style.position = 'relative';
                        draggedItem.style.top = y + 'px';
                        draggedItem.style.zIndex = 10;
                        
                        // 检查与其他项的位置关系
                        items.forEach(otherItem => {
                            if (otherItem !== draggedItem) {
                                const otherTop = otherItem.offsetTop;
                                const otherHeight = otherItem.offsetHeight;
                                
                                // 如果拖拽项与其他项重叠
                                if (newTop > otherTop && newTop < otherTop + otherHeight) {
                                    // 确定插入位置
                                    if (draggedItem.nextElementSibling === otherItem) {
                                        container.insertBefore(otherItem, draggedItem);
                                    } else if (draggedItem.previousElementSibling === otherItem) {
                                        container.insertBefore(draggedItem, otherItem);
                                    }
                                }
                            }
                        });
                    }
                    
                    // 鼠标抬起事件
                    function onMouseUp() {
                        document.removeEventListener('mousemove', onMouseMove);
                        document.removeEventListener('mouseup', onMouseUp);
                        
                        // 恢复样式
                        draggedItem.classList.remove('bg-blue-50', 'shadow-lg');
                        draggedItem.style.position = '';
                        draggedItem.style.top = '';
                        draggedItem.style.zIndex = '';
                        
                        draggedItem = null;
                    }
                    
                    document.addEventListener('mousemove', onMouseMove);
                    document.addEventListener('mouseup', onMouseUp);
                });
            });
        }

        // 更新预览显示函数
        function updatePreviewDisplay() {
            alert('功能开发中...');
        }

        // 添加产品按钮处理
        document.getElementById('addProductBtn').addEventListener('click', function() {
            const productInput = document.getElementById('addProductInput');
            const productName = productInput.value.trim();
            
            if (productName) {
                addProductTag(productName);
                productInput.value = '';
            }
        });

        // 添加产品输入框回车处理
        document.getElementById('addProductInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('addProductBtn').click();
            }
        });

        // 产品复选框处理
        document.querySelectorAll('.product-checkbox').forEach(function(checkbox) {
            checkbox.addEventListener('change', function() {
                const productName = this.value;
                const selectedProducts = document.getElementById('selectedProducts');
                
                if (this.checked) {
                    // 检查是否已存在
                    if (!document.querySelector(`#selectedProducts span[data-value="${productName}"]`)) {
                        addProductTag(productName);
                    }
                } else {
                    // 移除标签
                    const tag = document.querySelector(`#selectedProducts span[data-value="${productName}"]`);
                    if (tag) {
                        tag.remove();
                    }
                }
            });
        });

        // 添加产品标签
        function addProductTag(productName) {
            const selectedProducts = document.getElementById('selectedProducts');
            
            // 创建标签
            const tag = document.createElement('span');
            tag.className = 'px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded flex items-center';
            tag.setAttribute('data-value', productName);
            tag.innerHTML = `
                ${productName}
                <i class="fas fa-times ml-1 cursor-pointer remove-product-tag"></i>
            `;
            
            // 添加删除处理
            tag.querySelector('.remove-product-tag').addEventListener('click', function() {
                // 取消对应的复选框
                const checkbox = document.querySelector(`.product-checkbox[value="${productName}"]`);
                if (checkbox) {
                    checkbox.checked = false;
                }
                tag.remove();
            });
            
            selectedProducts.appendChild(tag);
        }

        // 更新支付方式
        function updatePaymentMethod(id, name, code, iconType, iconColor, status, title, description) {
            const item = document.querySelector(`.payment-method-item[data-id="${id}"]`);
            if (!item) return;
            
            // 更新名称和代码
            item.querySelector('h4').textContent = name;
            item.querySelector('p.text-xs').textContent = code;
            
            // 更新图标
            const iconContainer = item.querySelector('.w-8.h-8');
            iconContainer.className = `w-8 h-8 flex items-center justify-center bg-${iconColor}-100 text-${iconColor}-600 rounded-md mr-3`;
            
            const iconElement = iconContainer.querySelector('i');
            iconElement.className = `${iconType} text-xl`;
            
            // 更新状态
            item.querySelector('input[type="checkbox"]').checked = status;
            
            // 更新产品标签
            const productsContainer = item.querySelector('.flex-wrap.mb-2');
            productsContainer.innerHTML = '';
            
            const selectedTags = document.querySelectorAll('#selectedProducts span');
            selectedTags.forEach(function(tag) {
                const productName = tag.getAttribute('data-value');
                productsContainer.innerHTML += `<span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded mr-1">${productName}</span>`;
            });
            
            // 更新文案
            item.querySelector('.text-sm.text-gray-700.font-medium').textContent = title;
            item.querySelector('.text-xs.text-gray-600.flex-1').textContent = description;
        }

        // 生成规则项的HTML
        function generateRuleHTML(rule) {
            const statusClass = rule.status ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700';
            const statusIcon = rule.status ? 'fa-check-circle' : 'fa-ban';

            return `
                <div class="rule-item p-3 mb-2 border border-gray-200 rounded-md bg-white hover:bg-gray-50" data-id="${rule.id}">
                    <div class="flex justify-between">
                        <div class="flex items-center">
                            <div class="w-6 h-6 flex items-center justify-center text-gray-600 mr-2">
                                <i class="fas fa-filter"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-800">${rule.name}</h5>
                                <div class="flex flex-wrap gap-2 mt-1">
                                    <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">
                                        ${rule.type.replace(/_/g, ' ')}
                                    </span>
                                    <span class="px-2 py-0.5 bg-purple-100 text-purple-700 text-xs rounded">
                                        ${rule.operator} ${rule.value}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <span class="px-2 py-0.5 ${statusClass} text-xs rounded flex items-center">
                                <i class="fas ${statusIcon} mr-1"></i>${rule.status ? '启用' : '禁用'}
                            </span>
                        </div>
                    </div>
                </div>
            `;
        }

        // 按类型对规则进行分组
        function groupRulesByType(rules) {
            const groups = {};
            
            rules.forEach(rule => {
                const type = rule.type;
                if (!groups[type]) {
                    groups[type] = [];
                }
                groups[type].push(rule);
            });
            
            return groups;
        }

        // 添加条件模块卡片
        function addConditionModuleCard(id, name, description) {
            const moduleCard = document.createElement('div');
            moduleCard.className = 'condition-module-card';
            moduleCard.setAttribute('data-id', id);
            moduleCard.setAttribute('data-enabled', 'true');
            
            // 获取测试用例规则
            const rules = conditionModuleTestCases[id] || [];
            const rulesCount = rules.length;
            
            // 对规则按类型分组
            const ruleGroups = groupRulesByType(rules);
            const groupCount = Object.keys(ruleGroups).length;
            
            // 生成分组HTML
            let groupsHTML = '';
            let groupIndex = 1;
            
            for (const [type, groupRules] of Object.entries(ruleGroups)) {
                let rulesHTML = '';
                groupRules.forEach(rule => {
                    rulesHTML += generateRuleHTML(rule);
                });
                
                // 根据类型确定标签样式
                let typeLabel = type.replace(/_/g, ' ');
                let typeClassName = 'gray'; // Default color
                if (type.includes('user') || type.includes('vip')) {
                    typeClassName = 'blue';
                    typeLabel = typeLabel || '用户属性';
                } else if (type.includes('order') || type.includes('product')) {
                    typeClassName = 'green';
                    typeLabel = typeLabel || '订单属性';
                } else if (type.includes('payment')) {
                    typeClassName = 'yellow';
                    typeLabel = typeLabel || '支付相关';
                } else if (type.includes('env') || type.includes('time') || type.includes('device') || type.includes('holiday') || type.includes('weekday')) {
                    typeClassName = 'purple';
                    typeLabel = typeLabel || '环境条件';
                }
                typeLabel = typeLabel.charAt(0).toUpperCase() + typeLabel.slice(1); // 首字母大写
                
                groupsHTML += `
                    <div class="condition-group mb-4 border border-gray-200 rounded-lg overflow-hidden" data-group-id="group-${id}-${groupIndex}">
                        <div class="group-header flex justify-between items-center p-3 bg-gray-50 border-b border-gray-200 cursor-pointer" onclick="toggleGroupBody(this)">
                            <div class="flex items-center">
                                <i class="fas fa-chevron-down expand-icon mr-2 text-gray-500 text-xs"></i>
                                <span class="bg-${typeClassName}-100 text-${typeClassName}-800 px-2 py-0.5 rounded-full text-xs font-medium mr-2">组 ${groupIndex}</span>
                                <span class="text-sm font-medium text-gray-700">${typeLabel} 条件组</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">${groupRules.length} 条规则</span>
                            </div>
                        </div>
                        
                        <div class="group-body p-3 bg-white">
                            <div class="mb-3">
                                <h5 class="text-xs font-semibold text-gray-500 mb-1">组内条件关系</h5>
                                <div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-check-circle mr-1"></i>满足所有条件 (AND)
                                </div>
                            </div>
                            
                            <!-- 条件列表 -->
                            <div class="conditions-list space-y-2">
                                ${rulesHTML}
                            </div>
                        </div>
                    </div>
                `;
                
                groupIndex++;
            }
            
            moduleCard.innerHTML = `
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-project-diagram mr-2 text-primary-color"></i>
                        ${name}
                    </div>
                    <div class="card-actions">
                        <span class="rule-count">
                            <i class="fas fa-sitemap mr-1 text-gray-500"></i>
                            <span class="count">${groupCount}</span> 个组 / 
                            <i class="fas fa-list-ul mr-1 text-gray-500"></i>
                            <span class="count">${rulesCount}</span> 条规则
                        </span>
                        <button type="button" class="text-gray-600 hover:text-blue-600 toggle-module-btn p-1 rounded hover:bg-gray-100">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </button>
                        <button type="button" class="text-gray-600 hover:text-red-600 remove-module-btn p-1 rounded hover:bg-gray-100">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body expanded">
                    <p class="text-sm text-gray-600 mb-4 pb-3 border-b border-gray-200">${description}</p>
                    
                    <!-- 条件组间关系 -->
                    ${groupCount > 1 ? `
                    <div class="mb-4 pb-3">
                        <h4 class="text-sm font-semibold text-gray-600 mb-2">条件组关系</h4>
                        <div class="p-2 bg-gray-50 rounded-md inline-block">
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                <i class="fas fa-check-double mr-1"></i>满足所有条件组 (AND)
                            </span>
                        </div>
                    </div>
                    ` : ''}
                    
                    <!-- 条件组列表 -->
                    <div class="condition-groups">
                        ${groupsHTML}
                        ${rulesCount === 0 ? `
                        <div class="info-message p-4 border-dashed border-gray-300 rounded-md">
                            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                            <span>此模块当前没有定义具体规则。请前往 <a href="condition_module.html" class="text-blue-600 hover:underline">条件模块管理</a> 配置规则。</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
            
            // 添加事件监听
            moduleCard.querySelector('.toggle-module-btn').addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                const body = moduleCard.querySelector('.card-body');
                body.classList.toggle('expanded');
                
                // 切换图标
                const icon = this.querySelector('i');
                if (body.classList.contains('expanded')) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                    body.style.height = 'auto'; // 展开时设置高度为 auto
                    let actualHeight = body.scrollHeight + "px"; // 获取实际高度
                    body.style.height = '0'; // 先设为0触发动画
                    requestAnimationFrame(() => { // 延迟设置实际高度
                        body.style.height = actualHeight;
                    });
                } else {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                    body.style.height = body.scrollHeight + "px"; // 设置当前高度
                    requestAnimationFrame(() => { // 延迟设为0触发动画
                       body.style.height = '0';
                    });
                }
            });
            
            // 默认展开卡片（修改逻辑）
             setTimeout(() => {
                 const body = moduleCard.querySelector('.card-body');
                 const toggleBtn = moduleCard.querySelector('.toggle-module-btn');
                 const icon = toggleBtn.querySelector('i');
                 if (body.classList.contains('expanded')) {
                     body.style.height = 'auto'; // 设置为auto以适应内容
                     icon.classList.remove('fa-chevron-down');
                     icon.classList.add('fa-chevron-up');
                 } else {
                     body.style.height = '0';
                     icon.classList.remove('fa-chevron-up');
                     icon.classList.add('fa-chevron-down');
                 }
             }, 10); // 稍微延迟以确保元素渲染
            
            moduleCard.querySelector('.remove-module-btn').addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                moduleCard.remove();
                
                // 如果没有模块，显示空提示
                if (document.querySelectorAll('#selectedConditionModules .condition-module-card').length === 0) {
                    document.getElementById('noConditionsMessage').style.display = 'flex';
                    document.getElementById('selectedConditionModules').style.display = 'none';
                }
            });
            
            // 添加卡片头部点击展开/折叠功能 (保持不变)
            moduleCard.querySelector('.card-header').addEventListener('click', function() {
                const toggleBtn = moduleCard.querySelector('.toggle-module-btn');
                toggleBtn.click(); // 触发折叠按钮的点击事件
            });
            
            // 添加到容器
            document.getElementById('selectedConditionModules').appendChild(moduleCard);
        }

        // 条件组折叠/展开功能
        function toggleGroupBody(header) {
            const groupBody = header.nextElementSibling;
            const expandIcon = header.querySelector('.expand-icon');
            
            if (groupBody.style.maxHeight && groupBody.style.maxHeight !== '0px') {
                 // 折叠
                 groupBody.style.maxHeight = '0px';
                 expandIcon.classList.add('collapsed');
            } else {
                 // 展开
                 groupBody.style.maxHeight = groupBody.scrollHeight + "px";
                 expandIcon.classList.remove('collapsed');
                 // 添加一个监听器，以便在过渡结束后移除max-height，允许内容动态变化
                 groupBody.addEventListener('transitionend', function handler() {
                     groupBody.removeEventListener('transitionend', handler);
                     if (!expandIcon.classList.contains('collapsed')) { // 确保是展开状态
                         groupBody.style.maxHeight = null; // 或者 'none'
                     }
                 });
            }
        }

        // 添加全局函数
        window.toggleGroupBody = toggleGroupBody;
    </script>
</body>
</html>

<!-- 添加模态框结构 -->

<!-- JSON预览模态框 -->
<div id="jsonPreviewModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>条件配置JSON预览</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <pre id="jsonPreview" class="bg-gray-100 p-4 rounded-md overflow-auto" style="max-height: 400px;"></pre>
        </div>
        <div class="modal-footer">
            <button id="copyJsonBtn" class="btn btn-primary">
                <i class="fas fa-copy mr-2"></i>复制到剪贴板
            </button>
            <button class="btn btn-light close">关闭</button>
        </div>
    </div>
</div>

<!-- 条件模块选择模态框 -->
<div id="selectConditionModuleModal" class="modal">
    <div class="modal-content" style="max-width: 600px; max-height: 80vh; margin: 5vh auto;">
        <div class="modal-header">
            <h3>选择条件模块</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
            <div class="mb-4">
                <input type="text" id="searchConditionModule" placeholder="搜索条件模块..." class="w-full px-4 py-2 border rounded-md">
            </div>
            <div id="conditionModulesList" class="space-y-2">
                <!-- 条件模块选项 -->
                <div class="module-option p-3 border rounded-md hover:bg-blue-50 cursor-pointer" data-id="1">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <input type="radio" name="conditionModule" class="mr-2 h-4 w-4 module-radio">
                            <h4 class="module-name font-medium text-gray-800">用户属性条件</h4>
                        </div>
                        <div class="flex space-x-2">
                            <span class="module-tag bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs">用户</span>
                        </div>
                    </div>
                    <p class="module-desc text-sm text-gray-600 mt-1 ml-6">根据用户属性（如VIP等级、注册时间）设置条件规则</p>
                </div>
                <div class="module-option p-3 border rounded-md hover:bg-blue-50 cursor-pointer" data-id="2">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <input type="radio" name="conditionModule" class="mr-2 h-4 w-4 module-radio">
                            <h4 class="module-name font-medium text-gray-800">订单属性条件</h4>
                        </div>
                        <div class="flex space-x-2">
                            <span class="module-tag bg-green-100 text-green-600 px-2 py-1 rounded-md text-xs">订单</span>
                        </div>
                    </div>
                    <p class="module-desc text-sm text-gray-600 mt-1 ml-6">根据订单属性（如订单金额、商品类型）设置条件规则</p>
                </div>
                <div class="module-option p-3 border rounded-md hover:bg-blue-50 cursor-pointer" data-id="3">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <input type="radio" name="conditionModule" class="mr-2 h-4 w-4 module-radio">
                            <h4 class="module-name font-medium text-gray-800">环境条件</h4>
                        </div>
                        <div class="flex space-x-2">
                            <span class="module-tag bg-purple-100 text-purple-600 px-2 py-1 rounded-md text-xs">环境</span>
                        </div>
                    </div>
                    <p class="module-desc text-sm text-gray-600 mt-1 ml-6">根据环境参数（如时间、地区、设备类型）设置条件规则</p>
                </div>
                <div class="module-option p-3 border rounded-md hover:bg-blue-50 cursor-pointer" data-id="4">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center">
                            <input type="radio" name="conditionModule" class="mr-2 h-4 w-4 module-radio">
                            <h4 class="module-name font-medium text-gray-800">支付相关条件</h4>
                        </div>
                        <div class="flex space-x-2">
                            <span class="module-tag bg-yellow-100 text-yellow-600 px-2 py-1 rounded-md text-xs">支付</span>
                        </div>
                    </div>
                    <p class="module-desc text-sm text-gray-600 mt-1 ml-6">根据支付相关信息（如支付方式偏好、历史支付行为）设置条件规则</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="confirmSelectModule" class="btn btn-primary">确认选择</button>
            <button class="btn btn-light close">取消</button>
        </div>
    </div>
</div>

<!-- 添加支付方式模态框 -->
<div id="addPaymentMethodModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>添加支付方式</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="mb-4">
                <input type="text" id="searchPaymentMethod" placeholder="搜索支付方式..." class="w-full px-4 py-2 border rounded-md">
            </div>
            <div class="space-y-3">
                <!-- 支付方式选项 -->
                <div class="payment-method-option border rounded-md hover:bg-blue-50 cursor-pointer overflow-hidden" 
                     data-id="3" data-name="银联支付" data-code="UNIONPAY" data-status="true"
                     data-icon="fab fa-cc-visa" data-icon-color="red" 
                     data-title="银联支付 - 安全快捷" data-description="中国银联支付，支持全国各大银行，安全便捷。">
                    <div class="p-3 flex items-center border-b border-gray-100">
                        <div class="w-8 h-8 flex items-center justify-center bg-red-100 text-red-600 rounded-md mr-3">
                            <i class="fab fa-cc-visa text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">银联支付</h4>
                                    <p class="text-xs text-gray-500">UNIONPAY</p>
                                </div>
                                <div class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded flex items-center">
                                    <i class="fas fa-check-circle mr-1"></i>启用
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-3 py-2">
                        <div class="text-xs text-gray-500 mb-1">关联支付产品</div>
                        <div class="flex flex-wrap gap-1 mb-2">
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">银联云闪付</span>
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">银联快捷支付</span>
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">银联在线支付</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-1">显示文案</div>
                        <div class="text-sm text-gray-700 font-medium">银联支付 - 安全快捷</div>
                        <div class="text-xs text-gray-600 mt-1">中国银联支付，支持全国各大银行，安全便捷。</div>
                    </div>
                </div>
                
                <div class="payment-method-option border rounded-md hover:bg-blue-50 cursor-pointer overflow-hidden" 
                     data-id="4" data-name="花呗分期" data-code="HUABEI" data-status="true"
                     data-icon="fab fa-alipay" data-icon-color="blue" 
                     data-title="花呗分期 - 先享后付" data-description="支付宝旗下花呗产品，支持3-12期分期付款，0首付。">
                    <div class="p-3 flex items-center border-b border-gray-100">
                        <div class="w-8 h-8 flex items-center justify-center bg-blue-100 text-blue-600 rounded-md mr-3">
                            <i class="fab fa-alipay text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">花呗分期</h4>
                                    <p class="text-xs text-gray-500">HUABEI</p>
                                </div>
                                <div class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded flex items-center">
                                    <i class="fas fa-check-circle mr-1"></i>启用
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-3 py-2">
                        <div class="text-xs text-gray-500 mb-1">关联支付产品</div>
                        <div class="flex flex-wrap gap-1 mb-2">
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">支付宝</span>
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">花呗</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-1">显示文案</div>
                        <div class="text-sm text-gray-700 font-medium">花呗分期 - 先享后付</div>
                        <div class="text-xs text-gray-600 mt-1">支付宝旗下花呗产品，支持3-12期分期付款，0首付。</div>
                    </div>
                </div>
                
                <div class="payment-method-option border rounded-md hover:bg-blue-50 cursor-pointer overflow-hidden" 
                     data-id="5" data-name="信用卡支付" data-code="CREDIT_CARD" data-status="true"
                     data-icon="fas fa-credit-card" data-icon-color="purple" 
                     data-title="信用卡支付 - 快速到账" data-description="支持国内外主流信用卡，安全稳定，快速到账。">
                    <div class="p-3 flex items-center border-b border-gray-100">
                        <div class="w-8 h-8 flex items-center justify-center bg-purple-100 text-purple-600 rounded-md mr-3">
                            <i class="fas fa-credit-card text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">信用卡支付</h4>
                                    <p class="text-xs text-gray-500">CREDIT_CARD</p>
                                </div>
                                <div class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded flex items-center">
                                    <i class="fas fa-check-circle mr-1"></i>启用
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-3 py-2">
                        <div class="text-xs text-gray-500 mb-1">关联支付产品</div>
                        <div class="flex flex-wrap gap-1 mb-2">
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">Visa</span>
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">MasterCard</span>
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">JCB</span>
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">银联信用卡</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-1">显示文案</div>
                        <div class="text-sm text-gray-700 font-medium">信用卡支付 - 快速到账</div>
                        <div class="text-xs text-gray-600 mt-1">支持国内外主流信用卡，安全稳定，快速到账。</div>
                    </div>
                </div>
                
                <div class="payment-method-option border rounded-md hover:bg-blue-50 cursor-pointer overflow-hidden" 
                     data-id="6" data-name="ApplePay" data-code="APPLE_PAY" data-status="true"
                     data-icon="fab fa-apple" data-icon-color="gray" 
                     data-title="ApplePay - 隐私保护" data-description="Apple设备专属支付方式，指纹、面容ID一键完成支付。">
                    <div class="p-3 flex items-center border-b border-gray-100">
                        <div class="w-8 h-8 flex items-center justify-center bg-gray-100 text-gray-800 rounded-md mr-3">
                            <i class="fab fa-apple text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">ApplePay</h4>
                                    <p class="text-xs text-gray-500">APPLE_PAY</p>
                                </div>
                                <div class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded flex items-center">
                                    <i class="fas fa-check-circle mr-1"></i>启用
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-3 py-2">
                        <div class="text-xs text-gray-500 mb-1">关联支付产品</div>
                        <div class="flex flex-wrap gap-1 mb-2">
                            <span class="px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded">Apple支付</span>
                        </div>
                        <div class="text-xs text-gray-500 mb-1">显示文案</div>
                        <div class="text-sm text-gray-700 font-medium">ApplePay - 隐私保护</div>
                        <div class="text-xs text-gray-600 mt-1">Apple设备专属支付方式，指纹、面容ID一键完成支付。</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="confirmAddPaymentMethod" class="btn btn-primary">确认添加</button>
            <button class="btn btn-light close">取消</button>
        </div>
    </div>
</div>

<!-- 编辑支付方式模态框 -->
<div id="editPaymentMethodModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>编辑支付方式</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <input type="hidden" id="editPaymentMethodId">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="editPaymentMethodName">
                        支付方式名称
                    </label>
                    <input id="editPaymentMethodName" type="text" class="w-full px-3 py-2 border rounded-md">
                </div>
                <div>
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="editPaymentMethodCode">
                        支付方式代码
                    </label>
                    <input id="editPaymentMethodCode" type="text" class="w-full px-3 py-2 border rounded-md">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        图标选择
                    </label>
                    <div class="grid grid-cols-4 gap-2">
                        <div class="icon-option flex flex-col items-center p-2 border rounded-md cursor-pointer" data-icon="fab fa-alipay" data-color="blue">
                            <div class="w-10 h-10 flex items-center justify-center bg-blue-100 text-blue-600 rounded-md mb-1">
                                <i class="fab fa-alipay text-xl"></i>
                            </div>
                            <span class="text-xs text-center">支付宝</span>
                        </div>
                        <div class="icon-option flex flex-col items-center p-2 border rounded-md cursor-pointer" data-icon="fab fa-weixin" data-color="green">
                            <div class="w-10 h-10 flex items-center justify-center bg-green-100 text-green-600 rounded-md mb-1">
                                <i class="fab fa-weixin text-xl"></i>
                            </div>
                            <span class="text-xs text-center">微信</span>
                        </div>
                        <div class="icon-option flex flex-col items-center p-2 border rounded-md cursor-pointer" data-icon="fas fa-credit-card" data-color="purple">
                            <div class="w-10 h-10 flex items-center justify-center bg-purple-100 text-purple-600 rounded-md mb-1">
                                <i class="fas fa-credit-card text-xl"></i>
                            </div>
                            <span class="text-xs text-center">信用卡</span>
                        </div>
                        <div class="icon-option flex flex-col items-center p-2 border rounded-md cursor-pointer" data-icon="fab fa-cc-visa" data-color="red">
                            <div class="w-10 h-10 flex items-center justify-center bg-red-100 text-red-600 rounded-md mb-1">
                                <i class="fab fa-cc-visa text-xl"></i>
                            </div>
                            <span class="text-xs text-center">银联</span>
                        </div>
                    </div>
                </div>
                <div class="flex items-center mt-2">
                    <input id="editPaymentMethodStatus" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="editPaymentMethodStatus" class="ml-2 block text-gray-700">
                        启用此支付方式
                    </label>
                </div>
                <div class="md:col-span-2 mt-2">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        关联支付产品
                    </label>
                    <div class="border border-gray-300 rounded-md p-3 bg-gray-50">
                        <div class="grid grid-cols-2 gap-2">
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product1" class="product-checkbox" value="支付宝">
                                <label for="product1" class="text-sm">支付宝</label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product2" class="product-checkbox" value="微信支付">
                                <label for="product2" class="text-sm">微信支付</label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product3" class="product-checkbox" value="银联云闪付">
                                <label for="product3" class="text-sm">银联云闪付</label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product4" class="product-checkbox" value="银联快捷支付">
                                <label for="product4" class="text-sm">银联快捷支付</label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product5" class="product-checkbox" value="花呗">
                                <label for="product5" class="text-sm">花呗</label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product6" class="product-checkbox" value="Visa">
                                <label for="product6" class="text-sm">Visa</label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product7" class="product-checkbox" value="MasterCard">
                                <label for="product7" class="text-sm">MasterCard</label>
                            </div>
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="product8" class="product-checkbox" value="Apple支付">
                                <label for="product8" class="text-sm">Apple支付</label>
                            </div>
                        </div>
                        <div class="mt-2 flex items-center">
                            <input type="text" id="addProductInput" class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm" placeholder="添加其他产品...">
                            <button type="button" id="addProductBtn" class="ml-2 px-2 py-1 bg-blue-500 text-white rounded-md text-sm">添加</button>
                        </div>
                        <div class="mt-3 flex flex-wrap gap-2" id="selectedProducts">
                            <!-- 已选产品会显示在这里 -->
                        </div>
                    </div>
                </div>
                <div class="md:col-span-2 mt-2">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="editPaymentMethodTitle">
                        显示标题
                    </label>
                    <input id="editPaymentMethodTitle" type="text" class="w-full px-3 py-2 border rounded-md">
                </div>
                <div class="md:col-span-2">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="editPaymentMethodDescription">
                        显示描述
                    </label>
                    <textarea id="editPaymentMethodDescription" class="w-full px-3 py-2 border rounded-md" rows="2"></textarea>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="confirmEditPaymentMethod" class="btn btn-primary">保存修改</button>
            <button class="btn btn-light close">取消</button>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteConfirmModal" class="modal">
    <div class="modal-content" style="max-width: 500px;">
        <div class="modal-header">
            <h3>确认删除</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <input type="hidden" id="deletePaymentMethodId">
            <p class="text-center">确定要删除支付方式"<span id="deletePaymentMethodName" class="font-medium"></span>"吗？</p>
            <p class="text-center text-gray-500 text-sm mt-2">删除后将无法恢复</p>
        </div>
        <div class="modal-footer">
            <button id="confirmDeletePaymentMethod" class="btn btn-danger">确认删除</button>
            <button class="btn btn-light close">取消</button>
        </div>
    </div>
</div>
</body>
</html>
