<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付场景预览 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 模拟手机样式 */
        .phone-wrapper {
            position: relative;
            width: 320px;
            height: 640px;
            margin: 0 auto;
            border: 12px solid #111;
            border-radius: 36px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .phone-notch {
            position: absolute;
            top: 0;
            width: 30%;
            height: 25px;
            left: 35%;
            background-color: #111;
            border-bottom-left-radius: 16px;
            border-bottom-right-radius: 16px;
            z-index: 10;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background-color: white;
            overflow-y: auto;
            position: relative;
        }

        /* 模拟支付方式样式 */
        .payment-method {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .payment-method:hover {
            background-color: #f9f9f9;
        }
        
        .payment-method.selected {
            background-color: #f0f7ff;
        }
        
        .payment-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        .payment-icon img {
            width: 24px;
            height: 24px;
        }
        
        .payment-info {
            flex: 1;
        }
        
        .payment-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .payment-desc {
            font-size: 12px;
            color: #999;
        }
        
        .payment-action {
            width: 20px;
            height: 20px;
            border: 1px solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .payment-method.selected .payment-action {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .payment-method.selected .payment-action::after {
            content: '';
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: white;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300 transform rotate-180"></i>
                    </div>
                    <ul class="submenu active pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">支付场景预览</h2>
                <p class="text-gray-500 mt-1">预览不同条件下的支付场景展示效果</p>
            </div>
            <div class="flex space-x-2">
                <a href="payment_scene.html" class="btn btn-light flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>返回场景列表
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：条件模拟区域 -->
            <div class="lg:col-span-2">
                <div class="card p-5 mb-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">选择支付场景</h3>
                    <div class="flex flex-wrap items-center">
                        <div class="w-full md:w-2/3 md:pr-2 mb-4 md:mb-0">
                            <select class="w-full px-4 py-2">
                                <option value="PS001">PS001 - 电商普通商品购买</option>
                                <option value="PS002">PS002 - 会员充值</option>
                                <option value="PS003">PS003 - 线下门店扫码支付</option>
                                <option value="PS004">PS004 - 限时优惠活动</option>
                            </select>
                        </div>
                        <div class="w-full md:w-1/3 md:pl-2">
                            <select class="w-full px-4 py-2">
                                <option value="v1.5">v1.5 (当前版本)</option>
                                <option value="v1.4">v1.4</option>
                                <option value="v1.3">v1.3</option>
                                <option value="v1.2">v1.2</option>
                                <option value="v1.1">v1.1</option>
                                <option value="v1.0">v1.0</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="card p-5 mb-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">条件模拟</h3>
                    
                    <!-- 条件分类导航 -->
                    <div class="border-b border-gray-200 mb-4">
                        <nav class="flex -mb-px" aria-label="Tabs">
                            <button class="text-primary border-primary border-b-2 px-4 py-2 text-sm font-medium">
                                用户属性
                            </button>
                            <button class="text-gray-500 hover:text-gray-700 border-transparent border-b-2 px-4 py-2 text-sm font-medium">
                                订单属性
                            </button>
                            <button class="text-gray-500 hover:text-gray-700 border-transparent border-b-2 px-4 py-2 text-sm font-medium">
                                环境条件
                            </button>
                            <button class="text-gray-500 hover:text-gray-700 border-transparent border-b-2 px-4 py-2 text-sm font-medium">
                                支付相关
                            </button>
                        </nav>
                    </div>
                    
                    <!-- 用户属性条件 -->
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">用户等级</label>
                                <select class="w-full">
                                    <option value="0">普通用户</option>
                                    <option value="1">青铜会员</option>
                                    <option value="2">白银会员</option>
                                    <option value="3">黄金会员</option>
                                    <option value="4">铂金会员</option>
                                    <option value="5">钻石会员</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">用户注册时长</label>
                                <select class="w-full">
                                    <option value="0">新注册用户(0-7天)</option>
                                    <option value="1">一般用户(7-30天)</option>
                                    <option value="2">活跃用户(30-180天)</option>
                                    <option value="3">忠实用户(180天以上)</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">用户标签</label>
                                <select class="w-full">
                                    <option value="">无特殊标签</option>
                                    <option value="new_user">新用户</option>
                                    <option value="risky_user">风险用户</option>
                                    <option value="vip_user">VIP客户</option>
                                    <option value="enterprise">企业用户</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">用户所在地区</label>
                                <select class="w-full">
                                    <option value="all">全国</option>
                                    <option value="north">华北地区</option>
                                    <option value="east">华东地区</option>
                                    <option value="south">华南地区</option>
                                    <option value="west">西部地区</option>
                                    <option value="overseas">海外</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">用户历史订单数</label>
                                <select class="w-full">
                                    <option value="0">0单</option>
                                    <option value="1-5">1-5单</option>
                                    <option value="6-20">6-20单</option>
                                    <option value="21-50">21-50单</option>
                                    <option value="50+">50单以上</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">用户年龄段</label>
                                <select class="w-full">
                                    <option value="unknown">未知</option>
                                    <option value="under18">18岁以下</option>
                                    <option value="18-24">18-24岁</option>
                                    <option value="25-34">25-34岁</option>
                                    <option value="35-44">35-44岁</option>
                                    <option value="45+">45岁以上</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card p-5 mb-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">订单信息</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">订单金额</label>
                            <div class="flex items-center">
                                <span class="text-gray-500 mr-2">¥</span>
                                <input type="number" value="1999.00" class="w-full">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">商品类型</label>
                            <select class="w-full">
                                <option value="normal">普通商品</option>
                                <option value="digital">数字商品</option>
                                <option value="service">服务类商品</option>
                                <option value="food">餐饮</option>
                                <option value="membership">会员充值</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">终端设备</label>
                            <select class="w-full">
                                <option value="ios">iOS</option>
                                <option value="android">Android</option>
                                <option value="web">网页端</option>
                                <option value="miniprogram">小程序</option>
                                <option value="pos">POS机</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">支付场景</label>
                            <select class="w-full">
                                <option value="online">线上场景</option>
                                <option value="offline">线下场景</option>
                                <option value="app">APP内支付</option>
                                <option value="h5">H5页面</option>
                                <option value="scan">扫码支付</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end">
                    <button class="btn-gradient px-6 py-2 rounded-lg flex items-center shadow-md hover:shadow-lg">
                        <i class="fas fa-eye mr-2"></i>刷新预览
                    </button>
                </div>
            </div>
            
            <!-- 右侧：手机预览区域 -->
            <div class="lg:col-span-1">
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4 text-center">收银台预览</h3>
                    
                    <div class="phone-wrapper">
                        <div class="phone-notch"></div>
                        <div class="phone-screen">
                            <!-- 模拟手机界面 -->
                            <div class="bg-gray-100 p-4">
                                <div class="flex justify-between items-center">
                                    <div class="text-lg font-bold">确认支付</div>
                                    <div><i class="fas fa-times"></i></div>
                                </div>
                                <div class="bg-white rounded-lg p-4 mt-4">
                                    <div class="text-center">
                                        <div class="text-sm text-gray-500">支付金额</div>
                                        <div class="text-2xl font-bold">¥1,999.00</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 支付方式列表 -->
                            <div class="pt-3 pb-20">
                                <div class="px-4 pb-2 text-sm font-medium text-gray-500">选择支付方式</div>
                                
                                <!-- 微信支付 -->
                                <div class="payment-method selected">
                                    <div class="payment-icon">
                                        <img src="https://cdn.jsdelivr.net/gh/Mayandev/nodejs-shared/payment-icons/wechat-pay.png" alt="WeChat Pay">
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-title">微信支付</div>
                                        <div class="payment-desc">微信扫码支付</div>
                                    </div>
                                    <div class="payment-action"></div>
                                </div>
                                
                                <!-- 支付宝 -->
                                <div class="payment-method">
                                    <div class="payment-icon">
                                        <img src="https://cdn.jsdelivr.net/gh/Mayandev/nodejs-shared/payment-icons/alipay.png" alt="Alipay">
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-title">支付宝</div>
                                        <div class="payment-desc">支付宝扫码支付</div>
                                    </div>
                                    <div class="payment-action"></div>
                                </div>
                                
                                <!-- 银联支付 -->
                                <div class="payment-method">
                                    <div class="payment-icon">
                                        <img src="https://cdn.jsdelivr.net/gh/Mayandev/nodejs-shared/payment-icons/unionpay.png" alt="UnionPay">
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-title">银联支付</div>
                                        <div class="payment-desc">使用银联卡付款</div>
                                    </div>
                                    <div class="payment-action"></div>
                                </div>
                                
                                <!-- 花呗分期 -->
                                <div class="payment-method">
                                    <div class="payment-icon">
                                        <img src="https://cdn.jsdelivr.net/gh/Mayandev/nodejs-shared/payment-icons/huabei.png" alt="Huabei">
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-title">花呗分期</div>
                                        <div class="payment-desc">3期免息，每期¥666.33</div>
                                    </div>
                                    <div class="payment-action"></div>
                                </div>
                                
                                <!-- Apple Pay -->
                                <div class="payment-method">
                                    <div class="payment-icon">
                                        <img src="https://cdn.jsdelivr.net/gh/Mayandev/nodejs-shared/payment-icons/apple-pay.png" alt="Apple Pay">
                                    </div>
                                    <div class="payment-info">
                                        <div class="payment-title">Apple Pay</div>
                                        <div class="payment-desc">使用Apple设备支付</div>
                                    </div>
                                    <div class="payment-action"></div>
                                </div>
                            </div>
                            
                            <!-- 底部按钮 -->
                            <div class="fixed bottom-0 left-0 right-0 bg-white shadow-md p-3 flex justify-center">
                                <button class="bg-primary w-full py-3 rounded-lg text-white font-medium">
                                    确认支付 ¥1,999.00
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <div class="flex justify-between items-center mb-3">
                            <h4 class="font-medium">预览信息</h4>
                            <button class="text-primary text-sm">查看完整场景配置</button>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4 text-sm">
                            <div class="mb-2">
                                <span class="font-medium">场景名称:</span> 电商普通商品购买
                            </div>
                            <div class="mb-2">
                                <span class="font-medium">生效条件:</span> 普通用户，商品类型:普通商品
                            </div>
                            <div class="mb-2">
                                <span class="font-medium">当前显示支付方式:</span> 5种
                            </div>
                            <div>
                                <span class="font-medium">隐藏支付方式:</span> 1种
                                <div class="text-gray-500 text-xs mt-1">
                                    余额支付(条件：用户等级>=3)
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 支付方式选择
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => {
                    m.classList.remove('selected');
                });
                this.classList.add('selected');
            });
        });
    </script>
</body>
</html> 
