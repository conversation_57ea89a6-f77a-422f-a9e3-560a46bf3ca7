<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付场景版本历史 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <!-- 添加Material Design图标 -->
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <!-- 添加Google字体的镜像CDN -->
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 渐变按钮 */
        .btn-gradient {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-gradient:hover {
            box-shadow: 0 5px 15px rgba(108, 92, 231, 0.4);
            transform: translateY(-2px);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }

        .nav-item.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            white-space: nowrap;
        }
        
        .tag-blue {
            background-color: rgba(59, 124, 254, 0.15);
            color: var(--primary-color);
        }
        
        .tag-green {
            background-color: rgba(0, 184, 148, 0.15);
            color: var(--success-color);
        }
        
        .tag-purple {
            background-color: rgba(108, 92, 231, 0.15);
            color: var(--secondary-color);
        }
        
        .tag-yellow {
            background-color: rgba(253, 203, 110, 0.15);
            color: #d6a100;
        }
        
        .tag-red {
            background-color: rgba(225, 112, 85, 0.15);
            color: var(--danger-color);
        }
        
        .tag-gray {
            background-color: rgba(45, 52, 54, 0.1);
            color: #636e72;
        }

        /* 美化表单元素 */
        input, select, textarea {
            border-radius: 0.5rem !important;
            border: 1px solid #e0e6ed !important;
            padding: 0.5rem 1rem !important;
            transition: all 0.3s !important;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: var(--primary-color) !important;
            box-shadow: 0 0 0 3px rgba(59, 124, 254, 0.2) !important;
            outline: none !important;
        }

        /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }
        
        .btn-success {
            background-color: var(--success-color);
            color: white;
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }
        
        .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }

        /* 时间线样式 */
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 2px;
            height: 100%;
            background-color: var(--primary-light);
        }
        
        .timeline-item {
            position: relative;
            padding-bottom: 1.5rem;
            padding-left: 1rem;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: var(--primary-color);
            border: 3px solid white;
            box-shadow: 0 0 0 4px rgba(59, 124, 254, 0.2);
        }
        
        .timeline-item.current::before {
            background-color: var(--success-color);
            box-shadow: 0 0 0 4px rgba(0, 184, 148, 0.2);
        }

        /* 版本对比选择器 */
        .version-selector-card {
            border: 2px dashed var(--border-color);
            border-radius: 0.75rem;
            padding: 1rem;
            transition: all 0.3s;
        }
        
        .version-selector-card:hover {
            border-color: var(--primary-color);
            background-color: var(--primary-light);
        }
        
        .version-selector-card.selected {
            border-color: var(--primary-color);
            border-style: solid;
            background-color: var(--primary-light);
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-user mr-2 text-gray-500"></i>个人信息
                        </a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">
                            <i class="fas fa-cog mr-2 text-gray-500"></i>系统设置
                        </a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">
                            <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item active">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-primary cursor-pointer rounded-lg bg-primary-light">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4 active">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-primary font-semibold">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>

                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant_account.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="app_manager.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_logs.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h2 class="text-2xl font-bold text-gray-800">支付场景版本历史</h2>
                <p class="text-gray-500 mt-1">查看和管理支付场景的历史版本、比较版本差异和回滚操作</p>
            </div>
            <div class="flex space-x-2">
                <a href="payment_scene.html" class="btn btn-light flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i>返回场景列表
                </a>
            </div>
        </div>

        <!-- 支付场景选择 -->
        <div class="card p-5 mb-6">
            <div class="flex flex-wrap items-center">
                <div class="w-full md:w-1/3 md:pr-2 mb-4 md:mb-0">
                    <h3 class="text-md font-medium text-gray-700 mb-2">选择支付场景</h3>
                    <select class="w-full px-4 py-2">
                        <option value="PS001">PS001 - 电商普通商品购买</option>
                        <option value="PS002">PS002 - 会员充值</option>
                        <option value="PS003">PS003 - 线下门店扫码支付</option>
                        <option value="PS004">PS004 - 限时优惠活动</option>
                    </select>
                </div>
                <div class="w-full md:w-1/3 md:px-2 mb-4 md:mb-0">
                    <h3 class="text-md font-medium text-gray-700 mb-2">当前生效版本</h3>
                    <div class="flex items-center">
                        <span class="tag tag-green flex items-center">
                            <i class="fas fa-check-circle mr-1"></i>
                            v1.5
                        </span>
                        <span class="text-sm text-gray-500 ml-2">更新于 2023-08-15 14:30</span>
                    </div>
                </div>
                <div class="w-full md:w-1/3 md:pl-2 flex items-end">
                    <div class="flex mt-4 md:mt-0 space-x-2 w-full">
                        <button class="btn btn-primary flex items-center justify-center flex-1">
                            <i class="fas fa-random mr-2"></i>版本对比
                        </button>
                        <button class="btn btn-danger flex items-center justify-center flex-1">
                            <i class="fas fa-history mr-2"></i>版本回滚
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧：版本历史 -->
            <div class="lg:col-span-1">
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">版本历史</h3>
                    
                    <div class="timeline">
                        <!-- 当前版本 -->
                        <div class="timeline-item current">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center">
                                        <span class="font-bold text-lg">v1.5</span>
                                        <span class="tag tag-green ml-2">当前版本</span>
                                    </div>
                                    <p class="text-gray-500 text-sm">2023-08-15 14:30</p>
                                </div>
                                <div class="flex">
                                    <button class="text-blue-600 hover:text-blue-800 mr-2 text-sm" title="选择">
                                        <i class="fas fa-check-circle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-sm">优化了条件筛选规则，增加了节假日特殊逻辑处理</p>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    操作人：张经理
                                </span>
                            </div>
                        </div>
                        
                        <!-- 历史版本 -->
                        <div class="timeline-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <span class="font-bold text-lg">v1.4</span>
                                    <p class="text-gray-500 text-sm">2023-08-10 09:45</p>
                                </div>
                                <div class="flex">
                                    <button class="text-blue-600 hover:text-blue-800 mr-2 text-sm" title="选择">
                                        <i class="far fa-check-circle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-sm">调整支付方式排序和展示方式，微信支付默认置顶</p>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    操作人：李运营
                                </span>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <span class="font-bold text-lg">v1.3</span>
                                    <p class="text-gray-500 text-sm">2023-08-05 16:20</p>
                                </div>
                                <div class="flex">
                                    <button class="text-blue-600 hover:text-blue-800 mr-2 text-sm" title="选择">
                                        <i class="far fa-check-circle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-sm">新增"会员折扣"条件规则，会员等级>3可使用花呗分期</p>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    操作人：王产品
                                </span>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <span class="font-bold text-lg">v1.2</span>
                                    <p class="text-gray-500 text-sm">2023-07-25 11:10</p>
                                </div>
                                <div class="flex">
                                    <button class="text-blue-600 hover:text-blue-800 mr-2 text-sm" title="选择">
                                        <i class="far fa-check-circle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-sm">修复条件规则冲突问题，优化交互提示文案</p>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    操作人：张经理
                                </span>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <span class="font-bold text-lg">v1.1</span>
                                    <p class="text-gray-500 text-sm">2023-07-18 14:05</p>
                                </div>
                                <div class="flex">
                                    <button class="text-blue-600 hover:text-blue-800 mr-2 text-sm" title="选择">
                                        <i class="far fa-check-circle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-sm">修改部分文案，支持支付宝小程序应用</p>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    操作人：王产品
                                </span>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="flex justify-between items-start">
                                <div>
                                    <span class="font-bold text-lg">v1.0</span>
                                    <p class="text-gray-500 text-sm">2023-07-10 09:30</p>
                                </div>
                                <div class="flex">
                                    <button class="text-blue-600 hover:text-blue-800 mr-2 text-sm" title="选择">
                                        <i class="far fa-check-circle"></i>
                                    </button>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm" title="查看">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-sm">初始版本发布，包含基础支付方式和条件规则</p>
                            <div class="mt-2 text-xs text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-user mr-1"></i>
                                    操作人：张经理
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：版本比较 -->
            <div class="lg:col-span-2">
                <div class="card p-5">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">版本对比</h3>
                    
                    <!-- 版本选择器 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="version-selector-card selected">
                            <h4 class="font-medium mb-2">选择基准版本</h4>
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="text-lg font-bold">v1.5</div>
                                    <div class="text-sm text-gray-500">2023-08-15 14:30</div>
                                    <div class="text-sm mt-1">当前版本</div>
                                </div>
                                <div class="bg-blue-100 rounded-full p-2 text-blue-600">
                                    <i class="fas fa-check text-lg"></i>
                                </div>
                            </div>
                        </div>
                        
                        <div class="version-selector-card">
                            <h4 class="font-medium mb-2">选择比较版本</h4>
                            <div class="flex justify-between items-center">
                                <div>
                                    <div class="text-lg font-bold">v1.3</div>
                                    <div class="text-sm text-gray-500">2023-08-05 16:20</div>
                                    <div class="text-sm mt-1">王产品</div>
                                </div>
                                <div class="bg-gray-100 rounded-full p-2 text-gray-400">
                                    <i class="fas fa-check text-lg"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 差异对比 -->
                    <div>
                        <h4 class="text-md font-medium mb-3">差异详情</h4>
                        
                        <!-- 变更分类 -->
                        <div class="flex mb-4 border-b">
                            <button class="px-4 py-2 text-sm font-medium text-primary border-b-2 border-primary">
                                所有变更(4)
                            </button>
                            <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                                条件变更(2)
                            </button>
                            <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                                支付方式变更(1)
                            </button>
                            <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
                                文案变更(1)
                            </button>
                        </div>
                        
                        <!-- 差异列表 -->
                        <div class="space-y-4">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h5 class="font-medium">新增节假日特殊条件规则</h5>
                                        <p class="text-sm text-gray-500 mt-1">节假日期间支持花呗分期免息</p>
                                    </div>
                                    <span class="tag tag-blue">条件变更</span>
                                </div>
                                <div class="mt-3 flex">
                                    <div class="w-1/2 pr-3 border-r border-dashed">
                                        <div class="text-sm font-medium text-gray-600">v1.3</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            <span class="text-red-500">无此条件规则</span>
                                        </div>
                                    </div>
                                    <div class="w-1/2 pl-3">
                                        <div class="text-sm font-medium text-gray-600">v1.5 (当前)</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            <span class="text-green-500">条件：</span> 环境.时间类型 = 节假日 <br/>
                                            <span class="text-green-500">操作：</span> 支持花呗分期免息
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h5 class="font-medium">支付方式排序变更</h5>
                                        <p class="text-sm text-gray-500 mt-1">调整了支付方式的优先级和排序</p>
                                    </div>
                                    <span class="tag tag-purple">支付方式变更</span>
                                </div>
                                <div class="mt-3 flex">
                                    <div class="w-1/2 pr-3 border-r border-dashed">
                                        <div class="text-sm font-medium text-gray-600">v1.3</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            1. 支付宝<br/>
                                            2. 微信支付<br/>
                                            3. 银联<br/>
                                            4. 花呗分期
                                        </div>
                                    </div>
                                    <div class="w-1/2 pl-3">
                                        <div class="text-sm font-medium text-gray-600">v1.5 (当前)</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            <span class="text-green-500">1. 微信支付</span><br/>
                                            2. 支付宝<br/>
                                            3. 银联<br/>
                                            4. 花呗分期
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h5 class="font-medium">条件规则优化</h5>
                                        <p class="text-sm text-gray-500 mt-1">优化了会员条件规则的判断逻辑</p>
                                    </div>
                                    <span class="tag tag-blue">条件变更</span>
                                </div>
                                <div class="mt-3 flex">
                                    <div class="w-1/2 pr-3 border-r border-dashed">
                                        <div class="text-sm font-medium text-gray-600">v1.3</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            <span class="text-red-500">条件：</span> 用户.会员等级 > 3<br/>
                                            <span class="text-red-500">操作：</span> 支持花呗分期
                                        </div>
                                    </div>
                                    <div class="w-1/2 pl-3">
                                        <div class="text-sm font-medium text-gray-600">v1.5 (当前)</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            <span class="text-green-500">条件：</span> 用户.会员等级 >= 3<br/>
                                            <span class="text-green-500">操作：</span> 支持花呗分期
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h5 class="font-medium">支付结果文案优化</h5>
                                        <p class="text-sm text-gray-500 mt-1">调整了支付结果页的文案显示</p>
                                    </div>
                                    <span class="tag tag-yellow">文案变更</span>
                                </div>
                                <div class="mt-3 flex">
                                    <div class="w-1/2 pr-3 border-r border-dashed">
                                        <div class="text-sm font-medium text-gray-600">v1.3</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            支付成功：您已完成支付，感谢您的购买！
                                        </div>
                                    </div>
                                    <div class="w-1/2 pl-3">
                                        <div class="text-sm font-medium text-gray-600">v1.5 (当前)</div>
                                        <div class="text-sm bg-gray-50 p-2 rounded mt-1">
                                            支付成功：恭喜您，支付已完成！您的订单已生成，感谢您的购买！
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 侧边栏子菜单折叠展开
        document.querySelectorAll('.menu-toggle').forEach(item => {
            item.addEventListener('click', function() {
                const submenu = this.nextElementSibling;
                const icon = this.querySelector('.submenu-icon');
                
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单
        document.getElementById('userDropdown').addEventListener('click', function(e) {
            e.stopPropagation();
            document.getElementById('userMenu').classList.toggle('hidden');
        });

        // 点击页面其他位置关闭用户菜单
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('userDropdown');
            const userMenu = document.getElementById('userMenu');
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // 版本选择卡片点击效果
        document.querySelectorAll('.version-selector-card').forEach(card => {
            card.addEventListener('click', function() {
                // 如果是基准版本卡片，不允许取消选择
                if (this.classList.contains('selected') && this.querySelector('.text-sm')?.textContent.includes('当前版本')) {
                    return;
                }
                
                // 如果是比较版本卡片，允许选择或取消选择
                if (this === document.querySelectorAll('.version-selector-card')[1]) {
                    this.classList.toggle('selected');
                    const icon = this.querySelector('.bg-gray-100');
                    if (this.classList.contains('selected')) {
                        icon.classList.remove('bg-gray-100', 'text-gray-400');
                        icon.classList.add('bg-blue-100', 'text-blue-600');
                    } else {
                        icon.classList.remove('bg-blue-100', 'text-blue-600');
                        icon.classList.add('bg-gray-100', 'text-gray-400');
                    }
                }
            });
        });
    </script>
</body>
</html> 
