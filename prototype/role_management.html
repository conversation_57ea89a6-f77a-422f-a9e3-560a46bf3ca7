<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - 收银台及渠道管理系统</title>
    <!-- 使用国内CDN资源 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/6.0.0/css/all.min.css">
    <link href="https://lf26-cdn-tos.bytecdntp.com/cdn/expire-1-M/material-design-icons/3.0.1/iconfont/material-icons.min.css" rel="stylesheet">
    <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 如果需要，可以添加其他库如 Select2 -->
     <style>
        /* 全局样式 */
        :root {
            --primary-color: #3b7cfe;
            --primary-dark: #2a5cb9;
            --primary-light: #e8f0ff;
            --secondary-color: #6c5ce7;
            --success-color: #00b894;
            --info-color: #0984e3;
            --warning-color: #fdcb6e;
            --danger-color: #e17055;
            --dark-color: #2d3436;
            --light-color: #f8f9fa;
            --border-color: #dfe6e9;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        /* 基础风格 */
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f6f8fb;
            color: #333;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            transition: all 0.3s;
            background: #fff;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            z-index: 20;
        }

        /* 内容区样式 */
        .content {
            margin-left: 260px;
            transition: all 0.3s;
        }

        /* 头部样式 */
        .header {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        /* 导航项样式 */
        .nav-item {
            margin-bottom: 0.5rem;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        
        .nav-item:hover {
            background-color: var(--primary-light);
        }
        
        .nav-item.active {
             background-color: var(--primary-light);
             color: var(--primary-color);
             border-right: 3px solid var(--primary-color);
        }

        /* 子菜单样式 */
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .submenu.active {
            max-height: 500px; 
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
        }

        /* 表格样式 */
        .table-container {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .table-header {
            background: linear-gradient(to right, #f6f8fb, #eef2f7);
            color: #64748b; 
            font-weight: 500; 
        }

         /* 美化按钮 */
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 124, 254, 0.3);
        }

         .btn-light {
            background-color: #f1f5f9;
            color: #64748b;
        }
        
        .btn-light:hover {
            background-color: #e2e8f0;
        }
        
        .btn-outline-primary {
            border: 1px solid var(--primary-color);
            color: var(--primary-color);
            background-color: transparent;
        }
        .btn-outline-primary:hover {
             background-color: var(--primary-light);
        }
        
        .btn-outline-danger {
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
            background-color: transparent;
        }
        .btn-outline-danger:hover {
             background-color: rgba(225, 112, 85, 0.1);
        }
         .btn-outline-info {
             border: 1px solid var(--info-color);
            color: var(--info-color);
            background-color: transparent;
         }
         .btn-outline-info:hover {
             background-color: rgba(9, 132, 227, 0.1);
         }
        
        /* 分页样式 */
        .pagination a, .pagination span {
            display: inline-block;
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border-radius: 0.375rem;
            color: #64748b;
            transition: all 0.2s;
        }
        .pagination a:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        .pagination .active {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 5px rgba(59, 124, 254, 0.3);
        }
        .pagination .disabled {
            color: #cbd5e1;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header fixed top-0 left-0 right-0 z-30 flex justify-between items-center px-6 h-16">
        <div class="flex items-center space-x-4">
            <img src="https://img.yzcdn.cn/vant/logo.png" alt="Logo" class="h-8 w-8">
            <h1 class="text-lg font-bold">收银台及渠道管理系统</h1>
        </div>
        <div class="flex items-center space-x-5">
            <button class="p-2 rounded-full hover:bg-white hover:bg-opacity-20 relative">
                <i class="fas fa-bell text-white"></i>
                <span class="absolute top-0 right-0 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
            </button>
            <div class="relative">
                <button id="userDropdown" class="flex items-center space-x-2 focus:outline-none">
                    <img src="https://imgeweb.oss-cn-shenzhen.aliyuncs.com/avatar.jpg" alt="User" class="rounded-full h-8 w-8 object-cover border-2 border-white">
                    <span class="text-white">管理员</span>
                    <i class="fas fa-chevron-down text-white text-xs"></i>
                </button>
                <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg hidden z-40">
                    <div class="py-1">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">个人信息</a>
                        <a href="system_setting.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md mx-1">系统设置</a>
                        <div class="my-1 border-t border-gray-200"></div>
                        <a href="login.html" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md mx-1">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边栏 -->
    <aside class="sidebar fixed left-0 top-16 bottom-0 overflow-y-auto">
        <nav class="mt-6 px-4">
            <ul>
                <!-- 仪表盘 -->
                <li class="nav-item">
                    <a href="dashboard.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-tachometer-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">仪表盘</span>
                    </a>
                </li>

                <!-- 数据字典管理 -->
                <li class="nav-item">
                    <a href="dict_management.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-book w-5 h-5 text-center"></i>
                        <span class="ml-3">数据字典管理</span>
                    </a>
                </li>

                <!-- 支付场景管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-sitemap w-5 h-5 text-center"></i>
                            <span class="ml-3">支付场景管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="payment_scene.html" class="block py-2 text-gray-600 hover:text-primary">
                                场景列表
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="payment_scene_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本历史
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 条件筛选管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-filter w-5 h-5 text-center"></i>
                            <span class="ml-3">条件筛选管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="condition_module.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件模块
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="condition_rule.html" class="block py-2 text-gray-600 hover:text-primary">
                                条件规则
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 支付方式管理 -->
                <li class="nav-item">
                    <a href="payment_method_list.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-credit-card w-5 h-5 text-center"></i>
                        <span class="ml-3">支付方式管理</span>
                    </a>
                </li>

                <!-- 渠道路由管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-random w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道路由管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_route_rules.html" class="block py-2 text-gray-600 hover:text-primary">
                                路由规则
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_route_version.html" class="block py-2 text-gray-600 hover:text-primary">
                                版本管理
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- Feature Flags -->
                <li class="nav-item">
                     <a href="feature_flags.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                         <i class="fas fa-toggle-on w-5 h-5 text-center"></i>
                         <span class="ml-3">功能开关</span>
                     </a>
                 </li>

                <!-- 商户号管理 -->
                <li class="nav-item">
                    <a href="merchant.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-store w-5 h-5 text-center"></i>
                        <span class="ml-3">商户号管理</span>
                    </a>
                </li>

                <!-- 应用管理 -->
                <li class="nav-item">
                    <a href="application.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-mobile-alt w-5 h-5 text-center"></i>
                        <span class="ml-3">应用管理</span>
                    </a>
                </li>
                
                <!-- 渠道账号管理 -->
                <li class="nav-item">
                    <div class="menu-toggle flex items-center justify-between px-4 py-3 text-gray-700 hover:text-primary cursor-pointer rounded-lg">
                        <div class="flex items-center">
                             <i class="fas fa-users-cog w-5 h-5 text-center"></i>
                            <span class="ml-3">渠道账号管理</span>
                        </div>
                        <i class="submenu-icon fas fa-chevron-down text-xs transition-transform duration-300"></i>
                    </div>
                    <ul class="submenu pl-12 pr-4">
                        <li class="my-2">
                            <a href="channel_account.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道账号
                            </a>
                        </li>
                        <li class="my-2">
                            <a href="channel_employee.html" class="block py-2 text-gray-600 hover:text-primary">
                                渠道员工
                            </a>
                        </li>
                    </ul>
                </li>

                <!-- 操作日志 -->
                <li class="nav-item">
                    <a href="operation_log.html" class="flex items-center px-4 py-3 text-gray-700 hover:text-primary rounded-lg">
                        <i class="fas fa-history w-5 h-5 text-center"></i>
                        <span class="ml-3">操作日志</span>
                    </a>
                </li>

            </ul>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <main class="content pt-20 pb-8 px-8">
        <!-- 面包屑导航 -->
        <div class="mb-6 flex items-center text-sm text-gray-500">
            <a href="dashboard.html" class="hover:text-primary">仪表盘</a>
            <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-500">系统管理</span>
             <i class="fas fa-angle-right mx-2"></i>
            <span class="text-gray-800">角色管理</span>
        </div>

        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">角色管理</h2>
            <button class="btn btn-primary" onclick="openCreateRoleModal()"> <!-- 假设有模态框或跳转 -->
                 <i class="fas fa-plus mr-2"></i>创建角色
            </button>
        </div>

        <!-- 角色列表 -->
        <div class="card">
            <div class="table-container overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="table-header">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">角色ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">角色名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">角色标识 (Key)</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">描述</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">创建时间</th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 示例数据行 1 (超级管理员) -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ROLE001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">超级管理员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">super_admin</td>
                            <td class="px-6 py-4 text-sm text-gray-500">拥有系统所有权限</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-01 08:00:00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                <button class="btn btn-outline-primary btn-sm py-1 px-2" disabled title="系统内置角色，不可编辑">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                                <button class="btn btn-outline-info btn-sm py-1 px-2" onclick="openPermissionModal('ROLE001')">
                                    <i class="fas fa-lock mr-1"></i>权限配置
                                </button>
                                <button class="btn btn-outline-danger btn-sm py-1 px-2" disabled title="系统内置角色，不可删除">
                                    <i class="fas fa-trash-alt mr-1"></i>删除
                                </button>
                            </td>
                        </tr>
                        <!-- 示例数据行 2 (运营人员) -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ROLE002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">运营人员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">operator</td>
                            <td class="px-6 py-4 text-sm text-gray-500">负责日常运营操作，如管理支付场景、渠道等</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-10 11:00:00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                <button class="btn btn-outline-primary btn-sm py-1 px-2" onclick="openEditRoleModal('ROLE002')">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                                <button class="btn btn-outline-info btn-sm py-1 px-2" onclick="openPermissionModal('ROLE002')">
                                    <i class="fas fa-lock mr-1"></i>权限配置
                                </button>
                                <button class="btn btn-outline-danger btn-sm py-1 px-2" onclick="confirmDeleteRole('ROLE002', '运营人员')">
                                    <i class="fas fa-trash-alt mr-1"></i>删除
                                </button>
                            </td>
                        </tr>
                        <!-- 示例数据行 3 (观察员) -->
                         <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ROLE003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">观察员</td>
                             <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">viewer</td>
                             <td class="px-6 py-4 text-sm text-gray-500">只能查看系统数据，无操作权限</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-15 09:00:00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                <button class="btn btn-outline-primary btn-sm py-1 px-2" onclick="openEditRoleModal('ROLE003')">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                                <button class="btn btn-outline-info btn-sm py-1 px-2" onclick="openPermissionModal('ROLE003')">
                                    <i class="fas fa-lock mr-1"></i>权限配置
                                </button>
                                <button class="btn btn-outline-danger btn-sm py-1 px-2" onclick="confirmDeleteRole('ROLE003', '观察员')">
                                    <i class="fas fa-trash-alt mr-1"></i>删除
                                </button>
                            </td>
                        </tr>
                        <!-- 更多数据行... -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                </div>
                <nav class="pagination" aria-label="Pagination">
                    <span class="disabled">&laquo;</span>
                    <span class="active">1</span>
                    <!-- <a href="#">2</a> -->
                    <span class="disabled">&raquo;</span>
                </nav>
            </div>
        </div>
    </main>

    <!-- 使用国内CDN的jQuery -->
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>

    <script>
        // 模拟打开创建/编辑/权限配置模态框或跳转
        function openCreateRoleModal(){
             alert('打开创建角色界面（模拟）');
             // window.location.href = 'role_edit.html'; // 或者跳转到编辑页面
        }
        function openEditRoleModal(roleId){
             alert(`打开编辑角色 ${roleId} 界面（模拟）`);
             // window.location.href = `role_edit.html?id=${roleId}`; // 或者跳转到编辑页面
        }
         function openPermissionModal(roleId){
             alert(`打开角色 ${roleId} 的权限配置界面（模拟）`);
             // window.location.href = `role_permission.html?id=${roleId}`; // 或者跳转到权限配置页面
        }
        
         // 确认删除角色
        function confirmDeleteRole(roleId, roleName) {
            if (confirm(`确定要删除角色 "${roleName}" (ID: ${roleId}) 吗？删除后，关联此角色的用户将失去相应权限。`)) {
                // 在实际应用中，这里会调用后端API来删除角色
                alert(`角色 "${roleName}" 已删除（模拟操作）。`);
                // 可能需要刷新页面或移除表格行
            }
        }

        // 侧边栏子菜单折叠展开 (与之前页面相同逻辑)
        document.querySelectorAll('.menu-toggle').forEach(item => {
            const submenu = item.nextElementSibling;
            const icon = item.querySelector('.submenu-icon');
            if(!submenu || !icon) return;
             let isParentActive = item.closest('.nav-item').classList.contains('active');
             let hasActiveChild = false;
            submenu.querySelectorAll('a').forEach(link => {
                if (link.classList.contains('font-semibold')) {
                     hasActiveChild = true;
                }
            });
            if (isParentActive || hasActiveChild) {
                 submenu.classList.add('active');
                 icon.style.transform = 'rotate(180deg)';
            } else {
                 submenu.classList.remove('active');
                 icon.style.transform = 'rotate(0deg)';
            }
             item.addEventListener('click', function() {
                if (submenu.classList.contains('active')) {
                    submenu.classList.remove('active');
                    icon.style.transform = 'rotate(0deg)';
                } else {
                    document.querySelectorAll('.submenu.active').forEach(openSubmenu => {
                        if (openSubmenu !== submenu) {
                            openSubmenu.classList.remove('active');
                            const otherIcon = openSubmenu.previousElementSibling.querySelector('.submenu-icon');
                            if(otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                        }
                    });
                    submenu.classList.add('active');
                    icon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // 用户下拉菜单 (与之前页面相同逻辑)
        const userDropdown = document.getElementById('userDropdown');
        const userMenu = document.getElementById('userMenu');
        if (userDropdown) {
            userDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });
        }
        document.addEventListener('click', function(event) {
            if (userDropdown && userMenu && !userDropdown.contains(event.target) && !userMenu.contains(event.target)) {
                userMenu.classList.add('hidden');
            }
        });

       // 动态设置当前导航项的激活状态 (适配当前页面)
        const currentPath = window.location.pathname.split('/').pop();
        document.querySelectorAll('.nav-item.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu.active').forEach(item => item.classList.remove('active'));
        document.querySelectorAll('.submenu a.font-semibold.text-primary').forEach(item => item.classList.remove('font-semibold', 'text-primary'));
        document.querySelectorAll('.submenu-icon').forEach(icon => icon.style.transform = 'rotate(0deg)');
        document.querySelectorAll('.nav-item').forEach(navItem => {
            const link = navItem.querySelector('a');
            const submenuLinks = navItem.querySelectorAll('.submenu a');
            let isCurrentParent = false;
            let isCurrentPage = false;
            if (link && link.getAttribute('href').split('/').pop() === currentPath) {
                 isCurrentPage = true;
            } 
            else if (submenuLinks.length > 0) {
                submenuLinks.forEach(subLink => {
                    if (subLink.getAttribute('href').split('/').pop() === currentPath) {
                        isCurrentParent = true;
                        subLink.classList.add('font-semibold', 'text-primary');
                    }
                });
            }
            
             // 检查是否当前编辑/详情页对应的列表页父菜单 (系统管理下的页面)
             const parentMap = {
                 'system_setting.html': 'system_setting.html',
                 'user_management.html': 'system_setting.html', 
                 'role_management.html': 'system_setting.html'
             };
             const parentIndicator = parentMap[currentPath];
             if (parentIndicator) {
                 let parentNavItem = null;
                 // 找到系统管理父菜单项
                 document.querySelectorAll('.nav-item .submenu').forEach(submenu => {
                    if(submenu.querySelector('a[href="system_setting.html"]')){
                        parentNavItem = submenu.closest('.nav-item');
                    }
                 });
                 
                 if(parentNavItem && parentNavItem === navItem) {
                    isCurrentParent = true; // 标记当前navItem是父菜单
                    // 高亮当前页面的子菜单链接
                    submenuLinks.forEach(subLink => {
                        if(subLink.getAttribute('href').split('/').pop() === currentPath){
                             subLink.classList.add('font-semibold', 'text-primary');
                        }
                    });
                 }
            }
            
            if (isCurrentPage || isCurrentParent) {
                 navItem.classList.add('active');
                const submenu = navItem.querySelector('.submenu');
                const icon = navItem.querySelector('.submenu-icon');
                if (submenu && isCurrentParent) {
                    submenu.classList.add('active');
                    if(icon) icon.style.transform = 'rotate(180deg)';
                }
            }
        });
        
         // 示例：表格行悬停效果 (可选)
        document.querySelectorAll('tbody tr').forEach(row => {
            row.addEventListener('mouseover', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseout', function() {
                this.classList.remove('bg-gray-50');
            });
        });
    </script>
</body>
</html> 
