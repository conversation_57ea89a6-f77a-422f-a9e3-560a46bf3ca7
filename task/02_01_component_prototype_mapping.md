# 组件与原型文件映射索引

本文档为组件开发提供快速参考，帮助开发人员在实现组件时能够迅速找到对应的原型文件，提高开发效率。

## 1. 页面布局组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| Layout.vue | layout.html, layout_template.html | 全局页面布局框架 |
| Header.vue | layout.html, layout_template.html | 顶部导航栏 |
| Sidebar.vue | layout.html, layout_template.html | 左侧菜单栏 |
| ContentLayout.vue | layout.html, layout_template.html | 内容区布局 |
| Footer.vue | layout.html, layout_template.html | 页脚布局 |
| ComplexContentLayout.vue | payment_scene_edit.html, payment_scene_edit_new.html, condition_module_edit.html | 复杂内容区布局(多区域) |
| CardGroupLayout.vue | 几乎所有页面 | 卡片式分组布局 |
| CollapsiblePanel.vue | payment_scene_edit.html, channel_route_rule_edit.html, condition_module_edit.html | 可折叠面板 |
| SplitScreenLayout.vue | condition_relation_designs.html, payment_scene_edit.html | 分屏布局 |

## 2. 通用UI组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| Button.vue | 所有页面 | 按钮组件 |
| Input.vue | 所有表单页面 | 输入框组件 |
| Select.vue | 所有表单页面 | 下拉选择组件 |
| Checkbox.vue | 多个列表页面 | 复选框组件 |
| Table.vue | 所有列表页面 | 表格组件 |
| Card.vue | 所有页面 | 卡片容器组件 |
| List.vue | dashboard.html, operation_log.html | 列表组件 |
| Modal.vue | 所有页面 | 弹窗组件 |
| Alert.vue | 多个页面 | 提示组件 |
| Notification.vue | 操作成功/失败提示 | 通知组件 |
| Tabs.vue | payment_method_detail.html, merchant_detail.html | 标签页组件 |
| Breadcrumb.vue | 所有页面 | 面包屑导航 |
| Pagination.vue | 所有列表页面 | 分页组件 |
| SearchFilter.vue | 所有列表页面 | 搜索过滤区组件 |
| StatusTag.vue | 所有列表页面 | 状态标签组件 |
| ActionButtons.vue | 所有列表页面 | 操作按钮组 |
| Tooltip.vue | 多个页面 | 工具提示组件 |
| EnhancedTable.vue | 复杂列表页面 | 增强表格组件 |

## 3. 业务组件

### 支付相关组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| PaymentCard.vue | payment_method_list.html | 支付方式卡片 |
| MethodSelector.vue | payment_scene_edit.html, payment_scene_edit_new.html | 支付方式选择器 |
| PaymentSceneForm.vue | payment_scene_edit.html, payment_scene_edit_new.html | 支付场景表单 |
| PaymentMethodDetail.vue | payment_method_detail.html | 支付方式详情 |
| PaymentScenePreview.vue | payment_scene_preview.html | 支付场景预览 |
| PaymentTrendChart.vue | payment_method_trend.html | 支付趋势图表 |
| PaymentFilterConfig.vue | payment_method_filter.html | 支付过滤配置 |

### 商户相关组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| MerchantInfo.vue | merchant_detail.html | 商户信息展示 |
| AccountStatus.vue | merchant.html, merchant_detail.html | 账户状态展示 |
| MerchantForm.vue | merchant_edit.html | 商户编辑表单 |
| ApplicationList.vue | application.html | 应用列表 |
| ApplicationForm.vue | application_edit.html | 应用编辑表单 |
| ApplicationDetail.vue | application_detail.html | 应用详情 |

### 渠道相关组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| ChannelCard.vue | channel_account.html | 渠道账户卡片 |
| RouteRule.vue | channel_route_rules.html, channel_route_rule_edit.html | 路由规则组件 |
| ChannelAccountForm.vue | channel_account_edit.html | 渠道账户表单 |
| ChannelEmployeeList.vue | channel_employee.html | 渠道员工列表 |
| ChannelEmployeeForm.vue | channel_employee_edit.html | 渠道员工表单 |
| ChannelAccountDetail.vue | channel_account_detail.html | 渠道账户详情 |

### 条件相关组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| ConditionBuilder.vue | condition_module_edit.html, condition_rule_edit.html | 条件构建器 |
| RuleEditor.vue | condition_rule_edit.html, channel_route_rule_edit.html | 规则编辑器 |
| ConditionModuleList.vue | condition_module.html | 条件模块列表 |
| ConditionRuleList.vue | condition_rule.html | 条件规则列表 |
| ConditionRelationDesigner.vue | condition_relation_designs.html | 条件关系设计器 |
| ConditionModuleView.vue | condition_module_view.html | 条件模块查看 |

### 系统管理组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| RoleSelector.vue | role_management.html | 角色选择器 |
| LogViewer.vue | operation_log.html | 日志查看器 |
| DictManager.vue | dict_management.html | 字典管理 |
| DictCategoryList.vue | dict_category.html | 字典分类列表 |
| DictItemList.vue | dict_item.html | 字典项列表 |
| PermissionConfig.vue | role_management.html | 权限配置 |
| DictSelector.vue | 多个编辑页面 | 字典选择器 |
| FeatureToggle.vue | feature_flags.html | 功能开关组件 |
| DashboardStats.vue | dashboard.html | 仪表盘统计 |

## 4. 高级交互组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| DragSortable.vue | condition_module_edit.html, channel_route_rule_edit.html | 拖拽排序组件 |
| FlowDesigner.vue | condition_relation_designs.html | 流程设计器 |
| JsonEditor.vue | 多个高级配置页面 | JSON编辑器 |
| CodeEditor.vue | 多个高级配置页面 | 代码编辑器 |
| StepWizard.vue | payment_scene_edit_new.html | 步骤向导组件 |
| TreeView.vue | feature_flags.html | 树形视图组件 |

## 5. 表单组件

| 组件名称 | 原型文件 | 说明 |
|---------|---------|------|
| AdvancedSearchForm.vue | 所有列表页面 | 高级搜索表单 |
| DynamicForm.vue | payment_scene_edit.html, condition_module_edit.html | 动态表单生成器 |
| StepForm.vue | payment_scene_edit_new.html | 分步表单 |
| CascadeFormItem.vue | 多个编辑页面 | 级联表单项 |
| ValidatedFormGroup.vue | 所有表单页面 | 验证表单组 |
| FormLayout.vue | 所有编辑页面 | 表单布局 |

## 使用指南

1. 在开发组件前，先查找对应的原型文件参考
2. 分析原型中的HTML结构、CSS样式和JavaScript交互
3. 将分析结果转换为Vue组件结构
4. 注意保持视觉风格和交互体验的一致性 