# 路由与状态管理

## 角色与目标

作为前端架构师，你需要设计和实现Vue项目的路由系统和状态管理架构，确保应用的导航流畅，数据流清晰，并为大型应用提供可扩展的数据管理方案。

## 工作内容

### 1. 设计路由系统

- **路由架构规划**
  - 分析原型中的页面导航关系
  - 设计主路由和嵌套路由结构
  - 规划路由参数传递方式
  - 设计动态路由加载策略

- **路由配置实现**
  - 创建基础路由配置文件
  ```js
  // router/index.js 示例
  import { createRouter, createWebHistory } from 'vue-router'
  import Layout from '@/layout/index.vue'

  const routes = [
    {
      path: '/',
      component: Layout,
      redirect: '/dashboard',
      children: [
        {
          path: 'dashboard',
          component: () => import('@/views/dashboard/index.vue'),
          name: 'Dashboard',
          meta: { title: '仪表盘', icon: 'dashboard', affix: true }
        }
      ]
    },
    {
      path: '/payment',
      component: Layout,
      redirect: '/payment/method/list',
      name: 'Payment',
      meta: { title: '支付管理', icon: 'credit-card' },
      children: [
        {
          path: 'method/list',
          component: () => import('@/views/payment/method/list.vue'),
          name: 'PaymentMethodList',
          meta: { title: '支付方式列表' }
        },
        {
          path: 'method/detail/:id',
          component: () => import('@/views/payment/method/detail.vue'),
          name: 'PaymentMethodDetail',
          meta: { title: '支付方式详情', activeMenu: '/payment/method/list' },
          hidden: true
        },
        // 其他支付相关路由...
      ]
    },
    // 更多路由配置...
  ]

  const router = createRouter({
    history: createWebHistory(),
    routes
  })

  export default router
  ```

- **路由守卫实现**
  - 配置全局前置守卫(beforeEach)
  - 配置全局解析守卫(beforeResolve)
  - 配置全局后置钩子(afterEach)
  - 实现权限控制路由守卫

- **路由功能增强**
  - 实现面包屑导航生成
  - 设计标签页导航功能
  - 添加路由切换动画
  - 实现页面权限缓存机制

### 2. 实现状态管理

- **状态模块划分**
  - 设计用户相关状态模块
  - 设计应用配置状态模块
  - 设计业务数据状态模块
  - 设计UI状态模块

- **核心状态模块实现**
  - 实现用户状态模块(user.js)
  ```js
  // stores/user.js 示例
  import { defineStore } from 'pinia'
  import { login, logout, getUserInfo } from '@/api/user'
  import { getToken, setToken, removeToken } from '@/utils/auth'

  export const useUserStore = defineStore('user', {
    state: () => ({
      token: getToken(),
      name: '',
      avatar: '',
      roles: [],
      permissions: []
    }),
    getters: {
      hasRole: (state) => (role) => state.roles.includes(role),
      hasPermission: (state) => (permission) => state.permissions.includes(permission)
    },
    actions: {
      async login(userInfo) {
        const { username, password } = userInfo
        try {
          const { data } = await login(username.trim(), password)
          setToken(data.token)
          this.token = data.token
          return data
        } catch (error) {
          console.error('Login failed:', error)
          throw error
        }
      },
      async getUserInfo() {
        try {
          const { data } = await getUserInfo(this.token)
          this.name = data.name
          this.avatar = data.avatar
          this.roles = data.roles
          this.permissions = data.permissions
          return data
        } catch (error) {
          console.error('Get user info failed:', error)
          throw error
        }
      },
      async logout() {
        try {
          await logout(this.token)
          this.resetState()
          return true
        } catch (error) {
          console.error('Logout failed:', error)
          throw error
        }
      },
      resetState() {
        this.token = ''
        this.name = ''
        this.avatar = ''
        this.roles = []
        this.permissions = []
        removeToken()
      }
    }
  })
  ```

- **应用配置状态模块实现**
  - 实现app.js配置模块
  - 添加主题配置功能
  - 实现侧边栏状态管理
  - 添加标签页导航状态管理

- **业务数据状态模块实现**
  - 实现支付相关状态模块(payment.js)
  - 实现商户相关状态模块(merchant.js)
  - 实现渠道相关状态模块(channel.js)
  - 实现系统配置状态模块(system.js)

- **状态持久化实现**
  - 配置Pinia持久化插件
  - 设计持久化策略
  - 实现状态恢复机制
  - 添加敏感数据处理逻辑

### 3. 数据流管理

- **组件间数据流设计**
  - 设计自上而下的Props传递规范
  - 设计自下而上的Events触发规范
  - 设计兄弟组件通信策略
  - 设计组件与状态管理的交互模式

- **数据请求策略**
  - 设计数据加载与缓存策略
  - 实现请求防抖和节流
  - 设计批量请求处理
  - 实现错误重试机制

- **数据变更管理**
  - 设计数据变更跟踪机制
  - 实现撤销/重做功能
  - 添加数据变更提示
  - 设计表单数据变更处理

- **实时数据更新**
  - 设计轮询更新机制
  - 评估WebSocket实时更新可能性
  - 实现数据订阅模式
  - 添加数据更新通知机制

## 交付物

1. **路由系统**
   - 完整的路由配置文件
   - 路由守卫实现
   - 权限控制实现
   - 导航增强功能

2. **状态管理架构**
   - 核心状态模块实现
   - 业务状态模块实现
   - 状态持久化配置
   - 状态与组件交互示例

3. **数据流文档**
   - 数据流架构图
   - 组件数据交互指南
   - 状态管理使用手册
   - 最佳实践文档

## 时间规划

| 任务 | 耗时 | 负责人 |
|------|------|--------|
| 路由架构设计与实现 | 2天 | 前端架构师 |
| 核心状态模块实现 | 2天 | 前端开发 |
| 业务状态模块实现 | 3天 | 前端开发 |
| 数据流管理实现 | 2天 | 前端开发 |

## 检查清单

- [ ] 路由配置完整，包含所有页面
- [ ] 路由守卫正确实现权限控制
- [ ] 导航功能正常工作
- [ ] 核心状态模块功能测试通过
- [ ] 业务状态模块功能测试通过
- [ ] 状态持久化功能正常工作
- [ ] 组件能正确访问和更新状态
- [ ] 数据流文档完整清晰 