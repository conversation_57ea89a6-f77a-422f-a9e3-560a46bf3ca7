# API层实现

## 角色与目标

作为前端开发工程师，你需要设计和实现一套清晰、可维护的API通信层，使Vue组件能够与后端服务高效交互，并处理各种网络请求场景。

## 工作内容

### 1. 设计API模块结构

- **API目录组织**
  - 创建核心API架构
  ```
  src/
  ├── api/
  │   ├── index.js     # 基础配置和实例创建
  │   ├── user.js      # 用户相关API
  │   ├── payment/     # 支付相关API
  │   │   ├── method.js   # 支付方式API
  │   │   └── scene.js    # 支付场景API
  │   ├── merchant.js  # 商户相关API
  │   ├── channel.js   # 渠道相关API
  │   └── system.js    # 系统相关API
  ```

- **创建请求实例**
  - 配置Axios实例
  ```js
  // api/index.js 示例
  import axios from 'axios'
  import { useUserStore } from '@/stores/user'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import router from '@/router'

  // 创建axios实例
  const request = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 15000
  })

  // 请求拦截器
  request.interceptors.request.use(
    config => {
      const userStore = useUserStore()
      if (userStore.token) {
        config.headers['Authorization'] = `Bearer ${userStore.token}`
      }
      return config
    },
    error => {
      console.error('Request error:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  request.interceptors.response.use(
    response => {
      const res = response.data
      
      // 根据业务状态码处理响应
      if (res.code !== 200) {
        ElMessage({
          message: res.message || 'Error',
          type: 'error',
          duration: 5 * 1000
        })

        // 处理特定错误码
        if (res.code === 401) {
          ElMessageBox.confirm(
            '登录状态已过期，您可以继续留在该页面，或者重新登录',
            '系统提示',
            {
              confirmButtonText: '重新登录',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            const userStore = useUserStore()
            userStore.resetState()
            router.push('/login')
          })
        }
        
        return Promise.reject(new Error(res.message || 'Error'))
      } else {
        return res
      }
    },
    error => {
      console.error('Response error:', error)
      
      // 处理HTTP错误
      let message = error.message || '请求失败'
      if (error.response) {
        switch (error.response.status) {
          case 400:
            message = '请求错误'
            break
          case 401:
            message = '未授权，请登录'
            break
          case 403:
            message = '拒绝访问'
            break
          case 404:
            message = `请求地址出错: ${error.response.config.url}`
            break
          case 408:
            message = '请求超时'
            break
          case 500:
            message = '服务器内部错误'
            break
          case 501:
            message = '服务未实现'
            break
          case 502:
            message = '网关错误'
            break
          case 503:
            message = '服务不可用'
            break
          case 504:
            message = '网关超时'
            break
          default:
            message = `未知错误: ${error.response.status}`
        }
      }
      
      ElMessage({
        message: message,
        type: 'error',
        duration: 5 * 1000
      })
      
      return Promise.reject(error)
    }
  )

  export { request }
  ```

- **API错误处理机制**
  - 设计通用错误处理逻辑
  - 实现错误重试策略
  - 设计离线处理机制
  - 实现错误日志收集

- **请求配置管理**
  - 设置环境变量区分API地址
  - 配置请求超时处理
  - 实现请求取消机制
  - 设计全局加载状态管理

### 2. 实现API调用函数

- **用户相关API**
  - 实现登录/注销功能
  ```js
  // api/user.js 示例
  import { request } from './index'

  // 用户登录
  export function login(username, password) {
    return request({
      url: '/user/login',
      method: 'post',
      data: {
        username,
        password
      }
    })
  }

  // 用户登出
  export function logout() {
    return request({
      url: '/user/logout',
      method: 'post'
    })
  }

  // 获取用户信息
  export function getUserInfo() {
    return request({
      url: '/user/info',
      method: 'get'
    })
  }

  // 修改用户密码
  export function changePassword(oldPassword, newPassword) {
    return request({
      url: '/user/password',
      method: 'put',
      data: {
        oldPassword,
        newPassword
      }
    })
  }
  ```

- **支付相关API**
  - 实现支付方式API
  ```js
  // api/payment/method.js 示例
  import { request } from '../index'

  // 获取支付方式列表
  export function getPaymentMethods(params) {
    return request({
      url: '/payment/methods',
      method: 'get',
      params
    })
  }

  // 获取支付方式详情
  export function getPaymentMethodDetail(id) {
    return request({
      url: `/payment/method/${id}`,
      method: 'get'
    })
  }

  // 创建支付方式
  export function createPaymentMethod(data) {
    return request({
      url: '/payment/method',
      method: 'post',
      data
    })
  }

  // 更新支付方式
  export function updatePaymentMethod(id, data) {
    return request({
      url: `/payment/method/${id}`,
      method: 'put',
      data
    })
  }

  // 删除支付方式
  export function deletePaymentMethod(id) {
    return request({
      url: `/payment/method/${id}`,
      method: 'delete'
    })
  }
  ```

  - 实现支付场景API
  - 实现支付数据统计API

- **商户相关API**
  - 实现商户管理API ✅
  - 实现应用管理API ⚠️ (进行中)
  - 实现商户数据API ⚠️ (进行中)

- **渠道相关API**
  - 实现渠道账户API ✅
  - 实现渠道员工API ✅
  - 实现路由规则API ⚠️ (进行中)
  - 实现条件模块API ⚠️ (进行中)

- **系统管理API**
  - 实现角色管理API
  - 实现操作日志API
  - 实现字典管理API ✅
    - 字典类型相关接口 ✅
    - 字典数据相关接口 ✅
  - 实现功能开关API

### 3. API功能增强

- **请求优化实现**
  - 实现请求缓存机制
  ```js
  // utils/requestCache.js 示例
  import LRUCache from 'lru-cache'

  // 配置LRU缓存
  const cache = new LRUCache({
    max: 100, // 最多缓存100个请求结果
    ttl: 1000 * 60 * 5 // 缓存5分钟
  })

  // 缓存请求装饰器
  export function withCache(apiFunc, cacheKey) {
    return async function(...args) {
      // 生成缓存键
      const key = cacheKey ? cacheKey(...args) : `${apiFunc.name}:${JSON.stringify(args)}`
      
      // 检查缓存
      if (cache.has(key)) {
        return Promise.resolve(cache.get(key))
      }
      
      // 执行实际请求
      try {
        const result = await apiFunc(...args)
        // 缓存结果
        cache.set(key, result)
        return result
      } catch (error) {
        throw error
      }
    }
  }

  // 清除缓存
  export function clearCache(pattern) {
    if (!pattern) {
      cache.clear()
      return
    }
    
    const keys = cache.keys()
    for (const key of keys) {
      if (key.includes(pattern)) {
        cache.delete(key)
      }
    }
  }
  ```

  - 实现请求合并和批处理
  - 添加请求节流和防抖功能
  - 实现自动重试机制

- **接口适配器模式**
  - 设计API响应转换层
  - 实现统一数据格式化
  - 添加数据映射功能
  - 设计前后端字段适配

- **请求/响应拦截增强**
  - 添加请求日志系统
  - 实现API性能监控
  - 设计全局错误处理
  - 添加网络状态检测

- **Mock数据系统**
  - 配置Mock服务器
  - 实现API模拟数据
  - 设计模拟数据生成器
  - 实现环境切换机制

## 交付物

1. **API基础架构**
   - 请求实例配置
   - 拦截器实现
   - 错误处理机制
   - 环境配置文件

2. **API模块集合**
   - 用户相关API模块
   - 支付相关API模块
   - 商户相关API模块
   - 渠道相关API模块
   - 系统管理API模块

3. **API增强功能**
   - 请求缓存实现
   - 请求优化功能
   - 接口适配器
   - Mock数据服务

4. **API使用文档**
   - API调用规范
   - 错误处理指南
   - 缓存策略说明
   - 最佳实践示例

## 时间规划

| 任务 | 耗时 | 负责人 |
|------|------|--------|
| API模块结构设计 | 1天 | 前端开发 |
| 核心请求实例实现 | 1天 | 前端开发 |
| API调用函数实现 | 3天 | 前端开发 |
| API功能增强实现 | 2天 | 前端开发 |

## 检查清单

- [ ] 请求实例配置正确
- [ ] 拦截器功能正常工作
- [ ] 错误处理机制完善
- [ ] 各模块API实现完整
  - [x] 字典管理API实现完成
  - [x] 商户管理API部分完成 (60%)
  - [x] 渠道相关API部分完成 (60%)
- [ ] 请求缓存机制有效
- [ ] 请求优化功能测试通过
- [ ] Mock数据服务配置完成
- [ ] API文档齐全清晰 