# 测试与部署

## 角色与目标

作为质量保障工程师和DevOps专家，你需要设计和实施全面的测试策略，并建立高效的持续集成/持续部署流程，确保应用在各环境中稳定可靠地运行。

## 工作内容

### 1. 测试策略

- **单元测试设计**
  - 配置Vue组件测试环境
  ```js
  // vitest.config.js
  import { defineConfig } from 'vitest/config'
  import vue from '@vitejs/plugin-vue'

  export default defineConfig({
    plugins: [vue()],
    test: {
      environment: 'jsdom',
      deps: {
        inline: ['element-plus']
      },
      globals: true,
      coverage: {
        reporter: ['text', 'json', 'html']
      }
    }
  })
  ```

  - 实现核心组件测试
  ```js
  // tests/components/Button.spec.js
  import { mount } from '@vue/test-utils'
  import { describe, it, expect } from 'vitest'
  import Button from '@/components/common/Button.vue'

  describe('Button Component', () => {
    it('renders correctly with default props', () => {
      const wrapper = mount(Button, {
        props: {
          type: 'primary'
        },
        slots: {
          default: 'Test Button'
        }
      })
      
      expect(wrapper.classes()).toContain('btn-primary')
      expect(wrapper.text()).toBe('Test Button')
    })
    
    it('emits click event when clicked', async () => {
      const wrapper = mount(Button)
      
      await wrapper.trigger('click')
      
      expect(wrapper.emitted()).toHaveProperty('click')
    })
    
    it('does not emit click event when disabled', async () => {
      const wrapper = mount(Button, {
        props: {
          disabled: true
        }
      })
      
      await wrapper.trigger('click')
      
      expect(wrapper.emitted()).not.toHaveProperty('click')
    })
  })
  ```

  - 设计Vuex/Pinia状态测试
  - 实现工具函数测试

- **集成测试设计**
  - 设计页面组件集成测试
  ```js
  // tests/views/PaymentMethodList.spec.js
  import { mount } from '@vue/test-utils'
  import { createTestingPinia } from '@pinia/testing'
  import { describe, it, expect, vi, beforeEach } from 'vitest'
  import PaymentMethodList from '@/views/payment/method/list.vue'
  import { usePaymentStore } from '@/stores/payment'
  import { ElTable, ElPagination } from 'element-plus'

  describe('PaymentMethodList Component', () => {
    let wrapper
    let paymentStore
    
    beforeEach(() => {
      // 创建模拟Pinia状态
      wrapper = mount(PaymentMethodList, {
        global: {
          plugins: [
            createTestingPinia({
              createSpy: vi.fn,
              initialState: {
                payment: {
                  paymentMethods: [
                    { id: 1, name: '支付宝', status: 'active' },
                    { id: 2, name: '微信支付', status: 'active' }
                  ],
                  total: 2
                }
              }
            })
          ],
          stubs: {
            'el-table': ElTable,
            'el-pagination': ElPagination
          }
        }
      })
      
      paymentStore = usePaymentStore()
    })
    
    it('renders data table with correct items', () => {
      expect(wrapper.findAll('el-table-column')).toHaveLength(5) // 假设有5列
      expect(paymentStore.paymentMethods).toHaveLength(2)
    })
    
    it('loads payment methods on component mount', () => {
      expect(paymentStore.fetchPaymentMethods).toHaveBeenCalledTimes(1)
    })
    
    it('updates pagination when page changes', async () => {
      const pagination = wrapper.findComponent(ElPagination)
      await pagination.vm.$emit('current-change', 2)
      
      expect(paymentStore.fetchPaymentMethods).toHaveBeenCalledWith(
        expect.objectContaining({ page: 2 })
      )
    })
  })
  ```

  - 实现路由导航测试
  - 设计API交互测试
  - 配置组件通信测试

- **端到端测试设计**
  - 配置Cypress测试环境
  ```js
  // cypress.config.js
  import { defineConfig } from 'cypress'

  export default defineConfig({
    e2e: {
      baseUrl: 'http://localhost:5173',
      setupNodeEvents(on, config) {
        return config
      },
      viewportWidth: 1280,
      viewportHeight: 800,
      video: false
    }
  })
  ```

  - 实现核心用户流程测试
  ```js
  // cypress/e2e/login.spec.js
  describe('Login Flow', () => {
    beforeEach(() => {
      cy.visit('/login')
    })

    it('displays login form', () => {
      cy.get('input[name="username"]').should('be.visible')
      cy.get('input[name="password"]').should('be.visible')
      cy.get('button[type="submit"]').should('be.visible')
    })

    it('shows error with invalid credentials', () => {
      cy.get('input[name="username"]').type('invaliduser')
      cy.get('input[name="password"]').type('wrongpassword')
      cy.get('button[type="submit"]').click()

      cy.get('.error-message').should('be.visible')
        .and('contain', '用户名或密码错误')
    })

    it('navigates to dashboard on successful login', () => {
      // 使用测试账号
      cy.get('input[name="username"]').type('testuser')
      cy.get('input[name="password"]').type('password123')
      cy.get('button[type="submit"]').click()

      // 应该重定向到仪表盘
      cy.url().should('include', '/dashboard')
      cy.get('.welcome-message').should('contain', 'testuser')
    })
  })
  ```

  - 设计关键功能测试
  - 实现多环境测试
  - 配置视觉回归测试

- **性能与负载测试**
  - 设置Lighthouse性能测试
  - 配置网络性能监测
  - 实现组件渲染性能测试
  - 设计大数据加载测试

### 2. 部署准备

- **环境配置管理**
  - 设计环境变量策略
  ```js
  // .env.development
  VITE_API_BASE_URL=http://localhost:3000/api
  VITE_APP_TITLE=收银台管理系统(开发)
  VITE_ENABLE_MOCK=true

  // .env.production
  VITE_API_BASE_URL=https://api.example.com
  VITE_APP_TITLE=收银台管理系统
  VITE_ENABLE_MOCK=false
  ```

  - 创建多环境配置文件
  - 实现敏感信息管理
  - 设计配置验证机制

- **资源优化构建**
  - 配置Vite生产环境构建
  ```js
  // vite.config.js
  import { defineConfig, loadEnv } from 'vite'
  import vue from '@vitejs/plugin-vue'
  import { visualizer } from 'rollup-plugin-visualizer'
  import compression from 'vite-plugin-compression'

  export default defineConfig(({ command, mode }) => {
    const env = loadEnv(mode, process.cwd())
    
    return {
      plugins: [
        vue(),
        compression({
          algorithm: 'gzip',
          ext: '.gz',
          threshold: 10240 // 10kb以上文件进行压缩
        }),
        visualizer({
          open: command === 'build',
          gzipSize: true,
          brotliSize: true
        })
      ],
      build: {
        target: 'es2015',
        outDir: 'dist',
        assetsDir: 'assets',
        minify: 'terser',
        terserOptions: {
          compress: {
            drop_console: mode === 'production',
            drop_debugger: mode === 'production'
          }
        },
        rollupOptions: {
          output: {
            manualChunks: {
              'vendor': ['vue', 'vue-router', 'pinia'],
              'element-plus': ['element-plus'],
              'chart': ['chart.js']
            }
          }
        },
        chunkSizeWarningLimit: 600
      },
      server: {
        port: 5173,
        proxy: {
          '/api': {
            target: env.VITE_API_BASE_URL,
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, '')
          }
        }
      }
    }
  })
  ```

  - 实现代码压缩优化
  - 设置资源分类打包
  - 配置资源哈希策略

- **容器化配置**
  - 设计Docker镜像构建
  ```dockerfile
  # Dockerfile
  # 构建阶段
  FROM node:16-alpine as build
  WORKDIR /app
  COPY package*.json ./
  RUN npm ci
  COPY . .
  RUN npm run build

  # 生产阶段
  FROM nginx:stable-alpine as production
  COPY --from=build /app/dist /usr/share/nginx/html
  COPY nginx.conf /etc/nginx/conf.d/default.conf
  
  # 健康检查
  HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget -q -O - http://localhost/ || exit 1
  
  # 开放端口
  EXPOSE 80

  # 启动Nginx
  CMD ["nginx", "-g", "daemon off;"]
  ```

  - 创建Nginx配置文件
  ```nginx
  # nginx.conf
  server {
    listen 80;
    server_name _;

    # 压缩设置
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    root /usr/share/nginx/html;
    index index.html;

    # 静态资源缓存
    location /assets {
      expires 1y;
      add_header Cache-Control "public, max-age=31536000, immutable";
      try_files $uri =404;
    }

    # SPA路由支持
    location / {
      try_files $uri $uri/ /index.html;
    }
  }
  ```

  - 设计Docker Compose配置
  - 实现多阶段构建优化

- **静态资源部署**
  - 配置CDN分发策略
  - 设计缓存控制策略
  - 实现资源预热机制
  - 添加资源完整性校验

### 3. CI/CD流程

- **持续集成配置**
  - 设计GitHub Actions工作流
  ```yaml
  # .github/workflows/ci.yml
  name: CI

  on:
    push:
      branches: [ main, develop ]
    pull_request:
      branches: [ main, develop ]

  jobs:
    test:
      runs-on: ubuntu-latest
      
      steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linter
        run: npm run lint
        
      - name: Run tests
        run: npm run test:unit
        
      - name: Upload test coverage
        uses: actions/upload-artifact@v3
        with:
          name: coverage
          path: coverage/
          
    build:
      needs: test
      runs-on: ubuntu-latest
      
      steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
        
      - name: Upload build
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/
  ```

  - 实现自动化测试执行
  - 设置代码质量检查
  - 配置构建缓存策略

- **持续部署配置**
  - 设计生产环境部署流程
  ```yaml
  # .github/workflows/cd.yml
  name: CD

  on:
    push:
      branches: [ main ]
      
  jobs:
    deploy:
      runs-on: ubuntu-latest
      
      steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build
        run: npm run build
        
      - name: Build Docker image
        run: |
          docker build -t mics-vue:${{ github.sha }} .
          docker tag mics-vue:${{ github.sha }} mics-vue:latest
          
      - name: Log into registry
        run: echo "${{ secrets.DOCKER_PASSWORD }}" | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        
      - name: Push image
        run: |
          docker push mics-vue:${{ github.sha }}
          docker push mics-vue:latest
          
      - name: Deploy to production
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.SSH_KEY }}
          script: |
            cd /opt/mics-vue
            docker-compose pull
            docker-compose up -d
  ```

  - 配置测试环境部署
  - 实现蓝绿部署策略
  - 设计回滚机制

- **监控与报警**
  - 配置性能监控系统
  - 设置错误跟踪服务
  - 实现系统健康检查
  - 添加关键指标报警

- **发布管理**
  - 设计版本发布流程
  - 实现变更日志自动生成
  - 配置特性标记管理
  - 设计灰度发布策略

## 交付物

1. **测试套件**
   - 单元测试代码
   - 集成测试代码
   - 端到端测试代码
   - 性能测试脚本

2. **部署配置**
   - 环境配置文件
   - 构建优化配置
   - 容器化配置文件
   - 静态资源部署方案

3. **CI/CD配置**
   - 持续集成工作流
   - 持续部署工作流
   - 监控与报警配置
   - 发布管理流程

4. **文档与报告**
   - 测试报告和覆盖率
   - 部署指南和流程
   - 性能优化报告
   - 运维手册

## 时间规划

| 任务 | 耗时 | 负责人 |
|------|------|--------|
| 测试策略规划与实施 | 3天 | 测试工程师 |
| 部署环境准备 | 2天 | DevOps工程师 |
| CI/CD流程配置 | 2天 | DevOps工程师 |
| 监控与发布管理 | 1天 | 系统运维 |

## 检查清单

- [ ] 单元测试覆盖率达到80%以上
- [ ] 集成测试覆盖所有关键功能
- [ ] 端到端测试覆盖核心用户流程
- [ ] 性能测试达到预期指标
- [ ] 构建优化配置完成并验证
- [ ] 容器化配置测试通过
- [ ] CI/CD流程配置完成并运行
- [ ] 监控与报警系统正常工作 