# 数据字典管理功能实现计划

## 项目背景

数据字典是系统中用于维护各类静态数据的模块，通常分为字典类型和字典数据两层结构。本计划文档详细说明数据字典管理功能的实施步骤、页面设计和技术实现方案。

## 功能概述

数据字典管理主要包含两个部分：
1. **字典类型管理**：维护字典的类别信息，如状态类型、性别类型等
2. **字典数据管理**：维护具体类型下的数据项，如状态类型下的"启用"、"禁用"等

## 接口信息

### 字典类型管理接口

| 接口路径 | 方法 | 描述 | 请求参数 | 响应数据 |
|---------|------|------|---------|---------|
| `/dict_type/add` | POST | 添加字典类型 | SysDictTypeDTO（字典类型信息） | Result<Void> |
| `/dict_type/modify` | PUT | 修改字典类型 | SysDictTypeDTO（ID、需修改字段） | Result<Void> |
| `/dict_type/queryList` | GET | 分页查询字典类型列表 | dictCode（字典代码）, dictName（字典名称）, status（状态）, pageNo（页码）, pageSize（每页大小） | Result<List<SysDictTypeVO>> |

### 字典数据管理接口

| 接口路径 | 方法 | 描述 | 请求参数 | 响应数据 |
|---------|------|------|---------|---------|
| `/dict_data/add` | POST | 添加字典数据 | SysDictDataDTO（字典数据信息） | Result<Void> |
| `/dict_data/modify` | PUT | 修改字典数据 | SysDictDataDTO（ID、需修改字段） | Result<Void> |
| `/dict_data/queryList` | GET | 分页查询字典数据列表 | dictCode（字典类型代码）, uniqueCode（唯一代码）, status（状态）, label（标签）, pageNo（页码）, pageSize（每页大小） | Result<List<SysDictDataVO>> |

## 数据模型

### 字典类型（SysDictTypeVO）

```typescript
interface SysDictTypeVO {
  id: number;
  dictCode: string;     // 字典代码
  dictName: string;     // 字典名称
  status: string;       // 状态：ACTIVE-启用，INACTIVE-禁用
  remark: string;       // 备注
  createTime: string;   // 创建时间
  updateTime: string;   // 更新时间
}
```

### 字典数据（SysDictDataVO）

```typescript
interface SysDictDataVO {
  id: number;
  dictCode: string;     // 所属字典类型代码
  uniqueCode: string;   // 唯一代码
  label: string;        // 标签名称
  status: string;       // 状态：ACTIVE-启用，INACTIVE-禁用
  remark: string;       // 备注
  createTime: string;   // 创建时间
  updateTime: string;   // 更新时间
}
```

## 实施步骤

### 第一阶段：字典类型管理页面开发

1. 创建字典类型列表页面
   - 实现字典类型列表展示
   - 添加搜索功能（字典代码、字典名称、状态）
   - 添加分页功能
   - 实现列表刷新功能

2. 创建字典类型新增/编辑表单
   - 设计新增表单弹窗
   - 添加表单验证规则
   - 实现表单重置功能

3. 实现字典类型删除功能
   - 添加删除确认弹窗
   - 实现批量删除功能（可选）

### 第二阶段：字典数据管理页面开发

1. 创建字典数据列表页面
   - 实现字典数据列表展示
   - 添加搜索功能（唯一代码、标签、状态）
   - 添加分页功能
   - 显示所属字典类型信息
   - 实现列表刷新功能

2. 创建字典数据新增/编辑表单
   - 设计新增表单弹窗
   - 添加表单验证规则
   - 实现表单重置功能

3. 实现字典数据删除功能
   - 添加删除确认弹窗
   - 实现批量删除功能（可选）

### 第三阶段：后端接口调用实现

1. 创建API模块
   - 创建字典类型API文件
   - 创建字典数据API文件
   - 定义请求和响应类型

2. 实现字典类型接口调用
   - 实现查询字典类型列表接口
   - 实现添加字典类型接口
   - 实现修改字典类型接口
   - 实现删除字典类型接口（如有需要）

3. 实现字典数据接口调用
   - 实现查询字典数据列表接口
   - 实现添加字典数据接口
   - 实现修改字典数据接口
   - 实现删除字典数据接口（如有需要）

4. 整合前端组件与API
   - 替换Mock数据
   - 完善错误处理
   - 添加加载状态

## 页面组件设计

### 1. 数据字典管理主页面 (DictManagement.vue)

设计模式：采用Tab页切换的方式管理字典类型和字典数据

```vue
<template>
  <div class="dict-management">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="字典类型" name="dictType">
        <dict-type-list />
      </el-tab-pane>
      <el-tab-pane label="字典数据" name="dictData">
        <dict-data-list :selected-dict-code="selectedDictCode" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

### 2. 字典类型列表组件 (DictTypeList.vue)

包含：
- 搜索过滤区
- 操作按钮区（新增、刷新）
- 表格展示区
- 新增/编辑弹窗

### 3. 字典数据列表组件 (DictDataList.vue)

包含：
- 搜索过滤区
- 操作按钮区（新增、刷新）
- 表格展示区
- 新增/编辑弹窗

### 4. 字典类型表单组件 (DictTypeForm.vue)

表单字段：
- 字典代码
- 字典名称
- 状态
- 备注

### 5. 字典数据表单组件 (DictDataForm.vue)

表单字段：
- 字典类型代码（下拉选择）
- 唯一代码
- 标签
- 状态
- 备注

## 开发计划

| 任务 | 预计工时 | 负责人 |
|------|---------|-------|
| 字典类型管理页面开发 | 2天 | 前端开发 |
| 字典数据管理页面开发 | 2天 | 前端开发 |
| API接口调用实现 | 1天 | 前端开发 |
| 联调测试与问题修复 | 1天 | 前端开发、测试 |

## 最终交付物

1. 字典类型管理页面
2. 字典数据管理页面
3. 数据字典API模块
4. 相关单元测试（可选）
5. 开发文档与使用说明

## 风险与挑战

1. 字典类型与字典数据的关联关系维护
2. 状态切换时的数据一致性
3. 界面交互体验优化
4. 大量数据时的性能优化

## 验收标准

1. 字典类型管理功能完整可用
2. 字典数据管理功能完整可用
3. 接口调用正常，数据处理正确
4. 表单验证规则完善
5. 用户界面友好，交互流畅 